-- MySQL dump 10.13  Distrib 8.0.27, for macos11 (x86_64)
--
-- Host: 127.0.0.1    Database: ops_management
-- ------------------------------------------------------
-- Server version	8.4.6

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `_prisma_migrations`
--

DROP TABLE IF EXISTS `_prisma_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_prisma_migrations` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checksum` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `finished_at` datetime(3) DEFAULT NULL,
  `migration_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `logs` text COLLATE utf8mb4_unicode_ci,
  `rolled_back_at` datetime(3) DEFAULT NULL,
  `started_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `applied_steps_count` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_prisma_migrations`
--

LOCK TABLES `_prisma_migrations` WRITE;
/*!40000 ALTER TABLE `_prisma_migrations` DISABLE KEYS */;
INSERT INTO `_prisma_migrations` VALUES ('13c019c4-5014-4240-81b5-ac0096b1b398','6e70034ac4fb43b997b62cdba09e49c8926f19de2444d4643393f9e13333166a',NULL,'20241212120000_create_workflow_templates','A migration failed to apply. New migrations cannot be applied before the error is recovered from. Read more about how to resolve migration issues in a production database: https://pris.ly/d/migrate-resolve\n\nMigration name: 20241212120000_create_workflow_templates\n\nDatabase error code: 1824\n\nDatabase error:\nFailed to open the referenced table \'users\'\n\nPlease check the query number 6 from the migration file.\n\n   0: sql_schema_connector::apply_migration::apply_script\n           with migration_name=\"20241212120000_create_workflow_templates\"\n             at schema-engine/connectors/sql-schema-connector/src/apply_migration.rs:106\n   1: schema_core::commands::apply_migrations::Applying migration\n           with migration_name=\"20241212120000_create_workflow_templates\"\n             at schema-engine/core/src/commands/apply_migrations.rs:91\n   2: schema_core::state::ApplyMigrations\n             at schema-engine/core/src/state.rs:226',NULL,'2025-08-13 00:17:24.981',0);
/*!40000 ALTER TABLE `_prisma_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_template_categories`
--

DROP TABLE IF EXISTS `workflow_template_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_template_categories` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` enum('SERVICE_AUTOMATION','SLA_MONITORING','ALERT_PROCESSING','BACKUP_AUTOMATION','MAINTENANCE','APPROVAL_PROCESS','NOTIFICATION','DATA_PROCESSING','INTEGRATION','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 0xF09F94A7,
  `subcategories` json NOT NULL,
  `display_order` int unsigned NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workflow_template_categories_category_id_unique` (`category_id`),
  KEY `workflow_template_categories_category_id_idx` (`category_id`),
  KEY `workflow_template_categories_display_order_idx` (`display_order`),
  KEY `workflow_template_categories_is_active_idx` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_template_categories`
--

LOCK TABLES `workflow_template_categories` WRITE;
/*!40000 ALTER TABLE `workflow_template_categories` DISABLE KEYS */;
/*!40000 ALTER TABLE `workflow_template_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_template_ratings`
--

DROP TABLE IF EXISTS `workflow_template_ratings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_template_ratings` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rating` int unsigned NOT NULL,
  `comment` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workflow_template_ratings_template_user_unique` (`template_id`,`user_id`),
  KEY `workflow_template_ratings_template_id_idx` (`template_id`),
  KEY `workflow_template_ratings_user_id_idx` (`user_id`),
  KEY `workflow_template_ratings_rating_idx` (`rating`),
  CONSTRAINT `workflow_template_ratings_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `workflow_templates` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_template_ratings`
--

LOCK TABLES `workflow_template_ratings` WRITE;
/*!40000 ALTER TABLE `workflow_template_ratings` DISABLE KEYS */;
/*!40000 ALTER TABLE `workflow_template_ratings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_template_usage_logs`
--

DROP TABLE IF EXISTS `workflow_template_usage_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_template_usage_logs` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `workflow_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action` enum('preview','apply','download','rate') COLLATE utf8mb4_unicode_ci NOT NULL,
  `metadata` json DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `workflow_template_usage_logs_template_id_idx` (`template_id`),
  KEY `workflow_template_usage_logs_user_id_idx` (`user_id`),
  KEY `workflow_template_usage_logs_action_idx` (`action`),
  KEY `workflow_template_usage_logs_created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_template_usage_logs`
--

LOCK TABLES `workflow_template_usage_logs` WRITE;
/*!40000 ALTER TABLE `workflow_template_usage_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `workflow_template_usage_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_templates`
--

DROP TABLE IF EXISTS `workflow_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `category` enum('SERVICE_AUTOMATION','SLA_MONITORING','ALERT_PROCESSING','BACKUP_AUTOMATION','MAINTENANCE','APPROVAL_PROCESS','NOTIFICATION','DATA_PROCESSING','INTEGRATION','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CUSTOM',
  `subcategory` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user_created',
  `tags` json DEFAULT NULL,
  `author` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0.0',
  `is_builtin` tinyint(1) NOT NULL DEFAULT '0',
  `is_popular` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `usage_count` int unsigned NOT NULL DEFAULT '0',
  `rating` decimal(2,1) NOT NULL DEFAULT '0.0',
  `difficulty` enum('beginner','intermediate','advanced') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'beginner',
  `estimated_duration` int unsigned NOT NULL DEFAULT '5',
  `preview_thumbnail` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `preview_screenshots` json DEFAULT NULL,
  `preview_demo_video` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `requirements_permissions` json DEFAULT NULL,
  `requirements_integrations` json DEFAULT NULL,
  `requirements_minimum_version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0.0',
  `workflow_definition` json NOT NULL,
  `metadata` json DEFAULT NULL,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `workflow_templates_category_idx` (`category`),
  KEY `workflow_templates_subcategory_idx` (`subcategory`),
  KEY `workflow_templates_author_idx` (`author`),
  KEY `workflow_templates_is_builtin_idx` (`is_builtin`),
  KEY `workflow_templates_is_popular_idx` (`is_popular`),
  KEY `workflow_templates_is_active_idx` (`is_active`),
  KEY `workflow_templates_difficulty_idx` (`difficulty`),
  KEY `workflow_templates_rating_idx` (`rating`),
  KEY `workflow_templates_usage_count_idx` (`usage_count`),
  KEY `workflow_templates_created_at_idx` (`created_at`),
  FULLTEXT KEY `workflow_templates_search_idx` (`name`,`description`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_templates`
--

LOCK TABLES `workflow_templates` WRITE;
/*!40000 ALTER TABLE `workflow_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `workflow_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'ops_management'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-13  9:07:32
