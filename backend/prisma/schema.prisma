generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextIndex"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                               String                      @id @default(cuid())
  username                         String                      @unique
  email                            String                      @unique
  password                         String
  fullName                         String?                     @map("full_name")
  phone                            String?
  avatar                           String?
  department                       String?
  status                           UserStatus                  @default(ACTIVE)
  lastLoginAt                      DateTime?                   @map("last_login_at")
  createdAt                        DateTime                    @default(now()) @map("created_at")
  updatedAt                        DateTime                    @updatedAt @map("updated_at")
  aiAnalysisRequests               AIAnalysisRequest[]         @relation("AIAnalysisRequest")
  aiFeedback                       AIFeedback[]                @relation("AIFeedback")
  createdPromptTemplates           AIPromptTemplate[]          @relation("AIPromptTemplateCreator")
  updatedPromptTemplates           AIPromptTemplate[]          @relation("AIPromptTemplateUpdater")
  acknowledgedAlerts               Alert[]                     @relation("AlertAcknowledgedBy")
  resolvedAlerts                   Alert[]                     @relation("AlertResolvedBy")
  createdApiKeys                   A<PERSON>ey[]                    @relation("ApiKeyCreatedBy")
  createdCommunicationIntegrations CommunicationIntegration[]  @relation("CommunicationIntegrationCreatedBy")
  updatedCommunicationIntegrations CommunicationIntegration[]  @relation("CommunicationIntegrationUpdatedBy")
  createdCustomers                 Customer[]                  @relation("CustomerCreatedBy")
  createdEmailTemplates            EmailTemplate[]             @relation("EmailTemplateCreatedBy")
  updatedEmailTemplates            EmailTemplate[]             @relation("EmailTemplateUpdatedBy")
  operationLogs                    OperationLog[]
  permissionTemplateHistory        PermissionTemplateHistory[] @relation("PermissionTemplateHistoryChangedBy")
  permissionTemplateUsage          PermissionTemplateUsage[]   @relation("PermissionTemplateUsageAppliedBy")
  createdPermissionTemplates       PermissionTemplate[]        @relation("PermissionTemplateCreatedBy")
  updatedPermissionTemplates       PermissionTemplate[]        @relation("PermissionTemplateUpdatedBy")
  createdArchives                  ProjectArchive[]            @relation("ArchiveCreatedBy")
  uploads                          ServiceAttachment[]
  comments                         ServiceComment[]
  serviceOperationHistory          ServiceOperationHistory[]
  workLogs                         ServiceWorkLog[]
  assignedServices                 Service[]                   @relation("ServiceAssignedTo")
  createdServices                  Service[]                   @relation("ServiceCreatedBy")
  systemConfigHistory              SystemConfigHistory[]
  systemConfigUpdates              SystemConfig[]
  systemEvents                     SystemEvent[]               @relation("SystemEventUser")
  resolvedAnomalies                UserActivityAnomaly[]       @relation("AnomalyResolvedBy")
  userActivityAnomalies            UserActivityAnomaly[]
  userActivityStats                UserActivityStats[]
  userBehaviorPatterns             UserBehaviorPattern[]
  userFeatureUsage                 UserFeatureUsage[]
  userRoles                        UserRole[]
  userSessions                     UserSession[]
  createdWorkflowDefinitions       WorkflowDefinition[]        @relation("WorkflowDefinitionCreatedBy")
  updatedWorkflowDefinitions       WorkflowDefinition[]        @relation("WorkflowDefinitionUpdatedBy")
  triggeredWorkflowExecutions      WorkflowExecution[]         @relation("WorkflowExecutionTriggeredBy")
  templateRatings                  WorkflowTemplateRating[]    @relation("TemplateRatedBy")
  templateUsageLogs                WorkflowTemplateUsageLog[]  @relation("TemplateUsedBy")
  createdWorkflowTemplates         WorkflowTemplate[]          @relation("TemplateCreatedBy")

  @@map("users")
}

model Role {
  id                      String                    @id @default(cuid())
  name                    String                    @unique
  description             String?
  permissions             Json
  createdAt               DateTime                  @default(now()) @map("created_at")
  updatedAt               DateTime                  @updatedAt @map("updated_at")
  permissionTemplateUsage PermissionTemplateUsage[] @relation("PermissionTemplateUsageRole")
  userRoles               UserRole[]

  @@map("roles")
}

model UserRole {
  userId     String
  roleId     String
  assignedAt DateTime @default(now()) @map("assigned_at")
  role       Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
  @@index([roleId], map: "user_roles_roleId_fkey")
  @@map("user_roles")
}

model Customer {
  id            String            @id @default(cuid())
  name          String
  code          String            @unique
  company       String?
  industry      String?
  type          CustomerType      @default(ENTERPRISE)
  level         CustomerLevel     @default(STANDARD)
  contactPerson String?           @map("contact_person")
  contactPhone  String?           @map("contact_phone")
  contactEmail  String?           @map("contact_email")
  address       String?
  description   String?           @db.Text
  isVip         Boolean           @default(false) @map("is_vip")
  createdBy     String            @map("created_by")
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")
  contacts      CustomerContact[]
  createdByUser User              @relation("CustomerCreatedBy", fields: [createdBy], references: [id])
  archives      ProjectArchive[]

  @@index([createdBy], map: "customers_created_by_fkey")
  @@map("customers")
}

model CustomerContact {
  id         String   @id @default(cuid())
  customerId String   @map("customer_id")
  name       String
  position   String?
  email      String?
  phone      String?
  isPrimary  Boolean  @default(false) @map("is_primary")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@index([customerId], map: "customer_contacts_customer_id_fkey")
  @@map("customer_contacts")
}

model ProjectArchive {
  id             String                 @id @default(cuid())
  customerId     String                 @map("customer_id")
  name           String
  description    String?                @db.Text
  technology     String?
  environment    String?
  version        String?
  deploymentDate DateTime?              @map("deployment_date")
  status         ArchiveStatus          @default(ACTIVE)
  createdBy      String                 @map("created_by")
  createdAt      DateTime               @default(now()) @map("created_at")
  updatedAt      DateTime               @updatedAt @map("updated_at")
  createdByUser  User                   @relation("ArchiveCreatedBy", fields: [createdBy], references: [id])
  customer       Customer               @relation(fields: [customerId], references: [id])
  configurations ProjectConfiguration[]
  services       Service[]

  @@index([createdBy], map: "project_archives_created_by_fkey")
  @@index([customerId], map: "project_archives_customer_id_fkey")
  @@map("project_archives")
}

model ProjectConfiguration {
  id              String         @id @default(cuid())
  archiveId       String         @map("archive_id")
  configType      ConfigType     @map("config_type")
  title           String
  configData      Json           @map("config_data")
  encryptedFields Json?          @map("encrypted_fields")
  description     String?
  isActive        Boolean        @default(true) @map("is_active")
  lastUpdated     DateTime?      @map("last_updated")
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")
  archive         ProjectArchive @relation(fields: [archiveId], references: [id], onDelete: Cascade)

  @@index([archiveId], map: "project_configurations_archive_id_fkey")
  @@map("project_configurations")
}

model SlaTemplate {
  id             String    @id @default(cuid())
  name           String
  description    String?   @db.Text
  responseTime   Int       @map("response_time")
  resolutionTime Int       @map("resolution_time")
  availability   Float     @default(99.9)
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  services       Service[]

  @@map("sla_templates")
}

model Service {
  id                   String                    @id @default(cuid())
  archiveId            String                    @map("archive_id")
  slaTemplateId        String?                   @map("sla_template_id")
  ticketNumber         String                    @unique @map("ticket_number")
  title                String
  description          String                    @db.Text
  category             ServiceCategory           @default(MAINTENANCE)
  priority             Priority                  @default(MEDIUM)
  status               ServiceStatus             @default(PENDING)
  assignedTo           String?                   @map("assigned_to")
  customerContact      String?                   @map("customer_contact")
  source               ServiceSource             @default(INTERNAL) @map("source")
  externalSystemId     String?                   @map("external_system_id")
  externalUserId       String?                   @map("external_user_id")
  externalAccount      String?                   @map("external_account")
  startTime            DateTime?                 @map("start_time")
  endTime              DateTime?                 @map("end_time")
  actualResponseTime   Int?                      @map("actual_response_time")
  actualResolutionTime Int?                      @map("actual_resolution_time")
  estimatedHours       Float?                    @map("estimated_hours")
  actualHours          Float?                    @map("actual_hours")
  resolution           String?                   @db.Text
  customerFeedback     String?                   @map("customer_feedback") @db.Text
  satisfaction         Int?
  tags                 Json?
  firstResponseAt      DateTime?                 @map("first_response_at")
  createdBy            String                    @map("created_by")
  createdAt            DateTime                  @default(now()) @map("created_at")
  updatedAt            DateTime                  @updatedAt @map("updated_at")
  attachments          ServiceAttachment[]
  comments             ServiceComment[]
  operationHistory     ServiceOperationHistory[]
  workLogs             ServiceWorkLog[]
  archive              ProjectArchive            @relation(fields: [archiveId], references: [id])
  assignedUser         User?                     @relation("ServiceAssignedTo", fields: [assignedTo], references: [id])
  createdByUser        User                      @relation("ServiceCreatedBy", fields: [createdBy], references: [id])
  slaTemplate          SlaTemplate?              @relation(fields: [slaTemplateId], references: [id])

  @@index([archiveId], map: "services_archive_id_fkey")
  @@index([assignedTo], map: "services_assigned_to_fkey")
  @@index([createdBy], map: "services_created_by_fkey")
  @@index([slaTemplateId], map: "services_sla_template_id_fkey")
  @@map("services")
}

model ServiceWorkLog {
  id          String       @id @default(cuid())
  serviceId   String       @map("service_id")
  userId      String       @map("user_id")
  description String       @db.Text
  workHours   Float        @map("work_hours")
  workDate    DateTime     @map("work_date")
  category    WorkCategory @default(MAINTENANCE)
  createdAt   DateTime     @default(now()) @map("created_at")
  service     Service      @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  user        User         @relation(fields: [userId], references: [id])

  @@index([serviceId], map: "service_work_logs_service_id_fkey")
  @@index([userId], map: "service_work_logs_user_id_fkey")
  @@map("service_work_logs")
}

model ServiceAttachment {
  id           String   @id @default(cuid())
  serviceId    String   @map("service_id")
  filename     String
  originalName String   @map("original_name")
  filePath     String   @map("file_path")
  fileSize     Int      @map("file_size")
  mimeType     String   @map("mime_type")
  uploadedBy   String   @map("uploaded_by")
  uploadedAt   DateTime @default(now()) @map("uploaded_at")
  service      Service  @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  uploader     User     @relation(fields: [uploadedBy], references: [id])

  @@index([serviceId], map: "service_attachments_service_id_fkey")
  @@index([uploadedBy], map: "service_attachments_uploaded_by_fkey")
  @@map("service_attachments")
}

model ServiceComment {
  id         String   @id @default(cuid())
  serviceId  String   @map("service_id")
  content    String   @db.Text
  isInternal Boolean  @default(false) @map("is_internal")
  authorId   String   @map("author_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  author     User     @relation(fields: [authorId], references: [id])
  service    Service  @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@index([authorId], map: "service_comments_author_id_fkey")
  @@index([serviceId], map: "service_comments_service_id_fkey")
  @@map("service_comments")
}

model NotificationTemplate {
  id            String           @id @default(cuid())
  name          String
  type          NotificationType
  subject       String
  content       String           @db.Text
  variables     Json?
  createdAt     DateTime         @default(now()) @map("created_at")
  updatedAt     DateTime         @updatedAt @map("updated_at")
  notifications Notification[]

  @@map("notification_templates")
}

model Notification {
  id         String                @id @default(cuid())
  templateId String?               @map("template_id")
  recipient  String
  type       NotificationType
  subject    String
  content    String                @db.Text
  status     NotificationStatus    @default(PENDING)
  sentAt     DateTime?             @map("sent_at")
  createdAt  DateTime              @default(now()) @map("created_at")
  template   NotificationTemplate? @relation(fields: [templateId], references: [id])

  @@index([templateId], map: "notifications_template_id_fkey")
  @@map("notifications")
}

model ServiceOperationHistory {
  id          String               @id @default(cuid())
  serviceId   String               @map("service_id")
  type        ServiceOperationType
  description String
  fromValue   String?              @map("from_value")
  toValue     String?              @map("to_value")
  note        String?              @db.Text
  userId      String               @map("user_id")
  createdAt   DateTime             @default(now()) @map("created_at")
  service     Service              @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  user        User                 @relation(fields: [userId], references: [id])

  @@index([serviceId], map: "service_operation_history_service_id_fkey")
  @@index([userId], map: "service_operation_history_user_id_fkey")
  @@map("service_operation_history")
}

model OperationLog {
  id         String   @id @default(cuid())
  userId     String   @map("user_id")
  action     String
  resource   String
  resourceId String?  @map("resource_id")
  details    Json?
  ipAddress  String?  @map("ip_address")
  userAgent  String?  @map("user_agent")
  createdAt  DateTime @default(now()) @map("created_at")
  user       User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "operation_logs_user_id_fkey")
  @@map("operation_logs")
}

model ApiKey {
  id            String       @id @default(cuid())
  name          String
  keyValue      String       @unique @map("key_value")
  systemId      String       @map("system_id")
  description   String?
  status        ApiKeyStatus @default(ACTIVE)
  expiresAt     DateTime?    @map("expires_at")
  lastUsedAt    DateTime?    @map("last_used_at")
  usageCount    Int          @default(0) @map("usage_count")
  createdBy     String       @map("created_by")
  createdAt     DateTime     @default(now()) @map("created_at")
  updatedAt     DateTime     @updatedAt @map("updated_at")
  createdByUser User         @relation("ApiKeyCreatedBy", fields: [createdBy], references: [id])

  @@index([createdBy], map: "api_keys_created_by_fkey")
  @@map("api_keys")
}

model AlertRule {
  id                   String          @id @default(cuid())
  name                 String          @unique
  description          String?         @db.Text
  metricType           AlertMetricType @map("metric_type")
  condition            AlertCondition
  threshold            Float
  duration             Int
  severity             AlertSeverity
  enabled              Boolean         @default(true)
  notificationChannels Json            @map("notification_channels")
  createdAt            DateTime        @default(now()) @map("created_at")
  updatedAt            DateTime        @updatedAt @map("updated_at")
  alerts               Alert[]

  @@map("alert_rules")
}

model Alert {
  id                 String        @id @default(cuid())
  ruleId             String        @map("rule_id")
  severity           AlertSeverity
  status             AlertStatus   @default(PENDING)
  message            String        @db.Text
  metricValue        Float?        @map("metric_value")
  triggeredAt        DateTime      @default(now()) @map("triggered_at")
  acknowledgedAt     DateTime?     @map("acknowledged_at")
  acknowledgedBy     String?       @map("acknowledged_by")
  resolvedAt         DateTime?     @map("resolved_at")
  resolvedBy         String?       @map("resolved_by")
  notificationsSent  Json?         @map("notifications_sent")
  acknowledgedByUser User?         @relation("AlertAcknowledgedBy", fields: [acknowledgedBy], references: [id])
  resolvedByUser     User?         @relation("AlertResolvedBy", fields: [resolvedBy], references: [id])
  rule               AlertRule     @relation(fields: [ruleId], references: [id], onDelete: Cascade)

  @@index([acknowledgedBy], map: "alerts_acknowledged_by_fkey")
  @@index([resolvedBy], map: "alerts_resolved_by_fkey")
  @@index([ruleId], map: "alerts_rule_id_fkey")
  @@map("alerts")
}

model SystemConfig {
  id             String                @id @default(cuid())
  category       SystemConfigCategory
  key            String                @unique
  value          Json
  description    String?
  dataType       SystemConfigDataType  @default(STRING) @map("data_type")
  isEncrypted    Boolean               @default(false) @map("is_encrypted")
  isSystem       Boolean               @default(false) @map("is_system")
  isPublic       Boolean               @default(false) @map("is_public")
  validationRule Json?                 @map("validation_rule")
  defaultValue   Json?                 @map("default_value")
  displayOrder   Int?                  @map("display_order")
  updatedBy      String?               @map("updated_by")
  createdAt      DateTime              @default(now()) @map("created_at")
  updatedAt      DateTime              @updatedAt @map("updated_at")
  configHistory  SystemConfigHistory[]
  updatedByUser  User?                 @relation(fields: [updatedBy], references: [id])

  @@index([updatedBy], map: "system_configs_updated_by_fkey")
  @@map("system_configs")
}

model SystemConfigHistory {
  id            String       @id @default(cuid())
  configId      String       @map("config_id")
  oldValue      Json?        @map("old_value")
  newValue      Json         @map("new_value")
  changeReason  String?      @map("change_reason")
  changedBy     String       @map("changed_by")
  createdAt     DateTime     @default(now()) @map("created_at")
  changedByUser User         @relation(fields: [changedBy], references: [id])
  config        SystemConfig @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@index([changedBy], map: "system_config_history_changed_by_fkey")
  @@index([configId], map: "system_config_history_config_id_fkey")
  @@map("system_config_history")
}

model PermissionTemplate {
  id              String                      @id @default(cuid())
  name            String                      @unique
  description     String?                     @db.Text
  permissions     Json
  category        PermissionTemplateCategory  @default(CUSTOM)
  isDefault       Boolean                     @default(false) @map("is_default")
  isSystem        Boolean                     @default(false) @map("is_system")
  version         String                      @default("1.0")
  metadata        Json?
  createdBy       String                      @map("created_by")
  updatedBy       String?                     @map("updated_by")
  createdAt       DateTime                    @default(now()) @map("created_at")
  updatedAt       DateTime                    @updatedAt @map("updated_at")
  templateHistory PermissionTemplateHistory[]
  templateUsage   PermissionTemplateUsage[]
  createdByUser   User                        @relation("PermissionTemplateCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User?                       @relation("PermissionTemplateUpdatedBy", fields: [updatedBy], references: [id])

  @@index([createdBy], map: "permission_templates_created_by_fkey")
  @@index([updatedBy], map: "permission_templates_updated_by_fkey")
  @@map("permission_templates")
}

model PermissionTemplateHistory {
  id            String                   @id @default(cuid())
  templateId    String                   @map("template_id")
  action        PermissionTemplateAction
  oldData       Json?                    @map("old_data")
  newData       Json?                    @map("new_data")
  changeReason  String?                  @map("change_reason")
  changedBy     String                   @map("changed_by")
  createdAt     DateTime                 @default(now()) @map("created_at")
  changedByUser User                     @relation("PermissionTemplateHistoryChangedBy", fields: [changedBy], references: [id])
  template      PermissionTemplate       @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@index([changedBy], map: "permission_template_history_changed_by_fkey")
  @@index([templateId], map: "permission_template_history_template_id_fkey")
  @@map("permission_template_history")
}

model PermissionTemplateUsage {
  id            String                        @id @default(cuid())
  templateId    String                        @map("template_id")
  roleId        String                        @map("role_id")
  appliedBy     String                        @map("applied_by")
  appliedAt     DateTime                      @default(now()) @map("applied_at")
  status        PermissionTemplateUsageStatus @default(ACTIVE)
  note          String?                       @db.Text
  appliedByUser User                          @relation("PermissionTemplateUsageAppliedBy", fields: [appliedBy], references: [id])
  role          Role                          @relation("PermissionTemplateUsageRole", fields: [roleId], references: [id], onDelete: Cascade)
  template      PermissionTemplate            @relation(fields: [templateId], references: [id], onDelete: Cascade)

  @@unique([templateId, roleId])
  @@index([appliedBy], map: "permission_template_usage_applied_by_fkey")
  @@index([roleId], map: "permission_template_usage_role_id_fkey")
  @@map("permission_template_usage")
}

model SystemEvent {
  id             String    @id @default(cuid())
  level          String
  type           String
  message        String    @db.Text
  details        Json?
  source         String?
  resolved       Boolean   @default(false)
  resolvedBy     String?   @map("resolved_by")
  createdAt      DateTime  @default(now()) @map("created_at")
  resolvedAt     DateTime? @map("resolved_at")
  resolvedByUser User?     @relation("SystemEventUser", fields: [resolvedBy], references: [id])

  @@index([level, type])
  @@index([createdAt])
  @@index([resolvedBy], map: "system_events_resolved_by_fkey")
  @@map("system_events")
}

model SystemMetrics {
  id         String   @id @default(cuid())
  timestamp  DateTime @default(now())
  metricType String   @map("metric_type")
  value      Float
  unit       String
  tags       Json?

  @@index([metricType, timestamp])
  @@index([timestamp])
  @@map("system_metrics")
}

model PerformanceBenchmark {
  id          String   @id @default(cuid())
  category    String
  metric      String
  baseline    Float
  target      Float
  description String?  @db.Text
  enabled     Boolean  @default(true)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([category, metric])
  @@map("performance_benchmarks")
}

model MonitoringConfig {
  id          String   @id @default(cuid())
  category    String
  key         String
  value       Json
  description String?  @db.Text
  enabled     Boolean  @default(true)
  updatedBy   String?  @map("updated_by")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([category, key])
  @@map("monitoring_config")
}

model AlertThreshold {
  id          String   @id @default(cuid())
  metricType  String   @map("metric_type")
  name        String
  warning     Float
  critical    Float
  duration    Int
  enabled     Boolean  @default(true)
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@unique([metricType, name])
  @@map("alert_thresholds")
}

model ServiceAvailability {
  id          String   @id @default(cuid())
  serviceName String   @map("service_name")
  date        DateTime @db.Date
  uptime      Float
  downtime    Float
  incidents   Int      @default(0)
  mttr        Float?

  @@unique([serviceName, date])
  @@index([serviceName, date])
  @@map("service_availability")
}

model IntelligentAnalysisResult {
  id           String    @id @default(cuid())
  analysisType String    @map("analysis_type")
  result       Json
  confidence   Float
  status       String    @default("active")
  validUntil   DateTime? @map("valid_until")
  createdAt    DateTime  @default(now()) @map("created_at")

  @@index([analysisType, status])
  @@index([createdAt])
  @@map("intelligent_analysis_results")
}

model UserSession {
  id              String    @id @default(cuid())
  userId          String    @map("user_id")
  sessionToken    String    @unique @map("session_token")
  ipAddress       String?   @map("ip_address") @db.VarChar(45)
  userAgent       String?   @map("user_agent") @db.Text
  location        Json?
  deviceInfo      Json?     @map("device_info")
  isActive        Boolean   @default(true) @map("is_active")
  lastActivity    DateTime  @default(now()) @map("last_activity")
  loginTime       DateTime  @default(now()) @map("login_time")
  logoutTime      DateTime? @map("logout_time")
  durationMinutes Int?      @map("duration_minutes")
  activityScore   Float?    @default(0) @map("activity_score")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([isActive])
  @@index([lastActivity])
  @@map("user_sessions")
}

model UserActivityStats {
  id                   String    @id @default(cuid())
  userId               String    @map("user_id")
  date                 DateTime  @db.Date
  loginCount           Int       @default(0) @map("login_count")
  sessionCount         Int       @default(0) @map("session_count")
  totalDurationMinutes Int       @default(0) @map("total_duration_minutes")
  operationCount       Int       @default(0) @map("operation_count")
  pageViews            Int       @default(0) @map("page_views")
  activityScore        Float     @default(0) @map("activity_score")
  peakActivityHour     Int?      @map("peak_activity_hour")
  featuresUsed         Json?     @map("features_used")
  lastActivity         DateTime? @map("last_activity")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")
  user                 User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([date])
  @@index([activityScore])
  @@map("user_activity_stats")
}

model UserBehaviorPattern {
  id              String                    @id @default(cuid())
  userId          String                    @map("user_id")
  patternType     UserBehaviorPatternType   @map("pattern_type")
  patternData     Json                      @map("pattern_data")
  confidenceScore Float                     @default(0) @map("confidence_score")
  detectedAt      DateTime                  @default(now()) @map("detected_at")
  lastUpdated     DateTime                  @default(now()) @updatedAt @map("last_updated")
  status          UserBehaviorPatternStatus @default(ACTIVE)
  metadata        Json?
  user            User                      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, patternType])
  @@index([patternType])
  @@index([status])
  @@map("user_behavior_patterns")
}

model UserActivityAnomaly {
  id              String                      @id @default(cuid())
  userId          String                      @map("user_id")
  anomalyType     UserActivityAnomalyType     @map("anomaly_type")
  description     String                      @db.Text
  severity        UserActivityAnomalySeverity @default(MEDIUM)
  detectedValue   Json                        @map("detected_value")
  normalRange     Json?                       @map("normal_range")
  confidenceScore Float                       @default(0) @map("confidence_score")
  isResolved      Boolean                     @default(false) @map("is_resolved")
  resolvedAt      DateTime?                   @map("resolved_at")
  resolvedBy      String?                     @map("resolved_by")
  notes           String?                     @db.Text
  detectedAt      DateTime                    @default(now()) @map("detected_at")
  resolvedByUser  User?                       @relation("AnomalyResolvedBy", fields: [resolvedBy], references: [id])
  user            User                        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([anomalyType])
  @@index([severity])
  @@index([isResolved])
  @@index([resolvedBy], map: "user_activity_anomalies_resolved_by_fkey")
  @@map("user_activity_anomalies")
}

model UserFeatureUsage {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  featureName  String   @map("feature_name")
  usageCount   Int      @default(0) @map("usage_count")
  lastUsed     DateTime @default(now()) @map("last_used")
  date         DateTime @db.Date
  avgTimeSpent Float?   @default(0) @map("avg_time_spent")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, featureName, date])
  @@index([featureName])
  @@index([date])
  @@map("user_feature_usage")
}

model EmailTemplate {
  id            String                @id @default(cuid())
  name          String
  type          EmailTemplateType
  category      EmailTemplateCategory @default(SYSTEM)
  subject       String
  content       String                @db.LongText
  description   String?               @db.Text
  variables     Json?
  enabled       Boolean               @default(true)
  isSystem      Boolean               @default(false) @map("is_system")
  version       String                @default("1.0")
  metadata      Json?
  createdBy     String                @map("created_by")
  updatedBy     String?               @map("updated_by")
  createdAt     DateTime              @default(now()) @map("created_at")
  updatedAt     DateTime              @updatedAt @map("updated_at")
  emailLogs     EmailLog[]
  createdByUser User                  @relation("EmailTemplateCreatedBy", fields: [createdBy], references: [id])
  updatedByUser User?                 @relation("EmailTemplateUpdatedBy", fields: [updatedBy], references: [id])

  @@unique([name, type])
  @@index([createdBy], map: "email_templates_created_by_fkey")
  @@index([updatedBy], map: "email_templates_updated_by_fkey")
  @@map("email_templates")
}

model EmailLog {
  id           String         @id @default(cuid())
  templateId   String?        @map("template_id")
  recipient    String
  subject      String
  content      String         @db.LongText
  status       EmailLogStatus @default(PENDING)
  errorMessage String?        @map("error_message") @db.Text
  sentAt       DateTime?      @map("sent_at")
  deliveredAt  DateTime?      @map("delivered_at")
  openedAt     DateTime?      @map("opened_at")
  metadata     Json?
  retryCount   Int            @default(0) @map("retry_count")
  createdAt    DateTime       @default(now()) @map("created_at")
  updatedAt    DateTime       @updatedAt @map("updated_at")
  template     EmailTemplate? @relation(fields: [templateId], references: [id])

  @@index([recipient])
  @@index([status])
  @@index([createdAt])
  @@index([templateId], map: "email_logs_template_id_fkey")
  @@map("email_logs")
}

model TaskExecution {
  id        String     @id @default(cuid())
  taskName  String     @map("task_name")
  status    TaskStatus @default(RUNNING)
  startedAt DateTime   @default(now()) @map("started_at")
  endedAt   DateTime?  @map("ended_at")
  duration  Int?
  output    String?    @db.Text
  error     String?    @db.Text
  createdAt DateTime   @default(now()) @map("created_at")
  updatedAt DateTime   @updatedAt @map("updated_at")

  @@index([taskName])
  @@index([status])
  @@index([createdAt])
  @@map("task_executions")
}

model AIConfiguration {
  id                String   @id @default(cuid())
  provider          String
  model             String
  apiKey            String?  @map("api_key")
  temperature       Float    @default(0.3)
  maxTokens         Int      @default(1000) @map("max_tokens")
  timeout           Int      @default(30000)
  mode              String   @default("USER_TRIGGERED")
  enabledFields     Json     @map("enabled_fields")
  autoFillThreshold Float    @default(0.8) @map("auto_fill_threshold")
  isActive          Boolean  @default(true) @map("is_active")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@map("ai_configurations")
}

model AIAnalysisRequest {
  id             String       @id @default(cuid())
  requestId      String       @unique @map("request_id")
  userId         String       @map("user_id")
  description    String       @db.Text
  contextData    Json?        @map("context_data")
  provider       String
  model          String
  prompt         String       @db.Text
  response       Json?
  suggestions    Json?
  processingTime Int?         @map("processing_time")
  success        Boolean      @default(false)
  error          String?
  createdAt      DateTime     @default(now()) @map("created_at")
  user           User         @relation("AIAnalysisRequest", fields: [userId], references: [id])
  feedback       AIFeedback[]

  @@index([userId], map: "ai_analysis_requests_user_id_fkey")
  @@map("ai_analysis_requests")
}

model AIFeedback {
  id              String            @id @default(cuid())
  requestId       String            @map("request_id")
  userId          String            @map("user_id")
  rating          Int
  helpful         Boolean?
  adopted         Json?
  comments        String?
  createdAt       DateTime          @default(now()) @map("created_at")
  analysisRequest AIAnalysisRequest @relation(fields: [requestId], references: [requestId])
  user            User              @relation("AIFeedback", fields: [userId], references: [id])

  @@index([requestId], map: "ai_feedback_request_id_fkey")
  @@index([userId], map: "ai_feedback_user_id_fkey")
  @@map("ai_feedback")
}

model AIPromptTemplate {
  id        String   @id @default(cuid())
  name      String   @unique
  category  String
  template  String   @db.Text
  variables Json?
  provider  String?
  version   String   @default("1.0")
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  createdBy String   @map("created_by")
  updatedBy String?  @map("updated_by")
  creator   User     @relation("AIPromptTemplateCreator", fields: [createdBy], references: [id])
  updater   User?    @relation("AIPromptTemplateUpdater", fields: [updatedBy], references: [id])

  @@index([createdBy], map: "ai_prompt_templates_created_by_fkey")
  @@index([updatedBy], map: "ai_prompt_templates_updated_by_fkey")
  @@map("ai_prompt_templates")
}

model WorkflowDefinition {
  id                String                     @id @default(cuid())
  name              String                     @unique
  description       String?                    @db.Text
  category          WorkflowCategory           @default(SERVICE_AUTOMATION)
  version           String                     @default("1.0")
  isActive          Boolean                    @default(true) @map("is_active")
  isTemplate        Boolean                    @default(false) @map("is_template")
  priority          WorkflowPriority           @default(MEDIUM)
  triggerConfig     Json                       @map("trigger_config")
  stepsConfig       Json                       @map("steps_config")
  conditions        Json?
  variables         Json?
  settings          Json?
  tags              Json?
  metadata          Json?
  executionCount    Int                        @default(0) @map("execution_count")
  successCount      Int                        @default(0) @map("success_count")
  failureCount      Int                        @default(0) @map("failure_count")
  lastExecutedAt    DateTime?                  @map("last_executed_at")
  avgExecutionTime  Float?                     @map("avg_execution_time")
  createdBy         String                     @map("created_by")
  updatedBy         String?                    @map("updated_by")
  createdAt         DateTime                   @default(now()) @map("created_at")
  updatedAt         DateTime                   @updatedAt @map("updated_at")
  createdByUser     User                       @relation("WorkflowDefinitionCreatedBy", fields: [createdBy], references: [id])
  updatedByUser     User?                      @relation("WorkflowDefinitionUpdatedBy", fields: [updatedBy], references: [id])
  executions        WorkflowExecution[]
  templateUsageLogs WorkflowTemplateUsageLog[] @relation("TemplateAppliedToWorkflow")

  @@index([createdBy], map: "workflow_definitions_created_by_fkey")
  @@index([updatedBy], map: "workflow_definitions_updated_by_fkey")
  @@map("workflow_definitions")
}

model WorkflowExecution {
  id                 String                  @id @default(cuid())
  workflowId         String                  @map("workflow_id")
  executionId        String                  @unique @map("execution_id")
  status             WorkflowExecutionStatus @default(PENDING)
  priority           WorkflowPriority        @default(MEDIUM)
  triggerType        WorkflowTriggerType     @map("trigger_type")
  triggerData        Json?                   @map("trigger_data")
  triggeredBy        String?                 @map("triggered_by")
  currentStep        Int                     @default(0) @map("current_step")
  totalSteps         Int                     @map("total_steps")
  executionData      Json?                   @map("execution_data")
  variables          Json?
  startedAt          DateTime?               @map("started_at")
  completedAt        DateTime?               @map("completed_at")
  scheduledAt        DateTime?               @map("scheduled_at")
  timeoutAt          DateTime?               @map("timeout_at")
  result             Json?
  error              String?                 @db.Text
  logs               Json?
  executionTime      Float?                  @map("execution_time")
  resourceUsage      Json?                   @map("resource_usage")
  createdAt          DateTime                @default(now()) @map("created_at")
  updatedAt          DateTime                @updatedAt @map("updated_at")
  steps              WorkflowExecutionStep[]
  triggeredByUser    User?                   @relation("WorkflowExecutionTriggeredBy", fields: [triggeredBy], references: [id])
  workflowDefinition WorkflowDefinition      @relation(fields: [workflowId], references: [id])

  @@index([workflowId])
  @@index([status])
  @@index([triggerType])
  @@index([createdAt])
  @@index([triggeredBy], map: "workflow_executions_triggered_by_fkey")
  @@map("workflow_executions")
}

model WorkflowExecutionStep {
  id            String             @id @default(cuid())
  executionId   String             @map("execution_id")
  stepIndex     Int                @map("step_index")
  stepName      String             @map("step_name")
  stepType      WorkflowStepType   @map("step_type")
  status        WorkflowStepStatus @default(PENDING)
  config        Json
  input         Json?
  output        Json?
  startedAt     DateTime?          @map("started_at")
  completedAt   DateTime?          @map("completed_at")
  executionTime Float?             @map("execution_time")
  result        Json?
  error         String?            @db.Text
  logs          Json?
  retryCount    Int                @default(0) @map("retry_count")
  createdAt     DateTime           @default(now()) @map("created_at")
  updatedAt     DateTime           @updatedAt @map("updated_at")
  execution     WorkflowExecution  @relation(fields: [executionId], references: [id], onDelete: Cascade)

  @@unique([executionId, stepIndex])
  @@index([executionId])
  @@index([status])
  @@map("workflow_execution_steps")
}

model WorkflowTemplate {
  id                         String                     @id @default(cuid())
  name                       String                     @db.VarChar(255)
  description                String?                    @db.Text
  category                   WorkflowCategory           @default(CUSTOM)
  subcategory                String                     @default("user_created") @db.VarChar(100)
  tags                       Json?
  author                     String                     @db.VarChar(100)
  version                    String                     @default("1.0.0") @db.VarChar(20)
  isBuiltin                  Boolean                    @default(false) @map("is_builtin")
  isPopular                  Boolean                    @default(false) @map("is_popular")
  isActive                   Boolean                    @default(true) @map("is_active")
  usageCount                 Int                        @default(0) @map("usage_count") @db.UnsignedInt
  rating                     Decimal                    @default(0.0) @db.Decimal(2, 1)
  difficulty                 WorkflowTemplateDifficulty @default(beginner)
  estimatedDuration          Int                        @default(5) @map("estimated_duration") @db.UnsignedInt
  previewThumbnail           String?                    @map("preview_thumbnail") @db.VarChar(500)
  previewScreenshots         Json?                      @map("preview_screenshots")
  previewDemoVideo           String?                    @map("preview_demo_video") @db.VarChar(500)
  requirementsPermissions    Json?                      @map("requirements_permissions")
  requirementsIntegrations   Json?                      @map("requirements_integrations")
  requirementsMinimumVersion String                     @default("1.0.0") @map("requirements_minimum_version") @db.VarChar(20)
  workflowDefinition         Json                       @map("workflow_definition")
  metadata                   Json?
  createdBy                  String?                    @map("created_by")
  createdAt                  DateTime                   @default(now()) @map("created_at")
  updatedAt                  DateTime                   @updatedAt @map("updated_at")
  ratings                    WorkflowTemplateRating[]
  usageLogs                  WorkflowTemplateUsageLog[]
  creator                    User?                      @relation("TemplateCreatedBy", fields: [createdBy], references: [id])

  @@index([category])
  @@index([subcategory])
  @@index([author])
  @@index([isBuiltin])
  @@index([isPopular])
  @@index([isActive])
  @@index([difficulty])
  @@index([rating])
  @@index([usageCount])
  @@index([createdAt])
  @@index([createdBy], map: "workflow_templates_created_by_fkey")
  @@fulltext([name, description])
  @@map("workflow_templates")
}

model WorkflowTemplateRating {
  id         String           @id @default(cuid())
  templateId String           @map("template_id")
  userId     String           @map("user_id")
  rating     Int              @db.UnsignedInt
  comment    String?          @db.Text
  createdAt  DateTime         @default(now()) @map("created_at")
  updatedAt  DateTime         @updatedAt @map("updated_at")
  template   WorkflowTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user       User             @relation("TemplateRatedBy", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateId, userId])
  @@index([templateId])
  @@index([userId])
  @@index([rating])
  @@map("workflow_template_ratings")
}

model WorkflowTemplateUsageLog {
  id         String                      @id @default(cuid())
  templateId String                      @map("template_id")
  userId     String                      @map("user_id")
  workflowId String?                     @map("workflow_id")
  action     WorkflowTemplateUsageAction
  metadata   Json?
  createdAt  DateTime                    @default(now()) @map("created_at")
  template   WorkflowTemplate            @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user       User                        @relation("TemplateUsedBy", fields: [userId], references: [id], onDelete: Cascade)
  workflow   WorkflowDefinition?         @relation("TemplateAppliedToWorkflow", fields: [workflowId], references: [id])

  @@index([templateId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@index([workflowId], map: "workflow_template_usage_logs_workflow_id_fkey")
  @@map("workflow_template_usage_logs")
}

model WorkflowTemplateCategory {
  id            String           @id @default(cuid())
  categoryId    WorkflowCategory @unique @map("category_id")
  name          String           @db.VarChar(100)
  description   String?          @db.Text
  icon          String           @default("?") @db.VarChar(50)
  subcategories Json
  displayOrder  Int              @default(0) @map("display_order") @db.UnsignedInt
  isActive      Boolean          @default(true) @map("is_active")
  createdAt     DateTime         @default(now()) @map("created_at")
  updatedAt     DateTime         @updatedAt @map("updated_at")

  @@index([categoryId])
  @@index([displayOrder])
  @@index([isActive])
  @@map("workflow_template_categories")
}

model CommunicationIntegration {
  id               String                              @id @default(cuid())
  name             String
  type             String
  description      String?                             @db.Text
  enabled          Boolean                             @default(true)
  priority         Int                                 @default(5)
  status           String                              @default("active")
  config           Json
  lastHealthCheck  DateTime?                           @map("last_health_check")
  healthStatus     String?                             @map("health_status")
  lastErrorAt      DateTime?                           @map("last_error_at")
  lastErrorMessage String?                             @map("last_error_message") @db.Text
  createdBy        String                              @map("created_by")
  updatedBy        String?                             @map("updated_by")
  createdAt        DateTime                            @default(now()) @map("created_at")
  updatedAt        DateTime                            @updatedAt @map("updated_at")
  executions       CommunicationIntegrationExecution[]
  createdByUser    User                                @relation("CommunicationIntegrationCreatedBy", fields: [createdBy], references: [id])
  updatedByUser    User?                               @relation("CommunicationIntegrationUpdatedBy", fields: [updatedBy], references: [id])

  @@index([createdBy], map: "communication_integrations_created_by_fkey")
  @@index([updatedBy], map: "communication_integrations_updated_by_fkey")
  @@map("communication_integrations")
}

model CommunicationIntegrationExecution {
  id            String                   @id @default(cuid())
  integrationId String                   @map("integration_id")
  operation     String
  status        String
  requestData   Json?                    @map("request_data")
  responseData  Json?                    @map("response_data")
  errorMessage  String?                  @map("error_message") @db.Text
  duration      Int?
  startedAt     DateTime                 @default(now()) @map("started_at")
  completedAt   DateTime?                @map("completed_at")
  integration   CommunicationIntegration @relation(fields: [integrationId], references: [id], onDelete: Cascade)

  @@index([integrationId], map: "communication_integration_executions_integration_id_fkey")
  @@map("communication_integration_executions")
}

model CommunicationIntegrationMetrics {
  id              String   @id @default(cuid())
  integrationId   String   @map("integration_id")
  timestamp       DateTime
  period          String
  totalRequests   Int      @default(0) @map("total_requests")
  successRequests Int      @default(0) @map("success_requests")
  failedRequests  Int      @default(0) @map("failed_requests")
  avgResponseTime Float    @default(0) @map("avg_response_time")
  maxResponseTime Float    @default(0) @map("max_response_time")
  minResponseTime Float    @default(0) @map("min_response_time")
  errorCount      Int      @default(0) @map("error_count")
  errorTypes      Json?    @map("error_types")
  availability    Float    @default(0)

  @@unique([integrationId, timestamp, period])
  @@map("communication_integration_metrics")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum CustomerType {
  ENTERPRISE
  INDIVIDUAL
  GOVERNMENT
  NONPROFIT
}

enum CustomerLevel {
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum ArchiveStatus {
  ACTIVE
  MAINTENANCE
  DEPRECATED
  ARCHIVED
}

enum ServiceCategory {
  MAINTENANCE
  SUPPORT
  UPGRADE
  BUGFIX
  CONSULTING
  MONITORING
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ConfigType {
  SERVER
  DATABASE
  VPN
  ACCOUNT
  ENVIRONMENT
  OTHER
}

enum ServiceStatus {
  PENDING
  IN_PROGRESS
  WAITING_CUSTOMER
  RESOLVED
  CLOSED
  OPEN
}

enum NotificationType {
  EMAIL
  SMS
  SYSTEM
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
  CANCELLED
}

enum WorkCategory {
  ANALYSIS
  IMPLEMENTATION
  TESTING
  DOCUMENTATION
  COMMUNICATION
  MAINTENANCE
  SUPPORT
  OTHER
}

enum ServiceOperationType {
  CREATE
  UPDATE
  STATUS_CHANGE
  TRANSFER
  ASSIGNMENT
  COMMENT
  ATTACHMENT
  WORK_LOG
}

enum ServiceSource {
  INTERNAL
  EXTERNAL
}

enum ApiKeyStatus {
  ACTIVE
  INACTIVE
  REVOKED
  EXPIRED
}

enum AlertMetricType {
  CPU
  MEMORY
  DISK
  NETWORK
  SERVICE
  DATABASE
  REDIS
}

enum AlertCondition {
  GT
  LT
  GTE
  LTE
  EQ
  NEQ
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  PENDING
  ACKNOWLEDGED
  RESOLVED
  CANCELLED
}

enum SystemConfigCategory {
  GENERAL
  SECURITY
  EMAIL
  SMS
  NOTIFICATION
  STORAGE
  BACKUP
  SYSTEM
  INTEGRATION
  CUSTOM
}

enum SystemConfigDataType {
  STRING
  NUMBER
  BOOLEAN
  JSON
  EMAIL
  URL
  PASSWORD
  TEXTAREA
  SELECT
  MULTI_SELECT
}

enum PermissionTemplateCategory {
  SYSTEM
  BUSINESS
  SERVICE
  READONLY
  CUSTOM
}

enum PermissionTemplateAction {
  CREATE
  UPDATE
  DELETE
  APPLY
  EXPORT
  IMPORT
  COPY
}

enum PermissionTemplateUsageStatus {
  ACTIVE
  INACTIVE
  SUPERSEDED
}

enum UserBehaviorPatternType {
  LOGIN_TIME
  ACTIVITY_DURATION
  FEATURE_USAGE
  OPERATION_FREQUENCY
}

enum UserBehaviorPatternStatus {
  ACTIVE
  INACTIVE
  ANOMALY
}

enum UserActivityAnomalyType {
  UNUSUAL_TIME
  EXCESSIVE_OPERATIONS
  ABNORMAL_LOCATION
  SUSPICIOUS_BEHAVIOR
}

enum UserActivityAnomalySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum EmailTemplateType {
  ALERT
  MAINTENANCE
  SERVICE_CREATED
  SERVICE_ASSIGNED
  SERVICE_RESOLVED
  SERVICE_CLOSED
  USER_WELCOME
  PASSWORD_RESET
  ACCOUNT_LOCKED
  BACKUP_SUCCESS
  BACKUP_FAILED
  CUSTOM
}

enum EmailTemplateCategory {
  SYSTEM
  BUSINESS
  NOTIFICATION
  SECURITY
}

enum EmailLogStatus {
  PENDING
  SENDING
  SENT
  DELIVERED
  OPENED
  FAILED
  CANCELLED
}

enum TaskStatus {
  RUNNING
  SUCCESS
  FAILED
}

enum WorkflowCategory {
  SERVICE_AUTOMATION
  SLA_MONITORING
  ALERT_PROCESSING
  BACKUP_AUTOMATION
  MAINTENANCE
  APPROVAL_PROCESS
  NOTIFICATION
  DATA_PROCESSING
  INTEGRATION
  CUSTOM
}

enum WorkflowPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum WorkflowExecutionStatus {
  PENDING
  RUNNING
  PAUSED
  COMPLETED
  FAILED
  CANCELLED
  TIMEOUT
  SKIPPED
}

enum WorkflowTriggerType {
  MANUAL
  SCHEDULED
  EVENT
  WEBHOOK
  API
  CONDITION
}

enum WorkflowStepType {
  ACTION
  CONDITION
  APPROVAL
  NOTIFICATION
  DELAY
  PARALLEL
  LOOP
  SUBPROCESS
  SCRIPT
  HTTP_REQUEST
  DATABASE_OPERATION
  FILE_OPERATION
}

enum WorkflowStepStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  SKIPPED
  RETRYING
}

enum WorkflowTemplateDifficulty {
  beginner
  intermediate
  advanced
}

enum WorkflowTemplateUsageAction {
  preview
  apply
  download
  rate
}
