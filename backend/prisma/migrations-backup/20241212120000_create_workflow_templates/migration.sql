-- CreateTable
CREATE TABLE `workflow_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT NULL,
    `category` ENUM('SERVICE_AUTOMATION', 'SLA_MONITORING', 'ALERT_PROCESSING', '<PERSON><PERSON><PERSON><PERSON>_AUTOMATION', '<PERSON>IN<PERSON><PERSON>NC<PERSON>', 'APPROVAL_PROCESS', 'NOTIFICATION', 'DATA_PROCESSING', 'INTEGRATION', 'CUSTOM') NOT NULL DEFAULT 'CUSTOM',
    `subcategory` VARCHAR(100) NOT NULL DEFAULT 'user_created',
    `tags` JSON NULL,
    `author` VARCHAR(100) NOT NULL,
    `version` VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    `is_builtin` BOOLEAN NOT NULL DEFAULT false,
    `is_popular` BOOLEAN NOT NULL DEFAULT false,
    `is_active` <PERSON>O<PERSON>EAN NOT NULL DEFAULT true,
    `usage_count` INT UNSIGNED NOT NULL DEFAULT 0,
    `rating` DECIMAL(2, 1) NOT NULL DEFAULT 0.0,
    `difficulty` ENUM('beginner', 'intermediate', 'advanced') NOT NULL DEFAULT 'beginner',
    `estimated_duration` INT UNSIGNED NOT NULL DEFAULT 5,
    `preview_thumbnail` VARCHAR(500) NULL,
    `preview_screenshots` JSON NULL,
    `preview_demo_video` VARCHAR(500) NULL,
    `requirements_permissions` JSON NULL,
    `requirements_integrations` JSON NULL,
    `requirements_minimum_version` VARCHAR(20) NOT NULL DEFAULT '1.0.0',
    `workflow_definition` JSON NOT NULL,
    `metadata` JSON NULL,
    `created_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `workflow_templates_category_idx`(`category`),
    INDEX `workflow_templates_subcategory_idx`(`subcategory`),
    INDEX `workflow_templates_author_idx`(`author`),
    INDEX `workflow_templates_is_builtin_idx`(`is_builtin`),
    INDEX `workflow_templates_is_popular_idx`(`is_popular`),
    INDEX `workflow_templates_is_active_idx`(`is_active`),
    INDEX `workflow_templates_difficulty_idx`(`difficulty`),
    INDEX `workflow_templates_rating_idx`(`rating`),
    INDEX `workflow_templates_usage_count_idx`(`usage_count`),
    INDEX `workflow_templates_created_at_idx`(`created_at`),
    FULLTEXT INDEX `workflow_templates_search_idx`(`name`, `description`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_template_ratings` (
    `id` VARCHAR(191) NOT NULL,
    `template_id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `rating` INT UNSIGNED NOT NULL,
    `comment` TEXT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `workflow_template_ratings_template_id_idx`(`template_id`),
    INDEX `workflow_template_ratings_user_id_idx`(`user_id`),
    INDEX `workflow_template_ratings_rating_idx`(`rating`),
    UNIQUE INDEX `workflow_template_ratings_template_user_unique`(`template_id`, `user_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_template_usage_logs` (
    `id` VARCHAR(191) NOT NULL,
    `template_id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `workflow_id` VARCHAR(191) NULL,
    `action` ENUM('preview', 'apply', 'download', 'rate') NOT NULL,
    `metadata` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `workflow_template_usage_logs_template_id_idx`(`template_id`),
    INDEX `workflow_template_usage_logs_user_id_idx`(`user_id`),
    INDEX `workflow_template_usage_logs_action_idx`(`action`),
    INDEX `workflow_template_usage_logs_created_at_idx`(`created_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_template_categories` (
    `id` VARCHAR(191) NOT NULL,
    `category_id` ENUM('SERVICE_AUTOMATION', 'SLA_MONITORING', 'ALERT_PROCESSING', 'BACKUP_AUTOMATION', 'MAINTENANCE', 'APPROVAL_PROCESS', 'NOTIFICATION', 'DATA_PROCESSING', 'INTEGRATION', 'CUSTOM') NOT NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT NULL,
    `icon` VARCHAR(50) NOT NULL DEFAULT '🔧',
    `subcategories` JSON NOT NULL,
    `display_order` INT UNSIGNED NOT NULL DEFAULT 0,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `workflow_template_categories_category_id_idx`(`category_id`),
    INDEX `workflow_template_categories_display_order_idx`(`display_order`),
    INDEX `workflow_template_categories_is_active_idx`(`is_active`),
    UNIQUE INDEX `workflow_template_categories_category_id_unique`(`category_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add Foreign Key Constraints
ALTER TABLE `workflow_template_ratings` ADD CONSTRAINT `workflow_template_ratings_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `workflow_templates`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `workflow_template_ratings` ADD CONSTRAINT `workflow_template_ratings_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `workflow_template_usage_logs` ADD CONSTRAINT `workflow_template_usage_logs_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `workflow_templates`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `workflow_template_usage_logs` ADD CONSTRAINT `workflow_template_usage_logs_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 插入预置分类数据
INSERT INTO `workflow_template_categories` (`id`, `category_id`, `name`, `description`, `icon`, `subcategories`, `display_order`, `is_active`) VALUES
('cat_service_auto', 'SERVICE_AUTOMATION', '服务自动化', '自动化服务管理和运维任务', '🔧', '[{"id":"deployment","name":"部署自动化","description":"应用部署和更新流程"},{"id":"scaling","name":"弹性伸缩","description":"资源自动伸缩管理"},{"id":"restart","name":"服务重启","description":"服务异常自动恢复"},{"id":"health_check","name":"健康检查","description":"服务健康状态监控"}]', 1, true),
('cat_sla_monitor', 'SLA_MONITORING', 'SLA监控', 'SLA指标监控和违规处理', '📊', '[{"id":"availability","name":"可用性监控","description":"服务可用性指标跟踪"},{"id":"performance","name":"性能监控","description":"响应时间和吞吐量监控"},{"id":"violation","name":"违规处理","description":"SLA违规自动响应流程"},{"id":"reporting","name":"报告生成","description":"SLA报告自动生成"}]', 2, true),
('cat_alert_proc', 'ALERT_PROCESSING', '告警处理', '智能告警处理和响应', '🚨', '[{"id":"escalation","name":"告警升级","description":"告警逐级升级处理"},{"id":"correlation","name":"告警关联","description":"相关告警自动关联"},{"id":"suppression","name":"告警抑制","description":"重复告警智能过滤"},{"id":"notification","name":"通知分发","description":"多渠道告警通知"}]', 3, true),
('cat_backup_auto', 'BACKUP_AUTOMATION', '备份自动化', '数据备份和恢复流程', '💾', '[{"id":"database","name":"数据库备份","description":"数据库定期备份"},{"id":"file_backup","name":"文件备份","description":"重要文件系统备份"},{"id":"disaster_recovery","name":"灾难恢复","description":"灾难恢复流程"},{"id":"cleanup","name":"备份清理","description":"过期备份自动清理"}]', 4, true),
('cat_maintenance', 'MAINTENANCE', '维护任务', '系统维护和优化任务', '🔧', '[{"id":"patch_management","name":"补丁管理","description":"系统补丁自动更新"},{"id":"cleanup","name":"系统清理","description":"日志和临时文件清理"},{"id":"optimization","name":"性能优化","description":"系统性能调优"},{"id":"security_scan","name":"安全扫描","description":"安全漏洞扫描"}]', 5, true),
('cat_approval', 'APPROVAL_PROCESS', '审批流程', '运维操作审批流程', '✅', '[{"id":"change_request","name":"变更请求","description":"系统变更审批流程"},{"id":"access_request","name":"权限申请","description":"系统访问权限审批"},{"id":"resource_request","name":"资源申请","description":"计算资源申请审批"},{"id":"emergency","name":"紧急操作","description":"紧急操作快速审批"}]', 6, true),
('cat_notification', 'NOTIFICATION', '通知流程', '多渠道通知和消息推送', '📢', '[{"id":"incident","name":"事件通知","description":"故障事件通知流程"},{"id":"maintenance","name":"维护通知","description":"系统维护通知"},{"id":"report","name":"报告推送","description":"定期报告推送"},{"id":"status_update","name":"状态更新","description":"服务状态变更通知"}]', 7, true),
('cat_data_proc', 'DATA_PROCESSING', '数据处理', '数据采集、处理和分析', '📈', '[{"id":"collection","name":"数据采集","description":"系统数据自动采集"},{"id":"analysis","name":"数据分析","description":"运维数据分析处理"},{"id":"migration","name":"数据迁移","description":"数据迁移和同步"},{"id":"archival","name":"数据归档","description":"历史数据归档处理"}]', 8, true),
('cat_integration', 'INTEGRATION', '集成流程', '第三方系统集成', '🔗', '[{"id":"api_sync","name":"API同步","description":"第三方API数据同步"},{"id":"webhook","name":"Webhook处理","description":"Webhook事件处理"},{"id":"file_transfer","name":"文件传输","description":"FTP/SFTP文件传输"},{"id":"message_queue","name":"消息队列","description":"消息队列处理"}]', 9, true),
('cat_custom', 'CUSTOM', '自定义模板', '用户自定义工作流模板', '🎨', '[{"id":"user_created","name":"用户创建","description":"用户自定义模板"},{"id":"organization","name":"组织模板","description":"组织内部模板"},{"id":"imported","name":"导入模板","description":"外部导入模板"}]', 10, true);