-- CreateTable
CREATE TABLE `email_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHA<PERSON>(191) NOT NULL,
    `type` ENUM('ALERT', 'MAINTENANCE', 'SERVICE_CREATED', 'SERVICE_ASSIGNED', 'SERVICE_RESOLVED', 'SERVICE_CLOSED', 'USER_WELCOME', 'PASSWORD_RESET', 'ACCOUNT_LOCKED', 'BAC<PERSON>UP_SUCCESS', 'BACKUP_FAILED', 'CUSTOM') NOT NULL,
    `category` ENUM('SYSTEM', 'BUSINESS', 'NOTIFICATION', 'SECURITY') NOT NULL DEFAULT 'SYSTEM',
    `subject` VARCHAR(191) NOT NULL,
    `content` LONGTEXT NOT NULL,
    `description` TEXT NULL,
    `variables` JSON NULL,
    `enabled` BOOLEAN NOT NULL DEFAULT true,
    `is_system` BOOLEAN NOT NULL DEFAULT false,
    `version` VARCHAR(191) NOT NULL DEFAULT '1.0',
    `metadata` JSON NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `updated_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `email_templates_name_type_key`(`name`, `type`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `email_logs` (
    `id` VARCHAR(191) NOT NULL,
    `template_id` VARCHAR(191) NULL,
    `recipient` VARCHAR(191) NOT NULL,
    `subject` VARCHAR(191) NOT NULL,
    `content` LONGTEXT NOT NULL,
    `status` ENUM('PENDING', 'SENDING', 'SENT', 'DELIVERED', 'OPENED', 'FAILED', 'CANCELLED') NOT NULL DEFAULT 'PENDING',
    `error_message` TEXT NULL,
    `sent_at` DATETIME(3) NULL,
    `delivered_at` DATETIME(3) NULL,
    `opened_at` DATETIME(3) NULL,
    `metadata` JSON NULL,
    `retry_count` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `email_logs_recipient_idx`(`recipient`),
    INDEX `email_logs_status_idx`(`status`),
    INDEX `email_logs_created_at_idx`(`created_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `email_templates` ADD CONSTRAINT `email_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `email_templates` ADD CONSTRAINT `email_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `email_logs` ADD CONSTRAINT `email_logs_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `email_templates`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
