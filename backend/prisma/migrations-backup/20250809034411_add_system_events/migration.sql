/*
  Warnings:

  - A unique constraint covering the columns `[key]` on the table `system_configs` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX `system_configs_category_key_key` ON `system_configs`;

-- AlterTable
ALTER TABLE `services` ADD COLUMN `external_account` VARCHAR(191) NULL,
    ADD COLUMN `external_system_id` VARCHAR(191) NULL,
    ADD COLUMN `external_user_id` VARCHAR(191) NULL,
    ADD COLUMN `source` ENUM('INTERNAL', 'EXTERNAL') NOT NULL DEFAULT 'INTERNAL';

-- AlterTable
ALTER TABLE `user_activity_anomalies` MODIFY `confidence_score` DOUBLE NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `user_activity_stats` MODIFY `activity_score` DOUBLE NOT NULL DEFAULT 0,
    ALTER COLUMN `updated_at` DROP DEFAULT;

-- AlterTable
ALTER TABLE `user_behavior_patterns` MODIFY `confidence_score` DOUBLE NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE `user_feature_usage` MODIFY `avg_time_spent` DOUBLE NULL DEFAULT 0,
    ALTER COLUMN `updated_at` DROP DEFAULT;

-- AlterTable
ALTER TABLE `user_sessions` MODIFY `activity_score` DOUBLE NULL DEFAULT 0,
    ALTER COLUMN `updated_at` DROP DEFAULT;

-- CreateTable
CREATE TABLE `api_keys` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `key_value` VARCHAR(191) NOT NULL,
    `system_id` VARCHAR(191) NOT NULL,
    `description` VARCHAR(191) NULL,
    `status` ENUM('ACTIVE', 'INACTIVE', 'REVOKED', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE',
    `expires_at` DATETIME(3) NULL,
    `last_used_at` DATETIME(3) NULL,
    `usage_count` INTEGER NOT NULL DEFAULT 0,
    `created_by` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `api_keys_key_value_key`(`key_value`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `permission_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `permissions` JSON NOT NULL,
    `category` ENUM('SYSTEM', 'BUSINESS', 'SERVICE', 'READONLY', 'CUSTOM') NOT NULL DEFAULT 'CUSTOM',
    `is_default` BOOLEAN NOT NULL DEFAULT false,
    `is_system` BOOLEAN NOT NULL DEFAULT false,
    `version` VARCHAR(191) NOT NULL DEFAULT '1.0',
    `metadata` JSON NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `updated_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `permission_templates_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `permission_template_history` (
    `id` VARCHAR(191) NOT NULL,
    `template_id` VARCHAR(191) NOT NULL,
    `action` ENUM('CREATE', 'UPDATE', 'DELETE', 'APPLY', 'EXPORT', 'IMPORT', 'COPY') NOT NULL,
    `old_data` JSON NULL,
    `new_data` JSON NULL,
    `change_reason` VARCHAR(191) NULL,
    `changed_by` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `permission_template_usage` (
    `id` VARCHAR(191) NOT NULL,
    `template_id` VARCHAR(191) NOT NULL,
    `role_id` VARCHAR(191) NOT NULL,
    `applied_by` VARCHAR(191) NOT NULL,
    `applied_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `status` ENUM('ACTIVE', 'INACTIVE', 'SUPERSEDED') NOT NULL DEFAULT 'ACTIVE',
    `note` TEXT NULL,

    UNIQUE INDEX `permission_template_usage_template_id_role_id_key`(`template_id`, `role_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `system_events` (
    `id` VARCHAR(191) NOT NULL,
    `level` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `message` TEXT NOT NULL,
    `details` JSON NULL,
    `source` VARCHAR(191) NULL,
    `resolved` BOOLEAN NOT NULL DEFAULT false,
    `resolved_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `resolved_at` DATETIME(3) NULL,

    INDEX `system_events_level_type_idx`(`level`, `type`),
    INDEX `system_events_created_at_idx`(`created_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `system_metrics` (
    `id` VARCHAR(191) NOT NULL,
    `timestamp` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `metric_type` VARCHAR(191) NOT NULL,
    `value` DOUBLE NOT NULL,
    `unit` VARCHAR(191) NOT NULL,
    `tags` JSON NULL,

    INDEX `system_metrics_metric_type_timestamp_idx`(`metric_type`, `timestamp`),
    INDEX `system_metrics_timestamp_idx`(`timestamp`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `performance_benchmarks` (
    `id` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `metric` VARCHAR(191) NOT NULL,
    `baseline` DOUBLE NOT NULL,
    `target` DOUBLE NOT NULL,
    `description` TEXT NULL,
    `enabled` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `performance_benchmarks_category_metric_key`(`category`, `metric`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `monitoring_config` (
    `id` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `key` VARCHAR(191) NOT NULL,
    `value` JSON NOT NULL,
    `description` TEXT NULL,
    `enabled` BOOLEAN NOT NULL DEFAULT true,
    `updated_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `monitoring_config_category_key_key`(`category`, `key`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `alert_thresholds` (
    `id` VARCHAR(191) NOT NULL,
    `metric_type` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `warning` DOUBLE NOT NULL,
    `critical` DOUBLE NOT NULL,
    `duration` INTEGER NOT NULL,
    `enabled` BOOLEAN NOT NULL DEFAULT true,
    `description` TEXT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `alert_thresholds_metric_type_name_key`(`metric_type`, `name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `service_availability` (
    `id` VARCHAR(191) NOT NULL,
    `service_name` VARCHAR(191) NOT NULL,
    `date` DATE NOT NULL,
    `uptime` DOUBLE NOT NULL,
    `downtime` DOUBLE NOT NULL,
    `incidents` INTEGER NOT NULL DEFAULT 0,
    `mttr` DOUBLE NULL,

    INDEX `service_availability_service_name_date_idx`(`service_name`, `date`),
    UNIQUE INDEX `service_availability_service_name_date_key`(`service_name`, `date`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `intelligent_analysis_results` (
    `id` VARCHAR(191) NOT NULL,
    `analysis_type` VARCHAR(191) NOT NULL,
    `result` JSON NOT NULL,
    `confidence` DOUBLE NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'active',
    `valid_until` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `intelligent_analysis_results_analysis_type_status_idx`(`analysis_type`, `status`),
    INDEX `intelligent_analysis_results_created_at_idx`(`created_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE UNIQUE INDEX `system_configs_key_key` ON `system_configs`(`key`);

-- AddForeignKey
ALTER TABLE `api_keys` ADD CONSTRAINT `api_keys_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_templates` ADD CONSTRAINT `permission_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_templates` ADD CONSTRAINT `permission_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_template_history` ADD CONSTRAINT `permission_template_history_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `permission_templates`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_template_history` ADD CONSTRAINT `permission_template_history_changed_by_fkey` FOREIGN KEY (`changed_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_template_usage` ADD CONSTRAINT `permission_template_usage_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `permission_templates`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_template_usage` ADD CONSTRAINT `permission_template_usage_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `permission_template_usage` ADD CONSTRAINT `permission_template_usage_applied_by_fkey` FOREIGN KEY (`applied_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `system_events` ADD CONSTRAINT `system_events_resolved_by_fkey` FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- RenameIndex
ALTER TABLE `user_activity_anomalies` RENAME INDEX `idx_user_activity_anomalies_anomaly_type` TO `user_activity_anomalies_anomaly_type_idx`;

-- RenameIndex
ALTER TABLE `user_activity_anomalies` RENAME INDEX `idx_user_activity_anomalies_is_resolved` TO `user_activity_anomalies_is_resolved_idx`;

-- RenameIndex
ALTER TABLE `user_activity_anomalies` RENAME INDEX `idx_user_activity_anomalies_severity` TO `user_activity_anomalies_severity_idx`;

-- RenameIndex
ALTER TABLE `user_activity_anomalies` RENAME INDEX `idx_user_activity_anomalies_user_id` TO `user_activity_anomalies_user_id_idx`;

-- RenameIndex
ALTER TABLE `user_activity_stats` RENAME INDEX `idx_user_activity_stats_activity_score` TO `user_activity_stats_activity_score_idx`;

-- RenameIndex
ALTER TABLE `user_activity_stats` RENAME INDEX `idx_user_activity_stats_date` TO `user_activity_stats_date_idx`;

-- RenameIndex
ALTER TABLE `user_behavior_patterns` RENAME INDEX `idx_user_behavior_patterns_pattern_type` TO `user_behavior_patterns_pattern_type_idx`;

-- RenameIndex
ALTER TABLE `user_behavior_patterns` RENAME INDEX `idx_user_behavior_patterns_status` TO `user_behavior_patterns_status_idx`;

-- RenameIndex
ALTER TABLE `user_feature_usage` RENAME INDEX `idx_user_feature_usage_date` TO `user_feature_usage_date_idx`;

-- RenameIndex
ALTER TABLE `user_feature_usage` RENAME INDEX `idx_user_feature_usage_feature_name` TO `user_feature_usage_feature_name_idx`;

-- RenameIndex
ALTER TABLE `user_sessions` RENAME INDEX `idx_user_sessions_is_active` TO `user_sessions_is_active_idx`;

-- RenameIndex
ALTER TABLE `user_sessions` RENAME INDEX `idx_user_sessions_last_activity` TO `user_sessions_last_activity_idx`;

-- RenameIndex
ALTER TABLE `user_sessions` RENAME INDEX `idx_user_sessions_user_id` TO `user_sessions_user_id_idx`;
