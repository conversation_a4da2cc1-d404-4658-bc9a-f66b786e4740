-- CreateTable
CREATE TABLE `service_operation_history` (
    `id` VARCHAR(191) NOT NULL,
    `service_id` VARCHAR(191) NOT NULL,
    `type` ENUM('CREATE', 'UPDATE', 'STATUS_CHANGE', 'TRANSFER', 'ASSIGNMENT', 'COMMENT', 'ATTACHMENT', 'WORK_LOG') NOT NULL,
    `description` VARCHAR(191) NOT NULL,
    `from_value` VARCHAR(191) NULL,
    `to_value` VARCHAR(191) NULL,
    `note` TEXT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `service_operation_history` ADD CONSTRAINT `service_operation_history_service_id_fkey` FOREIGN KEY (`service_id`) REFERENCES `services`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `service_operation_history` ADD CONSTRAINT `service_operation_history_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
