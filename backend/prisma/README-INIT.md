# 数据库初始化说明

## 概述

本目录包含运维管理系统的数据库初始化文件，已将所有原有的migration文件整合成统一的初始化脚本。

## 文件说明

### 主要文件

1. **`init.sql`** - 数据库初始化脚本
   - 包含所有表结构定义
   - 包含所有索引和外键约束
   - 基于所有历史migration文件整合而成

2. **`seeds/init-seed-data.sql`** - 初始种子数据
   - 工作流模板分类的预置数据
   - 系统启动必需的基础数据

### 使用方法

#### 方法一：直接执行SQL文件
```bash
# 1. 执行数据库初始化
mysql -u username -p database_name < init.sql

# 2. 执行种子数据
mysql -u username -p database_name < seeds/init-seed-data.sql
```

#### 方法二：使用Prisma
```bash
# 1. 重置数据库并应用schema
npx prisma db push --force-reset

# 2. 执行种子数据
npx prisma db seed
```

## 表结构概览

数据库包含以下主要模块：

### 用户和权限管理
- `users` - 用户表
- `roles` - 角色表
- `user_roles` - 用户角色关联表
- `permission_templates` - 权限模板表
- `permission_template_history` - 权限模板历史
- `permission_template_usage` - 权限模板使用记录

### 客户管理
- `customers` - 客户表
- `customer_contacts` - 客户联系人表

### 项目归档
- `project_archives` - 项目归档表
- `project_configurations` - 项目配置表

### 服务管理
- `services` - 服务单表
- `service_work_logs` - 工作日志表
- `service_attachments` - 附件表
- `service_comments` - 评论表
- `service_operation_history` - 操作历史表
- `sla_templates` - SLA模板表

### 通知系统
- `notification_templates` - 通知模板表
- `notifications` - 通知记录表
- `email_templates` - 邮件模板表
- `email_logs` - 邮件日志表

### 系统监控
- `alert_rules` - 告警规则表
- `alerts` - 告警记录表
- `system_events` - 系统事件表
- `system_metrics` - 系统指标表
- `performance_benchmarks` - 性能基准表
- `monitoring_config` - 监控配置表
- `alert_thresholds` - 告警阈值表
- `service_availability` - 服务可用性表
- `intelligent_analysis_results` - 智能分析结果表

### 用户行为分析
- `user_sessions` - 用户会话表
- `user_activity_stats` - 用户活动统计表
- `user_behavior_patterns` - 用户行为模式表
- `user_activity_anomalies` - 用户活动异常表
- `user_feature_usage` - 功能使用统计表

### AI功能
- `ai_configurations` - AI配置表
- `ai_analysis_requests` - AI分析请求表
- `ai_feedback` - AI反馈表
- `ai_prompt_templates` - AI提示模板表

### 工作流系统
- `workflow_definitions` - 工作流定义表
- `workflow_executions` - 工作流执行表
- `workflow_execution_steps` - 工作流执行步骤表
- `workflow_templates` - 工作流模板表
- `workflow_template_ratings` - 工作流模板评分表
- `workflow_template_usage_logs` - 工作流模板使用日志表
- `workflow_template_categories` - 工作流模板分类表

### 通信集成
- `communication_integrations` - 通信集成表
- `communication_integration_executions` - 通信集成执行表
- `communication_integration_metrics` - 通信集成指标表

### 其他
- `operation_logs` - 操作日志表
- `api_keys` - API密钥表
- `system_configs` - 系统配置表
- `system_config_history` - 系统配置历史表
- `task_executions` - 任务执行表

## 注意事项

1. **字符集**: 所有表使用 `utf8mb4` 字符集，支持完整的Unicode字符
2. **外键约束**: 已设置完整的外键约束关系
3. **索引优化**: 已为常用查询字段添加索引
4. **数据类型**: 使用了适当的数据类型以确保性能和存储效率

## 历史说明

此初始化脚本整合了以下历史migration文件：
- `20241212120000_create_workflow_templates`
- `20250803015203_init` 
- `20250803034526_add_customer_fields`
- `20250803134504_remove_customer_email_phone_fields`
- `20250805152636_add_service_operation_history`
- `20250807012426_add_system_config_management`
- `20250807015500_add_user_analytics_tables`
- `20250809034411_add_system_events`
- `20250809052933_add_email_templates`
- `20250810043258_add_task_execution_model`
- `20250811133817_add_task_execution_model`
- `20250813010737_init`

新的部署应该使用这个统一的初始化脚本，而不是逐个执行历史migration文件。
