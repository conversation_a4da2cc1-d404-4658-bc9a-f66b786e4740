#!/bin/bash

# ========================================
# 数据库迁移重置脚本
# 用于重新初始化Prisma migration历史
# ========================================

echo "🔄 开始重置数据库迁移..."

# 检查是否在正确的目录
if [ ! -f "schema.prisma" ]; then
    echo "❌ 错误: 请在 prisma 目录下运行此脚本"
    exit 1
fi

# 1. 备份当前的migrations目录（如果存在）
if [ -d "migrations" ] && [ "$(ls -A migrations)" ]; then
    BACKUP_DIR="migrations-backup-$(date +%Y%m%d-%H%M%S)"
    echo "📦 备份当前migrations到 $BACKUP_DIR"
    mkdir -p "$BACKUP_DIR"
    cp -r migrations/* "$BACKUP_DIR/"
fi

# 2. 清空migrations目录
echo "🧹 清空migrations目录..."
rm -rf migrations/*
rm -rf migrations/.git* 2>/dev/null || true

# 3. 生成新的初始migration
echo "🆕 生成新的初始migration..."
npx prisma migrate dev --name init --create-only

# 4. 应用数据库schema
echo "💾 应用数据库schema..."
npx prisma db push

# 5. 标记migration为已应用
echo "✅ 标记migration为已应用..."
npx prisma migrate resolve --applied $(ls migrations/ | head -1)

# 6. 运行种子数据（如果存在）
if [ -f "seeds/init-seed-data.sql" ]; then
    echo "🌱 执行种子数据..."
    # 这里需要根据你的数据库配置调整连接参数
    echo "ℹ️  请手动执行种子数据:"
    echo "   mysql -u [username] -p [database] < seeds/init-seed-data.sql"
fi

echo "✨ 迁移重置完成!"
echo ""
echo "📝 接下来的步骤:"
echo "1. 检查生成的migration文件是否正确"
echo "2. 如果需要，手动执行种子数据"
echo "3. 运行 'npx prisma generate' 更新Prisma客户端"
echo ""
echo "🔍 有用的命令:"
echo "   npx prisma studio     # 打开数据库可视化工具"
echo "   npx prisma db seed    # 运行种子脚本（如果配置了）"
echo "   npx prisma migrate status  # 检查迁移状态"
