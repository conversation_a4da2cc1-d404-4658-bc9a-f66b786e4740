/**
 * 测试通信集成服务
 */

import { communicationIntegrationService } from '../services/communication-integration.service';
import { CommunicationPlatform } from '../types/communication-integration';
import { logger } from '../utils/logger.util';

async function testCommunicationIntegration() {
  try {
    logger.info('开始测试通信集成服务...');

    // 测试获取所有集成
    logger.info('测试获取所有集成...');
    const allIntegrations = await communicationIntegrationService.getAllIntegrations();
    logger.info(`获取到 ${allIntegrations.length} 个集成`);

    // 测试根据类型获取集成
    logger.info('测试根据类型获取集成...');
    const wechatIntegrations = await communicationIntegrationService.getIntegrationsByType(CommunicationPlatform.WECHAT_WORK);
    logger.info(`获取到 ${wechatIntegrations.length} 个企业微信集成`);

    // 测试连接测试
    logger.info('测试连接测试...');
    const testResult = await communicationIntegrationService.testConnection({
      type: CommunicationPlatform.WECHAT_WORK,
      config: {
        corpId: 'test_corp_id',
        agentId: 'test_agent_id',
        secret: 'test_secret',
        settings: {
          enableMessagePush: true,
          enableFileUpload: false,
          enableUserSync: false,
          messageTemplate: '测试消息',
          retryCount: 3,
          timeout: 5000
        }
      }
    });
    logger.info('连接测试结果:', testResult);

    // 测试健康检查（如果有集成的话）
    if (allIntegrations.length > 0) {
      const firstIntegration = allIntegrations[0];
      logger.info(`测试健康检查: ${firstIntegration.name}`);
      const healthResult = await communicationIntegrationService.healthCheck(firstIntegration.id);
      logger.info('健康检查结果:', healthResult);
    }

    logger.info('通信集成服务测试完成！');
  } catch (error) {
    logger.error('测试失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testCommunicationIntegration()
    .then(() => {
      logger.info('测试脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

export { testCommunicationIntegration };
