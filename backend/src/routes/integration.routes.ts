/**
 * 外部系统集成管理路由
 */

import { Router } from 'express';
import { integrationController } from '../controllers/integration.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { validatePermissions } from '../middleware/permission.middleware';

const router = Router();

// 应用认证中间件
router.use(authMiddleware);

// #region 集成配置管理路由

/**
 * @swagger
 * /api/v1/integrations:
 *   get:
 *     summary: 获取所有集成配置
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: 按类型过滤
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 按状态过滤
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 按分类过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/IntegrationConfig'
 *                 message:
 *                   type: string
 */
router.get('/', 
  validatePermissions(['integration:read']),
  integrationController.getAllIntegrations
);

/**
 * @swagger
 * /api/v1/integrations/{id}:
 *   get:
 *     summary: 获取单个集成配置
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 集成ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 集成不存在
 */
router.get('/:id',
  validatePermissions(['integration:read']),
  integrationController.getIntegration
);

/**
 * @swagger
 * /api/v1/integrations:
 *   post:
 *     summary: 创建集成配置
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *               - auth
 *               - endpoints
 *               - category
 *             properties:
 *               name:
 *                 type: string
 *                 description: 集成名称
 *               type:
 *                 type: string
 *                 enum: [cloud_aliyun, chat_wechat_work, chat_dingtalk, chat_slack]
 *                 description: 集成类型
 *               description:
 *                 type: string
 *                 description: 描述
 *               auth:
 *                 type: object
 *                 description: 认证配置
 *               endpoints:
 *                 type: object
 *                 description: API端点配置
 *               category:
 *                 type: string
 *                 description: 分类
 *               priority:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 10
 *                 description: 优先级
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 */
router.post('/',
  validatePermissions(['integration:create']),
  integrationController.createIntegration
);

/**
 * @swagger
 * /api/v1/integrations/{id}:
 *   put:
 *     summary: 更新集成配置
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 集成不存在
 */
router.put('/:id',
  validatePermissions(['integration:update']),
  integrationController.updateIntegration
);

/**
 * @swagger
 * /api/v1/integrations/{id}:
 *   delete:
 *     summary: 删除集成配置
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 集成不存在
 */
router.delete('/:id',
  validatePermissions(['integration:delete']),
  integrationController.deleteIntegration
);

// #endregion

// #region 集成操作执行路由

/**
 * @swagger
 * /api/v1/integrations/{id}/execute:
 *   post:
 *     summary: 执行集成操作
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 集成ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - operation
 *               - endpoint
 *             properties:
 *               operation:
 *                 type: string
 *                 enum: [query, get, list, search, create, update, delete, start, stop, restart, notify, alert, message, monitor, metrics, health_check]
 *                 description: 操作类型
 *               endpoint:
 *                 type: string
 *                 description: 端点名称
 *               parameters:
 *                 type: object
 *                 description: 请求参数
 *               headers:
 *                 type: object
 *                 description: 请求头
 *               body:
 *                 description: 请求体
 *               timeout:
 *                 type: number
 *                 description: 超时时间(毫秒)
 *     responses:
 *       200:
 *         description: 执行成功
 *       404:
 *         description: 集成不存在
 */
router.post('/:id/execute',
  validatePermissions(['integration:execute']),
  integrationController.executeOperation
);

/**
 * @swagger
 * /api/v1/integrations/batch-execute:
 *   post:
 *     summary: 批量执行集成操作
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - requests
 *             properties:
 *               requests:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/IntegrationRequest'
 *               parallel:
 *                 type: boolean
 *                 description: 是否并行执行
 *               continueOnError:
 *                 type: boolean
 *                 description: 出错时是否继续执行
 *               timeout:
 *                 type: number
 *                 description: 总超时时间
 *     responses:
 *       200:
 *         description: 批量执行完成
 */
router.post('/batch-execute',
  validatePermissions(['integration:execute']),
  integrationController.executeBatchOperations
);

// #endregion

// #region 监控和健康检查路由

/**
 * @swagger
 * /api/v1/integrations/{id}/metrics:
 *   get:
 *     summary: 获取集成监控指标
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 集成不存在
 */
router.get('/:id/metrics',
  validatePermissions(['integration:monitor']),
  integrationController.getMetrics
);

/**
 * @swagger
 * /api/v1/integrations/{id}/events:
 *   get:
 *     summary: 获取集成事件日志
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           default: 100
 *         description: 返回条数限制
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 集成不存在
 */
router.get('/:id/events',
  validatePermissions(['integration:monitor']),
  integrationController.getEvents
);

/**
 * @swagger
 * /api/v1/integrations/{id}/health:
 *   get:
 *     summary: 集成健康检查
 *     tags: [Integration]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 健康检查完成
 *       404:
 *         description: 集成不存在
 */
router.get('/:id/health',
  validatePermissions(['integration:monitor']),
  integrationController.healthCheck
);

// #endregion

// #region 通信集成专用路由

/**
 * @swagger
 * /api/v1/integrations/communication/send-message:
 *   post:
 *     summary: 发送消息
 *     tags: [Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *               - recipients
 *               - message
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [wechat_work, dingtalk, slack]
 *                 description: 通信平台
 *               recipients:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 接收者列表
 *               message:
 *                 type: object
 *                 required:
 *                   - type
 *                   - content
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [text, markdown, card, image, file]
 *                   content:
 *                     type: string
 *                   title:
 *                     type: string
 *                   url:
 *                     type: string
 *               isGroup:
 *                 type: boolean
 *               atAll:
 *                 type: boolean
 *               atUsers:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 发送成功
 *       400:
 *         description: 请求参数错误
 */
router.post('/communication/send-message',
  validatePermissions(['communication:send']),
  integrationController.sendMessage
);

/**
 * @swagger
 * /api/v1/integrations/communication/broadcast:
 *   post:
 *     summary: 广播消息到多个平台
 *     tags: [Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platforms
 *               - recipients
 *               - message
 *             properties:
 *               platforms:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [wechat_work, dingtalk, slack]
 *               recipients:
 *                 type: object
 *                 description: 各平台的接收者列表
 *               message:
 *                 $ref: '#/components/schemas/MessageConfig'
 *     responses:
 *       200:
 *         description: 广播完成
 */
router.post('/communication/broadcast',
  validatePermissions(['communication:broadcast']),
  integrationController.broadcastMessage
);

/**
 * @swagger
 * /api/v1/integrations/communication/wechat-work:
 *   post:
 *     summary: 注册企业微信集成
 *     tags: [Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - corpId
 *               - corpSecret
 *               - agentId
 *             properties:
 *               corpId:
 *                 type: string
 *                 description: 企业ID
 *               corpSecret:
 *                 type: string
 *                 description: 应用密钥
 *               agentId:
 *                 type: string
 *                 description: 应用ID
 *               name:
 *                 type: string
 *                 description: 集成名称
 *     responses:
 *       201:
 *         description: 注册成功
 *       400:
 *         description: 请求参数错误
 */
router.post('/communication/wechat-work',
  validatePermissions(['integration:create']),
  integrationController.registerWeChatWork
);

/**
 * @swagger
 * /api/v1/integrations/communication/dingtalk:
 *   post:
 *     summary: 注册钉钉集成
 *     tags: [Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - appKey
 *               - appSecret
 *             properties:
 *               appKey:
 *                 type: string
 *                 description: 应用Key
 *               appSecret:
 *                 type: string
 *                 description: 应用密钥
 *               robotToken:
 *                 type: string
 *                 description: 机器人Token
 *               name:
 *                 type: string
 *                 description: 集成名称
 *     responses:
 *       201:
 *         description: 注册成功
 */
router.post('/communication/dingtalk',
  validatePermissions(['integration:create']),
  integrationController.registerDingTalk
);

/**
 * @swagger
 * /api/v1/integrations/communication/slack:
 *   post:
 *     summary: 注册Slack集成
 *     tags: [Communication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - botToken
 *               - signingSecret
 *             properties:
 *               botToken:
 *                 type: string
 *                 description: Bot令牌
 *               signingSecret:
 *                 type: string
 *                 description: 签名密钥
 *               appId:
 *                 type: string
 *                 description: 应用ID
 *               name:
 *                 type: string
 *                 description: 集成名称
 *     responses:
 *       201:
 *         description: 注册成功
 */
router.post('/communication/slack',
  validatePermissions(['integration:create']),
  integrationController.registerSlack
);

// #endregion

export default router;