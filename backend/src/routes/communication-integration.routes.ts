/**
 * 通信集成路由
 * 定义通信集成管理的API路由
 */

import { Router } from 'express';
import { communicationIntegrationController } from '../controllers/communication-integration.controller';
import { authMiddleware } from '../middleware/auth.middleware';

const router = Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取所有集成
router.get('/', communicationIntegrationController.getAllIntegrations);

// 根据类型获取集成
router.get('/type/:type', communicationIntegrationController.getIntegrationsByType);

// 获取单个集成
router.get('/:id', communicationIntegrationController.getIntegration);

// 创建集成
router.post('/', communicationIntegrationController.createIntegration);

// 更新集成
router.put('/:id', communicationIntegrationController.updateIntegration);

// 删除集成
router.delete('/:id', communicationIntegrationController.deleteIntegration);

// 测试连接
router.post('/test-connection', communicationIntegrationController.testConnection);

// 发送消息
router.post('/send-message', communicationIntegrationController.sendMessage);

// 健康检查
router.post('/:id/health-check', communicationIntegrationController.healthCheck);

// 启用/禁用集成
router.patch('/:id/toggle', communicationIntegrationController.toggleIntegration);

// 获取集成指标
router.get('/:id/metrics', communicationIntegrationController.getIntegrationMetrics);

export default router;
