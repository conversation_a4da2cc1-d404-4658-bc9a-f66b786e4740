import { Router } from 'express'
import authRoutes from './auth.routes'
import userRoutes from './user.routes'
import roleRoutes from './role.routes'
import customerRoutes from './customer.routes'
import archiveRoutes from './archive.routes'
import serviceRoutes from './service.routes'
import configurationRoutes from './configuration.routes'
import slaRoutes from './sla.routes'
import notificationRoutes from './notification.routes'
import auditRoutes from './audit.routes'
import uploadRoutes from './upload.routes'
import schedulerRoutes from './scheduler.routes'
import taskRoutes from './task.routes'
import systemRoutes from './system.routes'
import permissionRoutes from './permission.routes'
import permissionTemplateRoutes from './permission-template.routes'
import systemConfigRoutes from './system-config.routes'
import monitorRoutes from './monitor.routes'
import userAnalyticsRoutes from './user-analytics.routes'
import realtimeRoutes from './realtime.routes'
import apiKeyRoutes, { meApiKeyRoutes } from './api-key.routes'
import emailTemplateRoutes from './email-template.routes'
import aiRoutes from './ai.routes'
import { alertEngineRoutes } from './alert-engine.routes'
import reportRoutes from './report.routes'
import workflowRoutes from './workflow.routes'
import triggerRoutes from './trigger.routes'
import workflowExecutorRoutes from './workflow-executor.routes'
import workflowTemplateRoutes from './workflow-template.routes'
import communicationIntegrationRoutes from './communication-integration.routes'

const router = Router()

// API版本1路由
router.use('/v1/auth', authRoutes)
router.use('/v1/users', userRoutes)
router.use('/v1/roles', roleRoutes)
router.use('/v1/customers', customerRoutes)
router.use('/v1/archives', archiveRoutes)
router.use('/v1/services', serviceRoutes)
router.use('/v1/configurations', configurationRoutes)
router.use('/v1/sla', slaRoutes)
router.use('/v1/notifications', notificationRoutes)
router.use('/v1/audit', auditRoutes)
router.use('/v1/upload', uploadRoutes)
router.use('/v1/scheduler', schedulerRoutes)
router.use('/v1/tasks', taskRoutes)
router.use('/v1/system', systemRoutes)
router.use('/v1/permissions', permissionRoutes)
router.use('/v1/permission-templates', permissionTemplateRoutes)
router.use('/v1/system-config', systemConfigRoutes)
router.use('/v1/monitor', monitorRoutes)
router.use('/v1/user-analytics', userAnalyticsRoutes)
router.use('/v1/realtime', realtimeRoutes)
router.use('/v1/api-keys', apiKeyRoutes)
router.use('/v1/me/api-keys', meApiKeyRoutes)
router.use('/v1/email-templates', emailTemplateRoutes)
router.use('/v1/ai', aiRoutes)
router.use('/v1/alert-engine', alertEngineRoutes)
router.use('/v1/reports', reportRoutes)
router.use('/v1/workflows', workflowRoutes)
router.use('/v1/triggers', triggerRoutes)
router.use('/v1/workflow-templates', workflowTemplateRoutes)
router.use('/v1/communication-integrations', communicationIntegrationRoutes)
// router.use('/v1/workflow', workflowExecutorRoutes)

export default router