/**
 * 工作流模板路由
 * 处理工作流模板相关的API路由
 */

import { Router } from 'express';
import { workflowTemplateController } from '../controllers/workflow-template.controller';
import { authMiddleware } from '../middleware/auth.middleware';
import { checkPermissions } from '../middleware/permission.middleware';

const router = Router();

// 应用认证中间件到所有路由
router.use(authMiddleware);

// ========== 模板分类和统计 ==========

/**
 * @swagger
 * /api/v1/workflow-templates/categories:
 *   get:
 *     summary: 获取所有模板分类
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       icon:
 *                         type: string
 *                       subcategories:
 *                         type: array
 *                 message:
 *                   type: string
 */
router.get('/categories', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getCategories.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/stats:
 *   get:
 *     summary: 获取模板统计信息
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/stats', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getTemplateStats.bind(workflowTemplateController));

// ========== 模板查询和搜索 ==========

/**
 * @swagger
 * /api/v1/workflow-templates/search:
 *   get:
 *     summary: 搜索模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 模板分类
 *       - in: query
 *         name: difficulty
 *         schema:
 *           type: string
 *           enum: [beginner, intermediate, advanced]
 *         description: 难度级别
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *         description: 标签（逗号分隔）
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 结果数量限制
 *     responses:
 *       200:
 *         description: 搜索成功
 *       400:
 *         description: 搜索参数无效
 */
router.get('/search', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.searchTemplates.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/popular:
 *   get:
 *     summary: 获取热门模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 返回数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/popular', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getPopularTemplates.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/recent:
 *   get:
 *     summary: 获取最新模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 返回数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/recent', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getRecentTemplates.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/category/{category}:
 *   get:
 *     summary: 根据分类获取模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [SERVICE_AUTOMATION, SLA_MONITORING, ALERT_PROCESSING, BACKUP_AUTOMATION, MAINTENANCE, APPROVAL_PROCESS, NOTIFICATION, DATA_PROCESSING, INTEGRATION, CUSTOM]
 *         description: 模板分类
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/category/:category', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getTemplatesByCategory.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/category/{category}/subcategory/{subcategory}:
 *   get:
 *     summary: 根据子分类获取模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *         description: 主分类
 *       - in: path
 *         name: subcategory
 *         required: true
 *         schema:
 *           type: string
 *         description: 子分类
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/category/:category/subcategory/:subcategory', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getTemplatesBySubcategory.bind(workflowTemplateController));

// ========== 模板导入导出（需要放在通用路由前面以避免路径冲突）==========

/**
 * @swagger
 * /api/v1/workflow-templates/import:
 *   post:
 *     summary: 导入模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - template
 *             properties:
 *               version:
 *                 type: string
 *               template:
 *                 type: object
 *                 description: 模板数据
 *     responses:
 *       200:
 *         description: 导入成功
 *       400:
 *         description: 模板数据无效
 */
router.post('/import', checkPermissions(['workflow:create', 'admin:all']), workflowTemplateController.importTemplate.bind(workflowTemplateController));

// ========== 模板管理 ==========

/**
 * @swagger
 * /api/v1/workflow-templates:
 *   get:
 *     summary: 获取模板列表
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 模板分类
 *       - in: query
 *         name: subcategory
 *         schema:
 *           type: string
 *         description: 子分类
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: difficulty
 *         schema:
 *           type: string
 *           enum: [beginner, intermediate, advanced]
 *         description: 难度级别
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *         description: 标签（逗号分隔）
 *       - in: query
 *         name: popular
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *         description: 只显示热门模板
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: 偏移量
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [rating, usage, recent, name]
 *           default: rating
 *         description: 排序字段
 *       - in: query
 *         name: order
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getTemplates.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates:
 *   post:
 *     summary: 创建自定义模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - workflow
 *             properties:
 *               name:
 *                 type: string
 *                 description: 模板名称
 *               description:
 *                 type: string
 *                 description: 模板描述
 *               category:
 *                 type: string
 *                 enum: [SERVICE_AUTOMATION, SLA_MONITORING, ALERT_PROCESSING, BACKUP_AUTOMATION, MAINTENANCE, APPROVAL_PROCESS, NOTIFICATION, DATA_PROCESSING, INTEGRATION, CUSTOM]
 *                 default: CUSTOM
 *                 description: 模板分类
 *               subcategory:
 *                 type: string
 *                 default: user_created
 *                 description: 子分类
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 标签列表
 *               difficulty:
 *                 type: string
 *                 enum: [beginner, intermediate, advanced]
 *                 default: beginner
 *                 description: 难度级别
 *               estimatedDuration:
 *                 type: integer
 *                 default: 5
 *                 description: 预计执行时间（分钟）
 *               workflow:
 *                 type: object
 *                 description: 工作流定义
 *               preview:
 *                 type: object
 *                 properties:
 *                   thumbnail:
 *                     type: string
 *                   screenshots:
 *                     type: array
 *                     items:
 *                       type: string
 *               requirements:
 *                 type: object
 *                 properties:
 *                   permissions:
 *                     type: array
 *                     items:
 *                       type: string
 *                   integrations:
 *                     type: array
 *                     items:
 *                       type: string
 *                   minimumVersion:
 *                     type: string
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数无效
 */
router.post('/', checkPermissions(['workflow:create', 'admin:all']), workflowTemplateController.createCustomTemplate.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/{templateId}:
 *   get:
 *     summary: 获取模板详情
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 模板不存在
 */
router.get('/:templateId', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.getTemplate.bind(workflowTemplateController));

// ========== 模板应用和评分 ==========

/**
 * @swagger
 * /api/v1/workflow-templates/{templateId}/apply:
 *   post:
 *     summary: 应用模板创建工作流
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               customizations:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     description: 工作流名称
 *                   description:
 *                     type: string
 *                     description: 工作流描述
 *                   variables:
 *                     type: object
 *                     description: 自定义变量
 *                   settings:
 *                     type: object
 *                     description: 自定义设置
 *     responses:
 *       200:
 *         description: 应用成功
 *       404:
 *         description: 模板不存在
 */
router.post('/:templateId/apply', checkPermissions(['workflow:create', 'admin:all']), workflowTemplateController.applyTemplate.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/{templateId}/rate:
 *   post:
 *     summary: 对模板进行评分
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - rating
 *             properties:
 *               rating:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *                 description: 评分 (1-5)
 *               comment:
 *                 type: string
 *                 description: 评价内容
 *     responses:
 *       200:
 *         description: 评分成功
 *       400:
 *         description: 评分无效
 *       401:
 *         description: 用户未认证
 *       404:
 *         description: 模板不存在
 */
router.post('/:templateId/rate', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.rateTemplate.bind(workflowTemplateController));

/**
 * @swagger
 * /api/v1/workflow-templates/{templateId}/export:
 *   get:
 *     summary: 导出模板
 *     tags: [Workflow Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     responses:
 *       200:
 *         description: 导出成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 version:
 *                   type: string
 *                 exportedAt:
 *                   type: string
 *                   format: date-time
 *                 template:
 *                   type: object
 *                   description: 模板数据
 *       404:
 *         description: 模板不存在
 */
router.get('/:templateId/export', checkPermissions(['workflow:read', 'admin:all']), workflowTemplateController.exportTemplate.bind(workflowTemplateController));

export default router;