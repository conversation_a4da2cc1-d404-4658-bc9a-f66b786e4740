/**
 * 外部系统集成管理控制器
 * 提供集成配置、操作执行和监控的API端点
 */

import { Request, Response } from 'express';
import { integrationManager } from '../services/integration.service';
import { communicationIntegrationService, MessageConfig } from '../services/communication-integration.service';
import { 
  IntegrationConfig, 
  IntegrationRequest,
  BatchIntegrationRequest,
  IntegrationType,
  OperationType 
} from '../types/integration';
import { ApiResponse } from '../types/common';
import { logger } from '../utils/logger.util';
import { z } from 'zod';

// 请求验证schemas
const CreateIntegrationSchema = z.object({
  name: z.string().min(1, '集成名称不能为空'),
  type: z.nativeEnum(IntegrationType),
  description: z.string().optional(),
  auth: z.object({
    type: z.string(),
    credentials: z.record(z.any())
  }),
  endpoints: z.record(z.object({
    url: z.string().url('无效的URL格式'),
    method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
    headers: z.record(z.string()).optional(),
    timeout: z.number().optional(),
    retries: z.number().optional()
  })),
  settings: z.record(z.any()).optional(),
  tags: z.array(z.string()).optional(),
  category: z.string(),
  priority: z.number().min(0).max(10).default(5)
});

const ExecuteOperationSchema = z.object({
  operation: z.nativeEnum(OperationType),
  endpoint: z.string(),
  parameters: z.record(z.any()).optional(),
  headers: z.record(z.string()).optional(),
  body: z.any().optional(),
  timeout: z.number().optional()
});

const SendMessageSchema = z.object({
  platform: z.enum(['wechat_work', 'dingtalk', 'slack']),
  recipients: z.array(z.string()).min(1, '接收者不能为空'),
  message: z.object({
    type: z.enum(['text', 'markdown', 'card', 'image', 'file']),
    content: z.string().min(1, '消息内容不能为空'),
    title: z.string().optional(),
    url: z.string().url().optional(),
    imageUrl: z.string().url().optional(),
    fileUrl: z.string().url().optional(),
    buttons: z.array(z.object({
      text: z.string(),
      url: z.string().url().optional(),
      type: z.enum(['default', 'primary', 'danger']).optional()
    })).optional()
  }),
  isGroup: z.boolean().optional(),
  atAll: z.boolean().optional(),
  atUsers: z.array(z.string()).optional()
});

export class IntegrationController {

  // #region 集成配置管理

  /**
   * 获取所有集成配置
   */
  async getAllIntegrations(req: Request, res: Response): Promise<void> {
    try {
      const { type, status, category } = req.query;
      
      let integrations = integrationManager.getAllIntegrations();
      
      // 按类型过滤
      if (type) {
        integrations = integrations.filter(i => i.type === type);
      }
      
      // 按状态过滤
      if (status) {
        integrations = integrations.filter(i => i.status === status);
      }
      
      // 按分类过滤
      if (category) {
        integrations = integrations.filter(i => i.category === category);
      }

      const response: ApiResponse<IntegrationConfig[]> = {
        success: true,
        data: integrations,
        message: '获取集成配置成功'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to get integrations:', error);
      res.status(500).json({
        success: false,
        message: '获取集成配置失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 获取单个集成配置
   */
  async getIntegration(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      const integration = integrationManager.getIntegration(id);
      if (!integration) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      const response: ApiResponse<IntegrationConfig> = {
        success: true,
        data: integration,
        message: '获取集成配置成功'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to get integration:', error);
      res.status(500).json({
        success: false,
        message: '获取集成配置失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 创建集成配置
   */
  async createIntegration(req: Request, res: Response): Promise<void> {
    try {
      const validatedData = CreateIntegrationSchema.parse(req.body);
      
      const integrationConfig: IntegrationConfig = {
        id: `${validatedData.type}_${Date.now()}`,
        name: validatedData.name,
        type: validatedData.type,
        description: validatedData.description || '',
        version: '1.0.0',
        status: 'configuring',
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: req.user?.id || 'anonymous',
        auth: validatedData.auth,
        endpoints: validatedData.endpoints,
        dataMapping: {},
        settings: validatedData.settings || {},
        tags: validatedData.tags || [],
        category: validatedData.category,
        priority: validatedData.priority,
        enabled: false // 新创建的集成默认禁用，需要测试后启用
      };

      await integrationManager.registerIntegration(integrationConfig);

      const response: ApiResponse<IntegrationConfig> = {
        success: true,
        data: integrationConfig,
        message: '集成配置创建成功'
      };

      res.status(201).json(response);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.errors
        });
        return;
      }

      logger.error('Failed to create integration:', error);
      res.status(500).json({
        success: false,
        message: '创建集成配置失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 更新集成配置
   */
  async updateIntegration(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updates = req.body;

      const existing = integrationManager.getIntegration(id);
      if (!existing) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      await integrationManager.updateIntegration(id, updates);
      const updated = integrationManager.getIntegration(id);

      const response: ApiResponse<IntegrationConfig> = {
        success: true,
        data: updated!,
        message: '集成配置更新成功'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to update integration:', error);
      res.status(500).json({
        success: false,
        message: '更新集成配置失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 删除集成配置
   */
  async deleteIntegration(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const existing = integrationManager.getIntegration(id);
      if (!existing) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      await integrationManager.removeIntegration(id);

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: '集成配置删除成功'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to delete integration:', error);
      res.status(500).json({
        success: false,
        message: '删除集成配置失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // #endregion

  // #region 集成操作执行

  /**
   * 执行集成操作
   */
  async executeOperation(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const validatedData = ExecuteOperationSchema.parse(req.body);

      const integration = integrationManager.getIntegration(id);
      if (!integration) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      const request: IntegrationRequest = {
        integrationId: id,
        operation: validatedData.operation,
        endpoint: validatedData.endpoint,
        parameters: validatedData.parameters,
        headers: validatedData.headers,
        body: validatedData.body,
        timeout: validatedData.timeout
      };

      const context = {
        userId: req.user?.id || 'anonymous',
        userRole: req.user?.role?.name || 'user',
        permissions: req.user?.permissions || [],
        environment: process.env.NODE_ENV as 'development' | 'staging' | 'production' || 'development'
      };

      const result = await integrationManager.executeOperation(request, context);

      const response: ApiResponse<any> = {
        success: result.success,
        data: result.data,
        message: result.success ? '操作执行成功' : '操作执行失败'
      };

      res.json(response);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.errors
        });
        return;
      }

      logger.error('Failed to execute operation:', error);
      res.status(500).json({
        success: false,
        message: '执行操作失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 批量执行集成操作
   */
  async executeBatchOperations(req: Request, res: Response): Promise<void> {
    try {
      const batchRequest: BatchIntegrationRequest = req.body;

      const context = {
        userId: req.user?.id || 'anonymous',
        userRole: req.user?.role?.name || 'user',
        permissions: req.user?.permissions || [],
        environment: process.env.NODE_ENV as 'development' | 'staging' | 'production' || 'development'
      };

      const result = await integrationManager.executeBatchOperations(batchRequest, context);

      const response: ApiResponse<any> = {
        success: true,
        data: result,
        message: '批量操作执行完成'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to execute batch operations:', error);
      res.status(500).json({
        success: false,
        message: '批量操作执行失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // #endregion

  // #region 监控和健康检查

  /**
   * 获取集成监控指标
   */
  async getMetrics(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const integration = integrationManager.getIntegration(id);
      if (!integration) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      const metrics = await integrationManager.getMetrics(id);

      const response: ApiResponse<any> = {
        success: true,
        data: metrics,
        message: '获取监控指标成功'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to get metrics:', error);
      res.status(500).json({
        success: false,
        message: '获取监控指标失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 获取集成事件日志
   */
  async getEvents(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { limit = 100 } = req.query;

      const integration = integrationManager.getIntegration(id);
      if (!integration) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      const events = await integrationManager.getEvents(id, Number(limit));

      const response: ApiResponse<any> = {
        success: true,
        data: events,
        message: '获取事件日志成功'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to get events:', error);
      res.status(500).json({
        success: false,
        message: '获取事件日志失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const integration = integrationManager.getIntegration(id);
      if (!integration) {
        res.status(404).json({
          success: false,
          message: '集成配置不存在'
        });
        return;
      }

      const healthResult = await integrationManager.healthCheck(id);

      const response: ApiResponse<any> = {
        success: healthResult.healthy,
        data: healthResult,
        message: healthResult.healthy ? '健康检查通过' : '健康检查失败'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to perform health check:', error);
      res.status(500).json({
        success: false,
        message: '健康检查失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // #endregion

  // #region 通信集成专用接口

  /**
   * 发送消息
   */
  async sendMessage(req: Request, res: Response): Promise<void> {
    try {
      const validatedData = SendMessageSchema.parse(req.body);

      const result = await communicationIntegrationService.sendMessage(validatedData);

      const response: ApiResponse<any> = {
        success: result.success,
        data: result.data,
        message: result.success ? '消息发送成功' : '消息发送失败'
      };

      res.json(response);
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.errors
        });
        return;
      }

      logger.error('Failed to send message:', error);
      res.status(500).json({
        success: false,
        message: '消息发送失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 广播消息到多个平台
   */
  async broadcastMessage(req: Request, res: Response): Promise<void> {
    try {
      const { platforms, recipients, message } = req.body;

      if (!Array.isArray(platforms) || platforms.length === 0) {
        res.status(400).json({
          success: false,
          message: '平台列表不能为空'
        });
        return;
      }

      const results = await communicationIntegrationService.broadcastMessage(
        platforms,
        recipients,
        message
      );

      const response: ApiResponse<any> = {
        success: true,
        data: results,
        message: '广播消息完成'
      };

      res.json(response);
    } catch (error) {
      logger.error('Failed to broadcast message:', error);
      res.status(500).json({
        success: false,
        message: '广播消息失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 注册企业微信集成
   */
  async registerWeChatWork(req: Request, res: Response): Promise<void> {
    try {
      const { corpId, corpSecret, agentId, name } = req.body;

      if (!corpId || !corpSecret || !agentId) {
        res.status(400).json({
          success: false,
          message: 'corpId、corpSecret和agentId是必需的'
        });
        return;
      }

      await communicationIntegrationService.registerWeChatWork({
        corpId,
        corpSecret,
        agentId,
        name
      });

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: '企业微信集成注册成功'
      };

      res.status(201).json(response);
    } catch (error) {
      logger.error('Failed to register WeChat Work:', error);
      res.status(500).json({
        success: false,
        message: '企业微信集成注册失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 注册钉钉集成
   */
  async registerDingTalk(req: Request, res: Response): Promise<void> {
    try {
      const { appKey, appSecret, robotToken, name } = req.body;

      if (!appKey || !appSecret) {
        res.status(400).json({
          success: false,
          message: 'appKey和appSecret是必需的'
        });
        return;
      }

      await communicationIntegrationService.registerDingTalk({
        appKey,
        appSecret,
        robotToken,
        name
      });

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: '钉钉集成注册成功'
      };

      res.status(201).json(response);
    } catch (error) {
      logger.error('Failed to register DingTalk:', error);
      res.status(500).json({
        success: false,
        message: '钉钉集成注册失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 注册Slack集成
   */
  async registerSlack(req: Request, res: Response): Promise<void> {
    try {
      const { botToken, signingSecret, appId, name } = req.body;

      if (!botToken || !signingSecret) {
        res.status(400).json({
          success: false,
          message: 'botToken和signingSecret是必需的'
        });
        return;
      }

      await communicationIntegrationService.registerSlack({
        botToken,
        signingSecret,
        appId,
        name
      });

      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Slack集成注册成功'
      };

      res.status(201).json(response);
    } catch (error) {
      logger.error('Failed to register Slack:', error);
      res.status(500).json({
        success: false,
        message: 'Slack集成注册失败',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // #endregion
}

export const integrationController = new IntegrationController();