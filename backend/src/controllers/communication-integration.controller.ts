/**
 * 通信集成控制器
 * 提供通信集成管理的API接口
 */

import { Request, Response } from 'express';
import { communicationIntegrationService } from '../services/communication-integration.service';
import { logger } from '../utils/logger.util';
import { 
  CreateCommunicationIntegrationRequest,
  UpdateCommunicationIntegrationRequest,
  TestConnectionRequest,
  SendMessageRequest
} from '../types/communication-integration';

export class CommunicationIntegrationController {

  /**
   * 获取所有通信集成
   */
  async getAllIntegrations(req: Request, res: Response) {
    try {
      const integrations = await communicationIntegrationService.getAllIntegrations();
      res.json({
        success: true,
        data: integrations,
        message: '获取集成列表成功'
      });
    } catch (error) {
      logger.error('Failed to get all integrations:', error);
      res.status(500).json({
        success: false,
        message: '获取集成列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 根据类型获取集成
   */
  async getIntegrationsByType(req: Request, res: Response) {
    try {
      const { type } = req.params;
      if (!type) {
        return res.status(400).json({
          success: false,
          message: '缺少集成类型参数'
        });
      }

      const integrations = await communicationIntegrationService.getIntegrationsByType(type as any);
      res.json({
        success: true,
        data: integrations,
        message: '获取集成列表成功'
      });
    } catch (error) {
      logger.error('Failed to get integrations by type:', error);
      res.status(500).json({
        success: false,
        message: '获取集成列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取单个集成
   */
  async getIntegration(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          message: '缺少集成ID参数'
        });
      }

      const integration = await communicationIntegrationService.getIntegration(id);
      if (!integration) {
        return res.status(404).json({
          success: false,
          message: '集成不存在'
        });
      }

      res.json({
        success: true,
        data: integration,
        message: '获取集成配置成功'
      });
    } catch (error) {
      logger.error('Failed to get integration:', error);
      res.status(500).json({
        success: false,
        message: '获取集成配置失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 创建集成
   */
  async createIntegration(req: Request, res: Response) {
    try {
      const request: CreateCommunicationIntegrationRequest = req.body;
      const userId = (req as any).user?.id || 'system';

      // 验证请求数据
      if (!request.name || !request.type || !request.config) {
        return res.status(400).json({
          success: false,
          message: '缺少必要的请求参数'
        });
      }

      const integration = await communicationIntegrationService.createIntegration(request, userId);
      res.status(201).json({
        success: true,
        data: integration,
        message: '创建集成成功'
      });
    } catch (error) {
      logger.error('Failed to create integration:', error);
      res.status(500).json({
        success: false,
        message: '创建集成失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 更新集成
   */
  async updateIntegration(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const request: UpdateCommunicationIntegrationRequest = req.body;
      const userId = (req as any).user?.id || 'system';

      if (!id) {
        return res.status(400).json({
          success: false,
          message: '缺少集成ID参数'
        });
      }

      const integration = await communicationIntegrationService.updateIntegration(id, request, userId);
      res.json({
        success: true,
        data: integration,
        message: '更新集成成功'
      });
    } catch (error) {
      logger.error('Failed to update integration:', error);
      res.status(500).json({
        success: false,
        message: '更新集成失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 删除集成
   */
  async deleteIntegration(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          message: '缺少集成ID参数'
        });
      }

      await communicationIntegrationService.deleteIntegration(id);
      res.json({
        success: true,
        message: '删除集成成功'
      });
    } catch (error) {
      logger.error('Failed to delete integration:', error);
      res.status(500).json({
        success: false,
        message: '删除集成失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 测试连接
   */
  async testConnection(req: Request, res: Response) {
    try {
      const request: TestConnectionRequest = req.body;

      // 验证请求数据
      if (!request.type || !request.config) {
        return res.status(400).json({
          success: false,
          message: '缺少必要的请求参数'
        });
      }

      const result = await communicationIntegrationService.testConnection(request);
      res.json({
        success: true,
        data: result,
        message: '连接测试完成'
      });
    } catch (error) {
      logger.error('Failed to test connection:', error);
      res.status(500).json({
        success: false,
        message: '连接测试失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(req: Request, res: Response) {
    try {
      const request: SendMessageRequest = req.body;

      // 验证请求数据
      if (!request.integrationId || !request.message) {
        return res.status(400).json({
          success: false,
          message: '缺少必要的请求参数'
        });
      }

      const result = await communicationIntegrationService.sendMessage(request);
      res.json({
        success: true,
        data: result,
        message: '消息发送完成'
      });
    } catch (error) {
      logger.error('Failed to send message:', error);
      res.status(500).json({
        success: false,
        message: '消息发送失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(req: Request, res: Response) {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          message: '缺少集成ID参数'
        });
      }

      const result = await communicationIntegrationService.healthCheck(id);
      res.json({
        success: true,
        data: result,
        message: '健康检查完成'
      });
    } catch (error) {
      logger.error('Failed to perform health check:', error);
      res.status(500).json({
        success: false,
        message: '健康检查失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 启用/禁用集成
   */
  async toggleIntegration(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { enabled } = req.body;
      const userId = (req as any).user?.id || 'system';

      if (!id) {
        return res.status(400).json({
          success: false,
          message: '缺少集成ID参数'
        });
      }

      if (typeof enabled !== 'boolean') {
        return res.status(400).json({
          success: false,
          message: '缺少enabled参数或类型错误'
        });
      }

      await communicationIntegrationService.toggleIntegration(id, enabled, userId);
      res.json({
        success: true,
        message: `集成已${enabled ? '启用' : '禁用'}`
      });
    } catch (error) {
      logger.error('Failed to toggle integration:', error);
      res.status(500).json({
        success: false,
        message: '操作失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取集成指标
   */
  async getIntegrationMetrics(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { period = 'day', limit = '30' } = req.query;

      if (!id) {
        return res.status(400).json({
          success: false,
          message: '缺少集成ID参数'
        });
      }

      const metrics = await communicationIntegrationService.getIntegrationMetrics(
        id,
        period as string,
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: metrics,
        message: '获取指标成功'
      });
    } catch (error) {
      logger.error('Failed to get integration metrics:', error);
      res.status(500).json({
        success: false,
        message: '获取指标失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
}

export const communicationIntegrationController = new CommunicationIntegrationController();
export default communicationIntegrationController;
