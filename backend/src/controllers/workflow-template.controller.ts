/**
 * 工作流模板控制器
 * 处理工作流模板相关的HTTP请求
 */

import { Request, Response } from 'express';
import { workflowTemplateUnifiedService, WorkflowTemplate } from '../services/workflow-template-unified.service';
import { WorkflowCategory } from '../types/workflow';

interface TemplateQuery {
  category?: WorkflowCategory;
  subcategory?: string;
  search?: string;
  difficulty?: string;
  tags?: string;
  popular?: string;
  limit?: string;
  offset?: string;
  sort?: 'rating' | 'usage' | 'recent' | 'name';
  order?: 'asc' | 'desc';
}

interface ApplyTemplateRequest {
  templateId: string;
  customizations?: {
    name?: string;
    description?: string;
    variables?: Record<string, any>;
    settings?: any;
  };
}

interface RateTemplateRequest {
  rating: number; // 1-5
  comment?: string;
}

class WorkflowTemplateController {

  /**
   * 获取所有模板分类
   * GET /api/v1/workflow-templates/categories
   */
  async getCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = await workflowTemplateUnifiedService.getCategories();
      
      res.json({
        success: true,
        data: categories,
        message: '获取模板分类成功'
      });
    } catch (error) {
      console.error('获取模板分类失败:', error);
      res.status(500).json({
        success: false,
        message: '获取模板分类失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取模板列表
   * GET /api/v1/workflow-templates
   */
  async getTemplates(req: Request, res: Response): Promise<void> {
    try {
      const query = req.query as TemplateQuery;
      const { 
        category, 
        subcategory, 
        search, 
        difficulty, 
        tags, 
        popular, 
        limit = '20', 
        offset = '0',
        sort = 'rating',
        order = 'desc'
      } = query;

      let templates: WorkflowTemplate[] = [];

      // 使用统一的模板获取方法
      const params = {
        category,
        subcategory,
        search,
        difficulty,
        popular: popular === 'true',
        limit: parseInt(limit),
        offset: parseInt(offset),
        sort,
        order
      };

      const result = await workflowTemplateUnifiedService.getAllTemplates(params);
      templates = result.templates;
      
      // 使用数据库返回的分页信息
      const total = result.pagination.total;

      // 数据库已经处理了排序和分页
      const limitNum = parseInt(limit);
      const offsetNum = parseInt(offset);
      const paginatedTemplates = templates;

      // 简化模板数据，不返回完整的工作流定义
      const simplifiedTemplates = paginatedTemplates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        subcategory: template.subcategory,
        tags: template.tags,
        author: template.author,
        version: template.version,
        isBuiltin: template.isBuiltin,
        isPopular: template.isPopular,
        usageCount: template.usageCount,
        rating: template.rating,
        difficulty: template.difficulty,
        estimatedDuration: template.estimatedDuration,
        preview: template.preview,
        requirements: template.requirements,
        metadata: {
          ...template.metadata,
          stepsCount: template.workflow.steps.length,
          hasVariables: Object.keys(template.workflow.variables).length > 0
        }
      }));

      res.json({
        success: true,
        data: {
          templates: simplifiedTemplates,
          pagination: result.pagination
        },
        message: '获取模板列表成功'
      });
    } catch (error) {
      console.error('获取模板列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取模板列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取模板详情
   * GET /api/v1/workflow-templates/:templateId
   */
  async getTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { templateId } = req.params;
      
      const template = await workflowTemplateUnifiedService.getTemplate(templateId);
      if (!template) {
        res.status(404).json({
          success: false,
          message: '模板不存在'
        });
        return;
      }

      res.json({
        success: true,
        data: template,
        message: '获取模板详情成功'
      });
    } catch (error) {
      console.error('获取模板详情失败:', error);
      res.status(500).json({
        success: false,
        message: '获取模板详情失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 应用模板创建工作流
   * POST /api/v1/workflow-templates/:templateId/apply
   */
  async applyTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { templateId } = req.params;
      const { customizations } = req.body as ApplyTemplateRequest;
      
      const userId = (req as any).user?.id || 'system';
      const workflowDefinition = await workflowTemplateUnifiedService.applyTemplate(templateId, userId, customizations);
      
      res.json({
        success: true,
        data: {
          workflow: workflowDefinition,
          templateId,
          appliedAt: new Date().toISOString()
        },
        message: '模板应用成功'
      });
    } catch (error) {
      console.error('应用模板失败:', error);
      
      if (error instanceof Error && error.message.includes('Template not found')) {
        res.status(404).json({
          success: false,
          message: '模板不存在'
        });
        return;
      }

      res.status(500).json({
        success: false,
        message: '应用模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 搜索模板
   * GET /api/v1/workflow-templates/search
   */
  async searchTemplates(req: Request, res: Response): Promise<void> {
    try {
      const { q, category, difficulty, tags, limit = '10' } = req.query as Record<string, string>;
      
      if (!q || q.trim().length === 0) {
        res.status(400).json({
          success: false,
          message: '搜索关键词不能为空'
        });
        return;
      }

      const filters = {
        category: category as WorkflowCategory,
        difficulty,
        tags: tags ? tags.split(',') : undefined
      };

      const templates = await workflowTemplateUnifiedService.searchTemplates(q, filters);
      const limitedTemplates = templates.slice(0, parseInt(limit));

      // 简化返回数据
      const searchResults = limitedTemplates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        subcategory: template.subcategory,
        tags: template.tags,
        rating: template.rating,
        usageCount: template.usageCount,
        difficulty: template.difficulty,
        estimatedDuration: template.estimatedDuration,
        preview: {
          thumbnail: template.preview.thumbnail
        }
      }));

      res.json({
        success: true,
        data: {
          results: searchResults,
          total: templates.length,
          query: q,
          filters
        },
        message: '搜索完成'
      });
    } catch (error) {
      console.error('搜索模板失败:', error);
      res.status(500).json({
        success: false,
        message: '搜索模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取热门模板
   * GET /api/v1/workflow-templates/popular
   */
  async getPopularTemplates(req: Request, res: Response): Promise<void> {
    try {
      const { limit = '10' } = req.query as { limit?: string };
      
      const templates = await workflowTemplateUnifiedService.getPopularTemplates(parseInt(limit));
      
      const popularTemplates = templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        tags: template.tags,
        rating: template.rating,
        usageCount: template.usageCount,
        difficulty: template.difficulty,
        preview: {
          thumbnail: template.preview.thumbnail
        }
      }));

      res.json({
        success: true,
        data: popularTemplates,
        message: '获取热门模板成功'
      });
    } catch (error) {
      console.error('获取热门模板失败:', error);
      res.status(500).json({
        success: false,
        message: '获取热门模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取最新模板
   * GET /api/v1/workflow-templates/recent
   */
  async getRecentTemplates(req: Request, res: Response): Promise<void> {
    try {
      const { limit = '10' } = req.query as { limit?: string };
      
      const templates = await workflowTemplateUnifiedService.getRecentTemplates(parseInt(limit));
      
      const recentTemplates = templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        category: template.category,
        tags: template.tags,
        rating: template.rating,
        difficulty: template.difficulty,
        metadata: {
          updatedAt: template.metadata.updatedAt
        },
        preview: {
          thumbnail: template.preview.thumbnail
        }
      }));

      res.json({
        success: true,
        data: recentTemplates,
        message: '获取最新模板成功'
      });
    } catch (error) {
      console.error('获取最新模板失败:', error);
      res.status(500).json({
        success: false,
        message: '获取最新模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 模板评分
   * POST /api/v1/workflow-templates/:templateId/rate
   */
  async rateTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { templateId } = req.params;
      const { rating, comment } = req.body as RateTemplateRequest;
      
      if (!rating || rating < 1 || rating > 5) {
        res.status(400).json({
          success: false,
          message: '评分必须在1-5之间'
        });
        return;
      }

      const userId = (req as any).user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          message: '用户未认证'
        });
        return;
      }

      const template = await workflowTemplateUnifiedService.getTemplate(templateId);
      if (!template) {
        res.status(404).json({
          success: false,
          message: '模板不存在'
        });
        return;
      }

      await workflowTemplateUnifiedService.updateTemplateRating(templateId, userId, rating, comment);

      res.json({
        success: true,
        data: {
          templateId,
          rating,
          comment
        },
        message: '评分成功'
      });
    } catch (error) {
      console.error('模板评分失败:', error);
      res.status(500).json({
        success: false,
        message: '模板评分失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 导出模板
   * GET /api/v1/workflow-templates/:templateId/export
   */
  async exportTemplate(req: Request, res: Response): Promise<void> {
    try {
      const { templateId } = req.params;
      
      const template = await workflowTemplateUnifiedService.exportTemplate(templateId);
      if (!template) {
        res.status(404).json({
          success: false,
          message: '模板不存在'
        });
        return;
      }

      // 设置下载头
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="workflow-template-${templateId}.json"`);
      
      res.json({
        version: '1.0',
        exportedAt: new Date().toISOString(),
        template
      });
    } catch (error) {
      console.error('导出模板失败:', error);
      res.status(500).json({
        success: false,
        message: '导出模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 导入模板
   * POST /api/v1/workflow-templates/import
   */
  async importTemplate(req: Request, res: Response): Promise<void> {
    try {
      const templateData = req.body;
      
      // 验证导入数据结构
      if (!templateData.template || !templateData.template.name || !templateData.template.workflow) {
        res.status(400).json({
          success: false,
          message: '模板数据格式无效'
        });
        return;
      }

      const userId = (req as any).user?.id || 'system';
      const newTemplateId = await workflowTemplateUnifiedService.importTemplate(templateData.template, userId);

      res.json({
        success: true,
        data: {
          templateId: newTemplateId,
          importedAt: new Date().toISOString()
        },
        message: '模板导入成功'
      });
    } catch (error) {
      console.error('导入模板失败:', error);
      res.status(500).json({
        success: false,
        message: '导入模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 创建自定义模板
   * POST /api/v1/workflow-templates
   */
  async createCustomTemplate(req: Request, res: Response): Promise<void> {
    try {
      const templateData = req.body;
      
      // 验证必需字段
      if (!templateData.name || !templateData.workflow) {
        res.status(400).json({
          success: false,
          message: '缺少必需的模板数据'
        });
        return;
      }

      // 设置默认值
      const newTemplate = {
        name: templateData.name,
        description: templateData.description || '',
        category: templateData.category || 'CUSTOM' as WorkflowCategory,
        subcategory: templateData.subcategory || 'user_created',
        tags: templateData.tags || [],
        author: (req as any).user?.fullName || 'Unknown', // 从认证中间件获取用户信息
        version: '1.0.0',
        isBuiltin: false,
        isPopular: false,
        usageCount: 0,
        rating: 0,
        difficulty: templateData.difficulty || 'beginner',
        estimatedDuration: templateData.estimatedDuration || 5,
        preview: {
          thumbnail: templateData.preview?.thumbnail || '/templates/default-thumb.png',
          screenshots: templateData.preview?.screenshots || []
        },
        requirements: templateData.requirements || {
          permissions: [],
          integrations: [],
          minimumVersion: '1.0.0'
        },
        workflow: templateData.workflow
      };

      const userId = (req as any).user?.id || 'system';
      const templateId = await workflowTemplateUnifiedService.createCustomTemplate({
        name: newTemplate.name,
        description: newTemplate.description,
        category: newTemplate.category,
        subcategory: newTemplate.subcategory,
        tags: newTemplate.tags,
        difficulty: newTemplate.difficulty,
        estimatedDuration: newTemplate.estimatedDuration,
        workflow: newTemplate.workflow
      }, userId);

      res.status(201).json({
        success: true,
        data: {
          templateId,
          createdAt: new Date().toISOString()
        },
        message: '自定义模板创建成功'
      });
    } catch (error) {
      console.error('创建自定义模板失败:', error);
      res.status(500).json({
        success: false,
        message: '创建自定义模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 获取模板统计信息
   * GET /api/v1/workflow-templates/stats
   */
  async getTemplateStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await workflowTemplateUnifiedService.getTemplateStats();

      res.json({
        success: true,
        data: stats,
        message: '获取模板统计信息成功'
      });
    } catch (error) {
      console.error('获取模板统计信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取模板统计信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 根据分类获取模板
   * GET /api/v1/workflow-templates/category/:category
   */
  async getTemplatesByCategory(req: Request, res: Response): Promise<void> {
    try {
      const { category } = req.params;
      const templates = await workflowTemplateUnifiedService.getTemplatesByCategory(
        category as WorkflowCategory
      );

      res.json({
        success: true,
        data: templates,
        message: '获取分类模板成功'
      });
    } catch (error) {
      console.error('获取分类模板失败:', error);
      res.status(500).json({
        success: false,
        message: '获取分类模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }

  /**
   * 根据子分类获取模板
   * GET /api/v1/workflow-templates/category/:category/subcategory/:subcategory
   */
  async getTemplatesBySubcategory(req: Request, res: Response): Promise<void> {
    try {
      const { category, subcategory } = req.params;
      const templates = await workflowTemplateUnifiedService.getTemplatesBySubcategory(
        category as WorkflowCategory,
        subcategory
      );

      res.json({
        success: true,
        data: templates,
        message: '获取子分类模板成功'
      });
    } catch (error) {
      console.error('获取子分类模板失败:', error);
      res.status(500).json({
        success: false,
        message: '获取子分类模板失败',
        error: error instanceof Error ? error.message : '未知错误'
      });
    }
  }
}

export const workflowTemplateController = new WorkflowTemplateController();
export default workflowTemplateController;