/**
 * 外部API集成系统类型定义
 * 提供统一的外部系统集成接口和数据结构
 */

// 集成类型枚举
export enum IntegrationType {
  // 云服务
  CLOUD_ALIYUN = 'cloud_aliyun',
  CLOUD_TENCENT = 'cloud_tencent',
  CLOUD_HUAWEI = 'cloud_huawei',
  CLOUD_AWS = 'cloud_aws',
  CLOUD_AZURE = 'cloud_azure',
  
  // 监控系统
  MONITOR_PROMETHEUS = 'monitor_prometheus',
  MONITOR_GRAFANA = 'monitor_grafana',
  MONITOR_ZABBIX = 'monitor_zabbix',
  MONITOR_NAGIOS = 'monitor_nagios',
  MONITOR_DATADOG = 'monitor_datadog',
  
  // 通信平台
  CHAT_WECHAT_WORK = 'chat_wechat_work',
  CHAT_DINGTALK = 'chat_dingtalk',
  CHAT_SLACK = 'chat_slack',
  CHAT_TEAMS = 'chat_teams',
  
  // DevOps工具
  DEVOPS_GITLAB = 'devops_gitlab',
  DEVOPS_GITHUB = 'devops_github',
  DEVOPS_JENKINS = 'devops_jenkins',
  DEVOPS_DOCKER = 'devops_docker',
  DEVOPS_KUBERNETES = 'devops_kubernetes',
  
  // 数据库
  DATABASE_MYSQL = 'database_mysql',
  DATABASE_REDIS = 'database_redis',
  DATABASE_MONGODB = 'database_mongodb',
  DATABASE_POSTGRESQL = 'database_postgresql',
  
  // 日志系统
  LOG_ELK = 'log_elk',
  LOG_FLUENTD = 'log_fluentd',
  LOG_SPLUNK = 'log_splunk',
  
  // ITSM系统
  ITSM_SERVICENOW = 'itsm_servicenow',
  ITSM_JIRA = 'itsm_jira'
}

// 认证类型枚举
export enum AuthType {
  API_KEY = 'api_key',
  OAUTH2 = 'oauth2',
  BASIC_AUTH = 'basic_auth',
  TOKEN = 'token',
  JWT = 'jwt',
  CERTIFICATE = 'certificate'
}

// 集成状态枚举
export enum IntegrationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  TESTING = 'testing',
  CONFIGURING = 'configuring'
}

// 操作类型枚举
export enum OperationType {
  // 查询操作
  QUERY = 'query',
  GET = 'get',
  LIST = 'list',
  SEARCH = 'search',
  
  // 修改操作
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  
  // 控制操作
  START = 'start',
  STOP = 'stop',
  RESTART = 'restart',
  PAUSE = 'pause',
  RESUME = 'resume',
  
  // 通知操作
  NOTIFY = 'notify',
  ALERT = 'alert',
  MESSAGE = 'message',
  
  // 监控操作
  MONITOR = 'monitor',
  METRICS = 'metrics',
  HEALTH_CHECK = 'health_check'
}

// 基础集成配置接口
export interface BaseIntegrationConfig {
  id: string;
  name: string;
  type: IntegrationType;
  description?: string;
  version: string;
  status: IntegrationStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastTestedAt?: Date;
  lastErrorAt?: Date;
  lastErrorMessage?: string;
}

// 认证配置接口
export interface AuthConfig {
  type: AuthType;
  credentials: Record<string, any>;
  expiresAt?: Date;
  refreshToken?: string;
  scope?: string[];
}

// API端点配置
export interface ApiEndpoint {
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
  rateLimit?: {
    requests: number;
    window: number; // milliseconds
  };
}

// 数据映射配置
export interface DataMapping {
  source: string;
  target: string;
  transform?: string; // JavaScript expression
  defaultValue?: any;
  required?: boolean;
}

// 完整的集成配置
export interface IntegrationConfig extends BaseIntegrationConfig {
  auth: AuthConfig;
  endpoints: Record<string, ApiEndpoint>;
  dataMapping: Record<string, DataMapping[]>;
  settings: Record<string, any>;
  tags: string[];
  category: string;
  priority: number;
  enabled: boolean;
}

// 集成操作请求
export interface IntegrationRequest {
  integrationId: string;
  operation: OperationType;
  endpoint: string;
  parameters?: Record<string, any>;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  metadata?: Record<string, any>;
}

// 集成操作响应
export interface IntegrationResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    requestId: string;
    timestamp: Date;
    duration: number;
    endpoint: string;
    statusCode?: number;
  };
}

// 集成监控指标
export interface IntegrationMetrics {
  integrationId: string;
  timestamp: Date;
  requests: {
    total: number;
    success: number;
    failed: number;
    avgResponseTime: number;
    maxResponseTime: number;
  };
  errors: {
    total: number;
    byType: Record<string, number>;
  };
  availability: number; // 0-1
  lastHealthCheck: Date;
  healthStatus: 'healthy' | 'warning' | 'critical';
}

// 集成事件
export interface IntegrationEvent {
  id: string;
  integrationId: string;
  type: 'success' | 'error' | 'warning' | 'info';
  operation: OperationType;
  message: string;
  details?: any;
  timestamp: Date;
  resolved?: boolean;
  resolvedAt?: Date;
}

// 批量操作请求
export interface BatchIntegrationRequest {
  requests: IntegrationRequest[];
  parallel?: boolean;
  continueOnError?: boolean;
  timeout?: number;
}

// 批量操作响应
export interface BatchIntegrationResponse {
  results: IntegrationResponse[];
  summary: {
    total: number;
    success: number;
    failed: number;
    duration: number;
  };
}

// Webhook配置
export interface WebhookConfig {
  url: string;
  secret?: string;
  events: string[];
  headers?: Record<string, string>;
  retries?: number;
  timeout?: number;
  enabled: boolean;
}

// 集成模板
export interface IntegrationTemplate {
  id: string;
  name: string;
  type: IntegrationType;
  description: string;
  version: string;
  author: string;
  category: string;
  tags: string[];
  configSchema: any; // JSON Schema
  defaultConfig: Partial<IntegrationConfig>;
  documentation: {
    setup: string;
    usage: string;
    examples: any[];
  };
  requirements: {
    permissions: string[];
    dependencies: string[];
  };
}

// 集成上下文
export interface IntegrationContext {
  userId: string;
  userRole: string;
  permissions: string[];
  organization?: string;
  environment: 'development' | 'staging' | 'production';
  correlationId?: string;
  metadata?: Record<string, any>;
}

// 集成执行结果
export interface IntegrationExecution {
  id: string;
  integrationId: string;
  operation: OperationType;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startedAt: Date;
  completedAt?: Date;
  duration?: number;
  request: IntegrationRequest;
  response?: IntegrationResponse;
  error?: {
    code: string;
    message: string;
    stack?: string;
  };
  context: IntegrationContext;
}