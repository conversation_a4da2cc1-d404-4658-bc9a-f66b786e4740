/**
 * 工作流相关类型定义
 */

export type WorkflowCategory = 
  | 'SERVICE_AUTOMATION'
  | 'SLA_MONITORING'
  | 'ALERT_PROCESSING'
  | 'BACKUP_AUTOMATION'
  | 'MAINTENANCE'
  | 'APPROVAL_PROCESS'
  | 'NOTIFICATION'
  | 'DATA_PROCESSING'
  | 'INTEGRATION'
  | 'CUSTOM';

export type WorkflowPriority = 
  | 'LOW'
  | 'MEDIUM'
  | 'HIGH'
  | 'URGENT'
  | 'CRITICAL';

export type WorkflowStepType =
  | 'HTTP_REQUEST'
  | 'DATABASE_QUERY'
  | 'EMAIL_NOTIFICATION'
  | 'SMS_NOTIFICATION'
  | 'WEBHOOK'
  | 'SCRIPT_EXECUTION'
  | 'WAIT_DELAY'
  | 'CONDITION_CHECK'
  | 'APPROVAL_REQUEST'
  | 'FILE_OPERATION'
  | 'API_CALL'
  | 'CUSTOM_ACTION';

export type TriggerType =
  | 'MANUAL'
  | 'SCHEDULED'
  | 'EVENT'
  | 'CONDITIONAL'
  | 'WEBHOOK'
  | 'API';

export interface WorkflowStep {
  id: string;
  name: string;
  type: WorkflowStepType;
  description?: string;
  config: Record<string, any>;
  timeout?: number;
  retries?: number;
  order: number;
  dependencies?: string[];
  conditions?: Record<string, any>;
}

export interface WorkflowTrigger {
  type: TriggerType;
  config: Record<string, any>;
  conditions?: Record<string, any>;
  schedule?: string; // cron expression for scheduled triggers
  enabled: boolean;
}

export interface WorkflowDefinition {
  id?: string;
  name: string;
  description?: string;
  category: WorkflowCategory;
  version: string;
  isActive: boolean;
  isTemplate: boolean;
  priority: WorkflowPriority;
  
  // Configuration
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  conditions?: Record<string, any>;
  variables: Record<string, any>;
  settings: {
    timeout?: number;
    maxRetries?: number;
    errorHandling?: 'stop' | 'continue' | 'rollback';
    notifications?: string[];
  };
  
  // Metadata
  tags?: string[];
  metadata?: Record<string, any>;
  
  // Statistics
  executionCount?: number;
  successCount?: number;
  failureCount?: number;
  lastExecutedAt?: string;
  avgExecutionTime?: number;
  
  // Audit
  createdBy: string;
  updatedBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export type WorkflowExecutionStatus =
  | 'PENDING'
  | 'RUNNING'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'PAUSED'
  | 'WAITING';

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  executionId: string;
  status: WorkflowExecutionStatus;
  priority: WorkflowPriority;
  
  // Trigger information
  triggeredBy: TriggerType;
  triggerData?: Record<string, any>;
  triggeredAt: string;
  
  // Execution information
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  
  // Step execution data
  currentStep?: string;
  stepResults: Record<string, any>;
  executionLog: any[];
  
  // Error information
  error?: string;
  errorDetails?: Record<string, any>;
  
  // Context
  variables: Record<string, any>;
  metadata?: Record<string, any>;
  
  // Audit
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStepExecution {
  id: string;
  executionId: string;
  stepId: string;
  stepName: string;
  stepType: WorkflowStepType;
  status: WorkflowExecutionStatus;
  
  // Execution timing
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  
  // Input/Output
  input?: Record<string, any>;
  output?: Record<string, any>;
  error?: string;
  
  // Retry information
  retryCount: number;
  maxRetries: number;
  
  // Audit
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStats {
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  
  categoryDistribution: Record<WorkflowCategory, number>;
  statusDistribution: Record<WorkflowExecutionStatus, number>;
  
  recentExecutions: WorkflowExecution[];
  mostUsedWorkflows: Array<{
    id: string;
    name: string;
    executionCount: number;
  }>;
}

// API Request/Response types
export interface CreateWorkflowRequest {
  name: string;
  description?: string;
  category: WorkflowCategory;
  priority?: WorkflowPriority;
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  variables?: Record<string, any>;
  settings?: WorkflowDefinition['settings'];
  tags?: string[];
  isTemplate?: boolean;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  category?: WorkflowCategory;
  priority?: WorkflowPriority;
  trigger?: WorkflowTrigger;
  steps?: WorkflowStep[];
  variables?: Record<string, any>;
  settings?: WorkflowDefinition['settings'];
  tags?: string[];
  isActive?: boolean;
}

export interface ExecuteWorkflowRequest {
  workflowId?: string;
  triggerData?: Record<string, any>;
  variables?: Record<string, any>;
  priority?: WorkflowPriority;
}

export interface WorkflowListQuery {
  page?: number;
  limit?: number;
  category?: WorkflowCategory;
  isActive?: boolean;
  search?: string;
  tags?: string;
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'executionCount';
  sortOrder?: 'asc' | 'desc';
}

export interface ExecutionListQuery {
  page?: number;
  limit?: number;
  workflowId?: string;
  status?: WorkflowExecutionStatus;
  triggerType?: TriggerType;
  startDate?: string;
  endDate?: string;
  sortBy?: 'createdAt' | 'duration' | 'status';
  sortOrder?: 'asc' | 'desc';
}