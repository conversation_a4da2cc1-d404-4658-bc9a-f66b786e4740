/**
 * 通信集成类型定义
 */

// 通信平台类型
export enum CommunicationPlatform {
  WECHAT_WORK = 'wechat_work',
  DINGTALK = 'dingtalk',
  SLACK = 'slack',
  FEISHU = 'feishu'
}

// 集成状态
export enum IntegrationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  TESTING = 'testing',
  CONFIGURING = 'configuring'
}

// 健康状态
export enum HealthStatus {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  CRITICAL = 'critical'
}

// 执行状态
export enum ExecutionStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  RUNNING = 'running'
}

// 操作类型
export enum OperationType {
  TEST = 'test',
  SEND_MESSAGE = 'send_message',
  HEALTH_CHECK = 'health_check',
  USER_SYNC = 'user_sync'
}

// 企业微信配置
export interface WeChatWorkConfig {
  corpId: string;
  agentId: string;
  secret: string;
  token?: string;
  encodingAESKey?: string;
  webhookUrl?: string;
  settings: {
    enableMessagePush: boolean;
    enableFileUpload: boolean;
    enableUserSync: boolean;
    messageTemplate: string;
    retryCount: number;
    timeout: number;
  };
}

// 钉钉配置
export interface DingTalkConfig {
  appKey: string;
  appSecret: string;
  agentId: string;
  corpId: string;
  webhookUrl?: string;
  settings: {
    enableMessagePush: boolean;
    enableFileUpload: boolean;
    enableUserSync: boolean;
    messageTemplate: string;
    retryCount: number;
    timeout: number;
    enableAtAll: boolean;
    enableAtUsers: boolean;
  };
}

// Slack配置
export interface SlackConfig {
  botToken: string;
  signingSecret: string;
  appToken?: string;
  defaultChannel: string;
  webhookUrl?: string;
  settings: {
    enableMessagePush: boolean;
    enableFileUpload: boolean;
    enableUserSync: boolean;
    messageTemplate: string;
    retryCount: number;
    timeout: number;
    enableThreading: boolean;
    enableReactions: boolean;
    defaultUsername: string;
    defaultIcon?: string;
  };
}

// 飞书配置
export interface FeishuConfig {
  appId: string;
  appSecret: string;
  appTicket?: string;
  verificationToken?: string;
  encryptKey?: string;
  webhookUrl?: string;
  settings: {
    enableMessagePush: boolean;
    enableFileUpload: boolean;
    enableUserSync: boolean;
    messageTemplate: string;
    retryCount: number;
    timeout: number;
    enableEncryption: boolean;
    enableVerification: boolean;
    defaultChatId?: string;
  };
}

// 通用配置类型
export type CommunicationConfig = 
  | { type: CommunicationPlatform.WECHAT_WORK; config: WeChatWorkConfig }
  | { type: CommunicationPlatform.DINGTALK; config: DingTalkConfig }
  | { type: CommunicationPlatform.SLACK; config: SlackConfig }
  | { type: CommunicationPlatform.FEISHU; config: FeishuConfig };

// 通信集成实体
export interface CommunicationIntegration {
  id: string;
  name: string;
  type: CommunicationPlatform;
  description?: string;
  enabled: boolean;
  priority: number;
  status: IntegrationStatus;
  config: WeChatWorkConfig | DingTalkConfig | SlackConfig | FeishuConfig;
  lastHealthCheck?: Date;
  healthStatus?: HealthStatus;
  lastErrorAt?: Date;
  lastErrorMessage?: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 创建集成请求
export interface CreateCommunicationIntegrationRequest {
  name: string;
  type: CommunicationPlatform;
  description?: string;
  enabled?: boolean;
  priority?: number;
  config: WeChatWorkConfig | DingTalkConfig | SlackConfig | FeishuConfig;
}

// 更新集成请求
export interface UpdateCommunicationIntegrationRequest {
  name?: string;
  description?: string;
  enabled?: boolean;
  priority?: number;
  config?: WeChatWorkConfig | DingTalkConfig | SlackConfig | FeishuConfig;
}

// 测试连接请求
export interface TestConnectionRequest {
  type: CommunicationPlatform;
  config: WeChatWorkConfig | DingTalkConfig | SlackConfig | FeishuConfig;
}

// 测试连接响应
export interface TestConnectionResponse {
  success: boolean;
  message: string;
  details?: {
    responseTime: number;
    platform: string;
    timestamp: string;
    errorCode?: string;
  };
}

// 发送消息请求
export interface SendMessageRequest {
  integrationId: string;
  message: string;
  recipients?: string[];
  channel?: string;
  attachments?: Array<{
    name: string;
    content: string;
    type: string;
  }>;
}

// 发送消息响应
export interface SendMessageResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

// 集成执行记录
export interface CommunicationIntegrationExecution {
  id: string;
  integrationId: string;
  operation: OperationType;
  status: ExecutionStatus;
  requestData?: any;
  responseData?: any;
  errorMessage?: string;
  duration?: number;
  startedAt: Date;
  completedAt?: Date;
}

// 集成监控指标
export interface CommunicationIntegrationMetrics {
  id: string;
  integrationId: string;
  timestamp: Date;
  period: string;
  totalRequests: number;
  successRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  maxResponseTime: number;
  minResponseTime: number;
  errorCount: number;
  errorTypes?: Record<string, number>;
  availability: number;
}

// 健康检查响应
export interface HealthCheckResponse {
  healthy: boolean;
  details?: {
    responseTime: number;
    errorCount: number;
    lastError?: string;
    platform: string;
  };
}

// 用户同步请求
export interface UserSyncRequest {
  integrationId: string;
  syncType: 'full' | 'incremental';
  options?: {
    departments?: string[];
    roles?: string[];
    activeOnly?: boolean;
  };
}

// 用户同步响应
export interface UserSyncResponse {
  success: boolean;
  syncedUsers: number;
  errors?: Array<{
    userId: string;
    error: string;
  }>;
}
