/**
 * 工作流模板服务
 * 提供预置工作流模板的管理和应用功能
 */

import { 
  WorkflowDefinition,
  WorkflowStep,
  WorkflowStepType,
  WorkflowCategory,
  TriggerType 
} from '../types/workflow';

/**
 * 工作流模板接口
 */
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  subcategory: string;
  tags: string[];
  author: string;
  version: string;
  isBuiltin: boolean;
  isPopular: boolean;
  usageCount: number;
  rating: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // 预计执行时间（分钟）
  preview: {
    thumbnail: string;
    screenshots: string[];
    demoVideo?: string;
  };
  requirements: {
    permissions: string[];
    integrations: string[];
    minimumVersion: string;
  };
  workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
  metadata: {
    createdAt: string;
    updatedAt: string;
    downloads: number;
    lastUsed?: string;
  };
}

/**
 * 模板分类配置
 */
export interface TemplateCategory {
  id: WorkflowCategory;
  name: string;
  description: string;
  icon: string;
  subcategories: Array<{
    id: string;
    name: string;
    description: string;
  }>;
}

class WorkflowTemplateService {
  private templates: Map<string, WorkflowTemplate> = new Map();
  private categories: TemplateCategory[] = [];

  constructor() {
    this.initializeCategories();
    this.initializeBuiltinTemplates();
  }

  /**
   * 初始化模板分类
   */
  private initializeCategories(): void {
    this.categories = [
      {
        id: 'SERVICE_AUTOMATION',
        name: '服务自动化',
        description: '自动化服务管理和运维任务',
        icon: '🔧',
        subcategories: [
          { id: 'deployment', name: '部署自动化', description: '应用部署和更新流程' },
          { id: 'scaling', name: '弹性伸缩', description: '资源自动伸缩管理' },
          { id: 'restart', name: '服务重启', description: '服务异常自动恢复' },
          { id: 'health_check', name: '健康检查', description: '服务健康状态监控' }
        ]
      },
      {
        id: 'SLA_MONITORING',
        name: 'SLA监控',
        description: 'SLA指标监控和违规处理',
        icon: '📊',
        subcategories: [
          { id: 'availability', name: '可用性监控', description: '服务可用性指标跟踪' },
          { id: 'performance', name: '性能监控', description: '响应时间和吞吐量监控' },
          { id: 'violation', name: '违规处理', description: 'SLA违规自动响应流程' },
          { id: 'reporting', name: '报告生成', description: 'SLA报告自动生成' }
        ]
      },
      {
        id: 'ALERT_PROCESSING',
        name: '告警处理',
        description: '智能告警处理和响应',
        icon: '🚨',
        subcategories: [
          { id: 'escalation', name: '告警升级', description: '告警逐级升级处理' },
          { id: 'correlation', name: '告警关联', description: '相关告警自动关联' },
          { id: 'suppression', name: '告警抑制', description: '重复告警智能过滤' },
          { id: 'notification', name: '通知分发', description: '多渠道告警通知' }
        ]
      },
      {
        id: 'BACKUP_AUTOMATION',
        name: '备份自动化',
        description: '数据备份和恢复流程',
        icon: '💾',
        subcategories: [
          { id: 'database', name: '数据库备份', description: '数据库定期备份' },
          { id: 'file_backup', name: '文件备份', description: '重要文件系统备份' },
          { id: 'disaster_recovery', name: '灾难恢复', description: '灾难恢复流程' },
          { id: 'cleanup', name: '备份清理', description: '过期备份自动清理' }
        ]
      },
      {
        id: 'MAINTENANCE',
        name: '维护任务',
        description: '系统维护和优化任务',
        icon: '🔧',
        subcategories: [
          { id: 'patch_management', name: '补丁管理', description: '系统补丁自动更新' },
          { id: 'cleanup', name: '系统清理', description: '日志和临时文件清理' },
          { id: 'optimization', name: '性能优化', description: '系统性能调优' },
          { id: 'security_scan', name: '安全扫描', description: '安全漏洞扫描' }
        ]
      },
      {
        id: 'APPROVAL_PROCESS',
        name: '审批流程',
        description: '运维操作审批流程',
        icon: '✅',
        subcategories: [
          { id: 'change_request', name: '变更请求', description: '系统变更审批流程' },
          { id: 'access_request', name: '权限申请', description: '系统访问权限审批' },
          { id: 'resource_request', name: '资源申请', description: '计算资源申请审批' },
          { id: 'emergency', name: '紧急操作', description: '紧急操作快速审批' }
        ]
      },
      {
        id: 'NOTIFICATION',
        name: '通知流程',
        description: '多渠道通知和消息推送',
        icon: '📢',
        subcategories: [
          { id: 'incident', name: '事件通知', description: '故障事件通知流程' },
          { id: 'maintenance', name: '维护通知', description: '系统维护通知' },
          { id: 'report', name: '报告推送', description: '定期报告推送' },
          { id: 'status_update', name: '状态更新', description: '服务状态变更通知' }
        ]
      },
      {
        id: 'DATA_PROCESSING',
        name: '数据处理',
        description: '数据采集、处理和分析',
        icon: '📈',
        subcategories: [
          { id: 'collection', name: '数据采集', description: '系统数据自动采集' },
          { id: 'analysis', name: '数据分析', description: '运维数据分析处理' },
          { id: 'migration', name: '数据迁移', description: '数据迁移和同步' },
          { id: 'archival', name: '数据归档', description: '历史数据归档处理' }
        ]
      },
      {
        id: 'INTEGRATION',
        name: '集成流程',
        description: '第三方系统集成',
        icon: '🔗',
        subcategories: [
          { id: 'api_sync', name: 'API同步', description: '第三方API数据同步' },
          { id: 'webhook', name: 'Webhook处理', description: 'Webhook事件处理' },
          { id: 'file_transfer', name: '文件传输', description: 'FTP/SFTP文件传输' },
          { id: 'message_queue', name: '消息队列', description: '消息队列处理' }
        ]
      },
      {
        id: 'CUSTOM',
        name: '自定义模板',
        description: '用户自定义工作流模板',
        icon: '🎨',
        subcategories: [
          { id: 'user_created', name: '用户创建', description: '用户自定义模板' },
          { id: 'organization', name: '组织模板', description: '组织内部模板' },
          { id: 'imported', name: '导入模板', description: '外部导入模板' }
        ]
      }
    ];
  }

  /**
   * 初始化内置模板
   */
  private initializeBuiltinTemplates(): void {
    const builtinTemplates: WorkflowTemplate[] = [
      // 服务自动化模板
      {
        id: 'auto-deployment',
        name: '自动部署流程',
        description: '应用程序自动部署和健康检查',
        category: 'SERVICE_AUTOMATION',
        subcategory: 'deployment',
        tags: ['部署', '自动化', 'CI/CD', '健康检查'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        usageCount: 150,
        rating: 4.8,
        difficulty: 'intermediate',
        estimatedDuration: 15,
        preview: {
          thumbnail: '/templates/auto-deployment-thumb.png',
          screenshots: ['/templates/auto-deployment-1.png'],
          demoVideo: '/templates/auto-deployment-demo.mp4'
        },
        requirements: {
          permissions: ['deployment.manage', 'service.restart'],
          integrations: ['docker', 'kubernetes'],
          minimumVersion: '1.0.0'
        },
        workflow: {
          name: '自动部署流程',
          description: '从代码提交到生产部署的完整自动化流程',
          category: 'SERVICE_AUTOMATION',
          priority: 'MEDIUM',
          version: '1.0.0',
          isActive: true,
          steps: this.createAutoDeploymentSteps(),
          variables: {
            service_name: '',
            deployment_env: 'production',
            docker_image: '',
            health_check_url: '',
            rollback_enabled: true
          },
          settings: {
            timeout: 1800000, // 30分钟
            maxRetries: 3,
            errorHandling: 'rollback',
            notifications: ['<EMAIL>'],
            concurrency: { enabled: false, maxParallel: 1 },
            logging: { level: 'INFO', retentionDays: 30 }
          },
          tags: ['deployment', 'automation']
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          downloads: 150
        }
      },

      // SLA监控模板
      {
        id: 'sla-violation-response',
        name: 'SLA违规响应',
        description: 'SLA违规自动检测和响应处理',
        category: 'SLA_MONITORING',
        subcategory: 'violation',
        tags: ['SLA', '监控', '告警', '自动响应'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        usageCount: 89,
        rating: 4.6,
        difficulty: 'advanced',
        estimatedDuration: 5,
        preview: {
          thumbnail: '/templates/sla-violation-thumb.png',
          screenshots: ['/templates/sla-violation-1.png']
        },
        requirements: {
          permissions: ['sla.monitor', 'alert.create'],
          integrations: ['monitoring', 'notification'],
          minimumVersion: '1.0.0'
        },
        workflow: {
          name: 'SLA违规响应',
          description: 'SLA指标违规时的自动响应和升级流程',
          category: 'SLA_MONITORING',
          priority: 'HIGH',
          version: '1.0.0',
          isActive: true,
          steps: this.createSLAViolationSteps(),
          variables: {
            sla_threshold: 95,
            escalation_level: 1,
            notification_channels: ['email', 'sms', 'slack']
          },
          settings: {
            timeout: 300000, // 5分钟
            maxRetries: 1,
            errorHandling: 'continue',
            notifications: ['<EMAIL>'],
            concurrency: { enabled: true, maxParallel: 3 },
            logging: { level: 'DEBUG', retentionDays: 90 }
          },
          tags: ['sla', 'monitoring', 'alert']
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          downloads: 89
        }
      },

      // 告警处理模板
      {
        id: 'alert-escalation',
        name: '告警升级流程',
        description: '多级告警升级和通知流程',
        category: 'ALERT_PROCESSING',
        subcategory: 'escalation',
        tags: ['告警', '升级', '通知', 'oncall'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        usageCount: 124,
        rating: 4.7,
        difficulty: 'intermediate',
        estimatedDuration: 10,
        preview: {
          thumbnail: '/templates/alert-escalation-thumb.png',
          screenshots: ['/templates/alert-escalation-1.png']
        },
        requirements: {
          permissions: ['alert.manage', 'notification.send'],
          integrations: ['alerting', 'oncall'],
          minimumVersion: '1.0.0'
        },
        workflow: {
          name: '告警升级流程',
          description: '根据告警严重程度和响应时间进行逐级升级',
          category: 'ALERT_PROCESSING',
          priority: 'HIGH',
          version: '1.0.0',
          isActive: true,
          steps: this.createAlertEscalationSteps(),
          variables: {
            alert_severity: 'HIGH',
            escalation_delay: 900, // 15分钟
            max_escalation_level: 3,
            oncall_rotation: 'primary'
          },
          settings: {
            timeout: 3600000, // 1小时
            maxRetries: 2,
            errorHandling: 'continue',
            notifications: ['<EMAIL>'],
            concurrency: { enabled: false, maxParallel: 1 },
            logging: { level: 'INFO', retentionDays: 60 }
          },
          tags: ['alert', 'escalation', 'oncall']
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          downloads: 124
        }
      },

      // 备份自动化模板
      {
        id: 'database-backup',
        name: '数据库定时备份',
        description: '数据库定时备份和验证流程',
        category: 'BACKUP_AUTOMATION',
        subcategory: 'database',
        tags: ['备份', '数据库', '定时任务', '验证'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        usageCount: 203,
        rating: 4.9,
        difficulty: 'beginner',
        estimatedDuration: 30,
        preview: {
          thumbnail: '/templates/db-backup-thumb.png',
          screenshots: ['/templates/db-backup-1.png']
        },
        requirements: {
          permissions: ['database.backup', 'storage.write'],
          integrations: ['database', 'storage'],
          minimumVersion: '1.0.0'
        },
        workflow: {
          name: '数据库定时备份',
          description: '定期执行数据库备份并验证备份完整性',
          category: 'BACKUP_AUTOMATION',
          priority: 'MEDIUM',
          version: '1.0.0',
          isActive: true,
          steps: this.createDatabaseBackupSteps(),
          variables: {
            database_type: 'mysql',
            backup_location: '/backup/db',
            retention_days: 30,
            compression_enabled: true
          },
          settings: {
            timeout: 7200000, // 2小时
            maxRetries: 2,
            errorHandling: 'stop',
            notifications: ['<EMAIL>'],
            concurrency: { enabled: false, maxParallel: 1 },
            logging: { level: 'INFO', retentionDays: 90 }
          },
          tags: ['backup', 'database', 'scheduled']
        },
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          downloads: 203
        }
      }
    ];

    builtinTemplates.forEach(template => {
      this.templates.set(template.id, template);
    });
  }

  /**
   * 创建自动部署步骤
   */
  private createAutoDeploymentSteps(): WorkflowStep[] {
    return [
      {
        index: 0,
        name: '获取部署参数',
        type: 'ACTION',
        description: '获取和验证部署参数',
        config: {
          actionType: 'parameter_validation',
          parameters: {
            required_fields: ['service_name', 'docker_image', 'deployment_env']
          }
        },
        timeout: 10000,
        retry: { maxAttempts: 2, delay: 1000 }
      },
      {
        index: 1,
        name: '拉取Docker镜像',
        type: 'HTTP_REQUEST',
        description: '从镜像仓库拉取最新镜像',
        config: {
          method: 'POST',
          url: 'http://registry.internal/v2/{{service_name}}/pull',
          headers: { 'Content-Type': 'application/json' },
          body: { tag: '{{docker_image}}' },
          timeout: 300000
        },
        timeout: 300000,
        retry: { maxAttempts: 3, delay: 5000 }
      },
      {
        index: 2,
        name: '停止旧版本服务',
        type: 'SCRIPT',
        description: '优雅停止当前运行的服务',
        config: {
          language: 'bash',
          code: `
kubectl scale deployment {{service_name}} --replicas=0 -n {{deployment_env}}
kubectl rollout status deployment/{{service_name}} -n {{deployment_env}} --timeout=60s
          `,
          timeout: 60000
        },
        timeout: 60000,
        retry: { maxAttempts: 2, delay: 5000 }
      },
      {
        index: 3,
        name: '部署新版本',
        type: 'SCRIPT',
        description: '部署新版本服务',
        config: {
          language: 'bash',
          code: `
kubectl set image deployment/{{service_name}} {{service_name}}={{docker_image}} -n {{deployment_env}}
kubectl rollout status deployment/{{service_name}} -n {{deployment_env}} --timeout=300s
          `,
          timeout: 300000
        },
        timeout: 300000,
        retry: { maxAttempts: 3, delay: 10000 }
      },
      {
        index: 4,
        name: '健康检查',
        type: 'HTTP_REQUEST',
        description: '验证服务健康状态',
        config: {
          method: 'GET',
          url: '{{health_check_url}}/health',
          expectedStatus: 200,
          timeout: 30000,
          retryCount: 5,
          retryDelay: 10000
        },
        timeout: 60000,
        retry: { maxAttempts: 5, delay: 10000 }
      },
      {
        index: 5,
        name: '发送部署通知',
        type: 'NOTIFICATION',
        description: '发送部署成功通知',
        config: {
          type: 'email',
          recipients: ['{{notifications}}'],
          subject: '{{service_name}} 部署成功',
          content: '服务 {{service_name}} 已成功部署到 {{deployment_env}} 环境'
        },
        timeout: 30000,
        retry: { maxAttempts: 2, delay: 5000 }
      }
    ];
  }

  /**
   * 创建SLA违规步骤
   */
  private createSLAViolationSteps(): WorkflowStep[] {
    return [
      {
        index: 0,
        name: '检测SLA违规',
        type: 'CONDITION',
        description: '检查SLA指标是否违规',
        config: {
          expression: 'sla_current < sla_threshold',
          trueStep: 1,
          falseStep: -1
        },
        timeout: 5000,
        retry: { maxAttempts: 1, delay: 1000 }
      },
      {
        index: 1,
        name: '记录违规事件',
        type: 'DATABASE_OPERATION',
        description: '在数据库中记录SLA违规事件',
        config: {
          operation: 'INSERT',
          connection: 'main_db',
          query: 'INSERT INTO sla_violations (service_id, violation_time, severity, current_value, threshold) VALUES (?, NOW(), ?, ?, ?)',
          parameters: ['{{service_id}}', '{{severity}}', '{{sla_current}}', '{{sla_threshold}}']
        },
        timeout: 10000,
        retry: { maxAttempts: 3, delay: 2000 }
      },
      {
        index: 2,
        name: '发送告警通知',
        type: 'NOTIFICATION',
        description: '发送SLA违规告警',
        config: {
          type: 'multi_channel',
          channels: '{{notification_channels}}',
          recipients: ['<EMAIL>', '<EMAIL>'],
          subject: 'SLA违规告警 - {{service_name}}',
          content: '服务 {{service_name}} SLA违规，当前值: {{sla_current}}%，阈值: {{sla_threshold}}%'
        },
        timeout: 30000,
        retry: { maxAttempts: 3, delay: 5000 }
      },
      {
        index: 3,
        name: '启动自动恢复',
        type: 'SUBPROCESS',
        description: '启动服务自动恢复流程',
        config: {
          workflowId: 'service-recovery',
          context: {
            service_name: '{{service_name}}',
            severity: '{{severity}}'
          },
          waitForCompletion: false
        },
        timeout: 10000,
        retry: { maxAttempts: 1, delay: 0 }
      }
    ];
  }

  /**
   * 创建告警升级步骤
   */
  private createAlertEscalationSteps(): WorkflowStep[] {
    return [
      {
        index: 0,
        name: '一级响应',
        type: 'NOTIFICATION',
        description: '通知一级响应团队',
        config: {
          type: 'pager',
          recipients: ['{{oncall_rotation}}.primary'],
          subject: '{{alert_severity}} 级告警',
          content: '告警详情: {{alert_details}}',
          urgency: 'high'
        },
        timeout: 30000,
        retry: { maxAttempts: 3, delay: 10000 }
      },
      {
        index: 1,
        name: '等待响应',
        type: 'DELAY',
        description: '等待一级响应确认',
        config: {
          duration: '{{escalation_delay}}',
          unit: 'seconds'
        },
        timeout: 0,
        retry: { maxAttempts: 1, delay: 0 }
      },
      {
        index: 2,
        name: '检查响应状态',
        type: 'CONDITION',
        description: '检查告警是否已被响应',
        config: {
          expression: 'alert_acknowledged == false && escalation_level < max_escalation_level',
          trueStep: 3,
          falseStep: -1
        },
        timeout: 5000,
        retry: { maxAttempts: 1, delay: 0 }
      },
      {
        index: 3,
        name: '二级响应',
        type: 'NOTIFICATION',
        description: '升级至二级响应团队',
        config: {
          type: 'pager',
          recipients: ['{{oncall_rotation}}.secondary', '<EMAIL>'],
          subject: '升级告警 - {{alert_severity}}',
          content: '告警未及时响应，已升级至二级团队',
          urgency: 'critical'
        },
        timeout: 30000,
        retry: { maxAttempts: 3, delay: 10000 }
      },
      {
        index: 4,
        name: '更新升级状态',
        type: 'DATABASE_OPERATION',
        description: '更新告警升级状态',
        config: {
          operation: 'UPDATE',
          connection: 'main_db',
          query: 'UPDATE alerts SET escalation_level = ?, escalated_at = NOW() WHERE alert_id = ?',
          parameters: [2, '{{alert_id}}']
        },
        timeout: 10000,
        retry: { maxAttempts: 3, delay: 2000 }
      }
    ];
  }

  /**
   * 创建数据库备份步骤
   */
  private createDatabaseBackupSteps(): WorkflowStep[] {
    return [
      {
        index: 0,
        name: '检查备份目录',
        type: 'SCRIPT',
        description: '检查并创建备份目录',
        config: {
          language: 'bash',
          code: `
mkdir -p {{backup_location}}/$(date +%Y%m%d)
if [ ! -d "{{backup_location}}" ]; then
  echo "备份目录创建失败"
  exit 1
fi
          `,
          timeout: 10000
        },
        timeout: 10000,
        retry: { maxAttempts: 2, delay: 1000 }
      },
      {
        index: 1,
        name: '执行数据库备份',
        type: 'SCRIPT',
        description: '执行数据库导出',
        config: {
          language: 'bash',
          code: `
BACKUP_FILE="{{backup_location}}/$(date +%Y%m%d)/backup_$(date +%H%M%S).sql"
mysqldump --single-transaction --routines --triggers --all-databases > $BACKUP_FILE
if [ $? -eq 0 ]; then
  echo "备份成功: $BACKUP_FILE"
  echo "backup_file=$BACKUP_FILE" >> $GITHUB_ENV
else
  echo "备份失败"
  exit 1
fi
          `,
          timeout: 3600000
        },
        timeout: 3600000,
        retry: { maxAttempts: 2, delay: 30000 }
      },
      {
        index: 2,
        name: '压缩备份文件',
        type: 'SCRIPT',
        description: '压缩备份文件以节省空间',
        config: {
          language: 'bash',
          code: `
if [ "{{compression_enabled}}" = "true" ]; then
  gzip $backup_file
  COMPRESSED_FILE="$backup_file.gz"
  echo "压缩完成: $COMPRESSED_FILE"
  echo "final_backup_file=$COMPRESSED_FILE" >> $GITHUB_ENV
else
  echo "final_backup_file=$backup_file" >> $GITHUB_ENV
fi
          `,
          timeout: 300000
        },
        condition: {
          expression: 'compression_enabled == true',
          variables: ['compression_enabled']
        },
        timeout: 300000,
        retry: { maxAttempts: 2, delay: 10000 }
      },
      {
        index: 3,
        name: '验证备份完整性',
        type: 'SCRIPT',
        description: '验证备份文件的完整性',
        config: {
          language: 'bash',
          code: `
if [ -f "$final_backup_file" ]; then
  FILE_SIZE=$(stat -f%z "$final_backup_file" 2>/dev/null || stat -c%s "$final_backup_file")
  if [ $FILE_SIZE -gt 1000 ]; then
    echo "备份文件验证成功，大小: $FILE_SIZE bytes"
  else
    echo "备份文件太小，可能有问题"
    exit 1
  fi
else
  echo "备份文件不存在"
  exit 1
fi
          `,
          timeout: 60000
        },
        timeout: 60000,
        retry: { maxAttempts: 1, delay: 0 }
      },
      {
        index: 4,
        name: '清理过期备份',
        type: 'SCRIPT',
        description: '清理超过保留期的备份文件',
        config: {
          language: 'bash',
          code: `
find {{backup_location}} -type f -name "*.sql*" -mtime +{{retention_days}} -delete
echo "过期备份清理完成"
          `,
          timeout: 60000
        },
        timeout: 60000,
        retry: { maxAttempts: 2, delay: 5000 }
      },
      {
        index: 5,
        name: '发送备份报告',
        type: 'NOTIFICATION',
        description: '发送备份完成通知',
        config: {
          type: 'email',
          recipients: ['{{notifications}}'],
          subject: '数据库备份完成 - {{database_type}}',
          content: '数据库备份已完成\n备份文件: {{final_backup_file}}\n执行时间: {{execution_time}}'
        },
        timeout: 30000,
        retry: { maxAttempts: 3, delay: 5000 }
      }
    ];
  }

  /**
   * 获取所有模板分类
   */
  getCategories(): TemplateCategory[] {
    return this.categories;
  }

  /**
   * 获取指定分类的模板
   */
  getTemplatesByCategory(category: WorkflowCategory): WorkflowTemplate[] {
    return Array.from(this.templates.values())
      .filter(template => template.category === category)
      .sort((a, b) => b.usageCount - a.usageCount);
  }

  /**
   * 获取指定子分类的模板
   */
  getTemplatesBySubcategory(category: WorkflowCategory, subcategory: string): WorkflowTemplate[] {
    return Array.from(this.templates.values())
      .filter(template => template.category === category && template.subcategory === subcategory)
      .sort((a, b) => b.rating - a.rating);
  }

  /**
   * 搜索模板
   */
  searchTemplates(query: string, filters?: {
    category?: WorkflowCategory;
    difficulty?: string;
    tags?: string[];
    isPopular?: boolean;
  }): WorkflowTemplate[] {
    let results = Array.from(this.templates.values());

    // 文本搜索
    if (query) {
      const lowerQuery = query.toLowerCase();
      results = results.filter(template => 
        template.name.toLowerCase().includes(lowerQuery) ||
        template.description.toLowerCase().includes(lowerQuery) ||
        template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
      );
    }

    // 应用过滤器
    if (filters) {
      if (filters.category) {
        results = results.filter(template => template.category === filters.category);
      }
      if (filters.difficulty) {
        results = results.filter(template => template.difficulty === filters.difficulty);
      }
      if (filters.tags && filters.tags.length > 0) {
        results = results.filter(template => 
          filters.tags!.some(tag => template.tags.includes(tag))
        );
      }
      if (filters.isPopular) {
        results = results.filter(template => template.isPopular);
      }
    }

    return results.sort((a, b) => b.rating - a.rating);
  }

  /**
   * 获取模板详情
   */
  getTemplate(templateId: string): WorkflowTemplate | undefined {
    return this.templates.get(templateId);
  }

  /**
   * 获取热门模板
   */
  getPopularTemplates(limit = 10): WorkflowTemplate[] {
    return Array.from(this.templates.values())
      .filter(template => template.isPopular)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit);
  }

  /**
   * 获取最新模板
   */
  getRecentTemplates(limit = 10): WorkflowTemplate[] {
    return Array.from(this.templates.values())
      .sort((a, b) => new Date(b.metadata.updatedAt).getTime() - new Date(a.metadata.updatedAt).getTime())
      .slice(0, limit);
  }

  /**
   * 增加模板使用计数
   */
  incrementUsageCount(templateId: string): void {
    const template = this.templates.get(templateId);
    if (template) {
      template.usageCount += 1;
      template.metadata.lastUsed = new Date().toISOString();
    }
  }

  /**
   * 应用模板创建工作流
   */
  applyTemplate(templateId: string, customizations?: {
    name?: string;
    description?: string;
    variables?: Record<string, any>;
    settings?: Partial<WorkflowDefinition['settings']>;
  }): Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'> {
    const template = this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // 增加使用计数
    this.incrementUsageCount(templateId);

    // 应用自定义配置
    const workflowDef = { ...template.workflow };
    
    if (customizations) {
      if (customizations.name) {
        workflowDef.name = customizations.name;
      }
      if (customizations.description) {
        workflowDef.description = customizations.description;
      }
      if (customizations.variables) {
        workflowDef.variables = { ...workflowDef.variables, ...customizations.variables };
      }
      if (customizations.settings) {
        workflowDef.settings = { ...workflowDef.settings, ...customizations.settings };
      }
    }

    return workflowDef;
  }

  /**
   * 添加自定义模板
   */
  addCustomTemplate(template: Omit<WorkflowTemplate, 'id' | 'metadata'>): string {
    const templateId = `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const fullTemplate: WorkflowTemplate = {
      ...template,
      id: templateId,
      isBuiltin: false,
      usageCount: 0,
      rating: 0,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        downloads: 0
      }
    };

    this.templates.set(templateId, fullTemplate);
    return templateId;
  }

  /**
   * 更新模板评分
   */
  updateTemplateRating(templateId: string, rating: number): void {
    const template = this.templates.get(templateId);
    if (template && rating >= 1 && rating <= 5) {
      // 简化的评分更新逻辑
      template.rating = rating;
      template.metadata.updatedAt = new Date().toISOString();
    }
  }

  /**
   * 导出模板
   */
  exportTemplate(templateId: string): WorkflowTemplate | undefined {
    return this.getTemplate(templateId);
  }

  /**
   * 导入模板
   */
  importTemplate(templateData: WorkflowTemplate): string {
    // 生成新的ID避免冲突
    const newId = `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const importedTemplate: WorkflowTemplate = {
      ...templateData,
      id: newId,
      isBuiltin: false,
      metadata: {
        ...templateData.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    };

    this.templates.set(newId, importedTemplate);
    return newId;
  }
}

export const workflowTemplateService = new WorkflowTemplateService();
export default workflowTemplateService;