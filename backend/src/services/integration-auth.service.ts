/**
 * 集成认证管理服务
 * 处理各种外部系统的认证方式
 */

import { AuthConfig, AuthType } from '../types/integration';
import { logger } from '../utils/logger.util';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';

export class IntegrationAuthManager {
  
  /**
   * 验证认证配置
   */
  validateAuthConfig(authConfig: AuthConfig): void {
    switch (authConfig.type) {
      case AuthType.API_KEY:
        if (!authConfig.credentials.apiKey) {
          throw new Error('API Key is required for API_KEY auth type');
        }
        break;
        
      case AuthType.OAUTH2:
        if (!authConfig.credentials.clientId || !authConfig.credentials.clientSecret) {
          throw new Error('Client ID and Client Secret are required for OAuth2');
        }
        break;
        
      case AuthType.BASIC_AUTH:
        if (!authConfig.credentials.username || !authConfig.credentials.password) {
          throw new Error('Username and password are required for Basic Auth');
        }
        break;
        
      case AuthType.TOKEN:
        if (!authConfig.credentials.token) {
          throw new Error('Token is required for TOKEN auth type');
        }
        break;
        
      case AuthType.JWT:
        if (!authConfig.credentials.secret || !authConfig.credentials.payload) {
          throw new Error('Secret and payload are required for JWT auth type');
        }
        break;
        
      default:
        throw new Error(`Unsupported auth type: ${authConfig.type}`);
    }
  }

  /**
   * 获取认证头信息
   */
  async getAuthHeaders(authConfig: AuthConfig): Promise<Record<string, string>> {
    switch (authConfig.type) {
      case AuthType.API_KEY:
        return this.getApiKeyHeaders(authConfig);
        
      case AuthType.OAUTH2:
        return this.getOAuth2Headers(authConfig);
        
      case AuthType.BASIC_AUTH:
        return this.getBasicAuthHeaders(authConfig);
        
      case AuthType.TOKEN:
        return this.getTokenHeaders(authConfig);
        
      case AuthType.JWT:
        return this.getJwtHeaders(authConfig);
        
      default:
        throw new Error(`Unsupported auth type: ${authConfig.type}`);
    }
  }

  /**
   * API Key 认证
   */
  private getApiKeyHeaders(authConfig: AuthConfig): Record<string, string> {
    const { apiKey, headerName = 'X-API-Key' } = authConfig.credentials;
    return {
      [headerName]: apiKey
    };
  }

  /**
   * OAuth2 认证
   */
  private async getOAuth2Headers(authConfig: AuthConfig): Promise<Record<string, string>> {
    try {
      // 检查是否有有效的访问令牌
      if (authConfig.credentials.accessToken && !this.isTokenExpired(authConfig)) {
        return {
          'Authorization': `Bearer ${authConfig.credentials.accessToken}`
        };
      }

      // 刷新令牌或重新获取
      const accessToken = await this.refreshOAuth2Token(authConfig);
      return {
        'Authorization': `Bearer ${accessToken}`
      };
    } catch (error) {
      logger.error('OAuth2 authentication failed:', error);
      throw new Error('OAuth2 authentication failed');
    }
  }

  /**
   * Basic Auth 认证
   */
  private getBasicAuthHeaders(authConfig: AuthConfig): Record<string, string> {
    const { username, password } = authConfig.credentials;
    const credentials = Buffer.from(`${username}:${password}`).toString('base64');
    return {
      'Authorization': `Basic ${credentials}`
    };
  }

  /**
   * Token 认证
   */
  private getTokenHeaders(authConfig: AuthConfig): Record<string, string> {
    const { token, headerName = 'Authorization', prefix = 'Bearer' } = authConfig.credentials;
    const headerValue = prefix ? `${prefix} ${token}` : token;
    return {
      [headerName]: headerValue
    };
  }

  /**
   * JWT 认证
   */
  private getJwtHeaders(authConfig: AuthConfig): Record<string, string> {
    try {
      const { secret, payload, algorithm = 'HS256', expiresIn = '1h' } = authConfig.credentials;
      
      const token = jwt.sign(payload, secret, {
        algorithm: algorithm as jwt.Algorithm,
        expiresIn
      });
      
      return {
        'Authorization': `Bearer ${token}`
      };
    } catch (error) {
      logger.error('JWT token generation failed:', error);
      throw new Error('JWT token generation failed');
    }
  }

  /**
   * 检查令牌是否过期
   */
  private isTokenExpired(authConfig: AuthConfig): boolean {
    if (!authConfig.expiresAt) {
      return false;
    }
    
    const now = new Date();
    const expiryTime = new Date(authConfig.expiresAt);
    const bufferTime = 5 * 60 * 1000; // 5分钟缓冲
    
    return now.getTime() > (expiryTime.getTime() - bufferTime);
  }

  /**
   * 刷新OAuth2令牌
   */
  private async refreshOAuth2Token(authConfig: AuthConfig): Promise<string> {
    const { 
      clientId, 
      clientSecret, 
      refreshToken, 
      tokenEndpoint 
    } = authConfig.credentials;

    if (!refreshToken || !tokenEndpoint) {
      throw new Error('Refresh token or token endpoint not configured');
    }

    try {
      const response = await fetch(tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: refreshToken
        })
      });

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.statusText}`);
      }

      const tokenData = await response.json();
      
      // 更新认证配置
      authConfig.credentials.accessToken = tokenData.access_token;
      if (tokenData.refresh_token) {
        authConfig.credentials.refreshToken = tokenData.refresh_token;
      }
      if (tokenData.expires_in) {
        authConfig.expiresAt = new Date(Date.now() + tokenData.expires_in * 1000);
      }

      logger.info('OAuth2 token refreshed successfully');
      return tokenData.access_token;

    } catch (error) {
      logger.error('Failed to refresh OAuth2 token:', error);
      throw error;
    }
  }

  /**
   * 为企业微信生成签名
   */
  generateWeChatWorkSignature(timestamp: string, nonce: string, token: string): string {
    const arr = [token, timestamp, nonce].sort();
    const str = arr.join('');
    return crypto.createHash('sha1').update(str).digest('hex');
  }

  /**
   * 为钉钉生成签名
   */
  generateDingTalkSignature(timestamp: string, secret: string): string {
    const stringToSign = timestamp + '\n' + secret;
    return crypto.createHmac('sha256', secret).update(stringToSign).digest('base64');
  }

  /**
   * 验证Webhook签名
   */
  verifyWebhookSignature(payload: string, signature: string, secret: string, algorithm = 'sha256'): boolean {
    try {
      const expectedSignature = crypto
        .createHmac(algorithm, secret)
        .update(payload)
        .digest('hex');
      
      // 时间常数比较防止时序攻击
      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      logger.error('Webhook signature verification failed:', error);
      return false;
    }
  }
}