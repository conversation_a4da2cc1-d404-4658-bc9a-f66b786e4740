/**
 * 集成监控服务
 * 负责收集集成操作的指标和事件
 */

import { 
  IntegrationMetrics, 
  IntegrationEvent, 
  OperationType 
} from '../types/integration';
import { logger } from '../utils/logger.util';

interface MetricRecord {
  success: boolean;
  duration: number;
  operation: OperationType;
  timestamp: Date;
  error?: string;
}

export class IntegrationMonitor {
  private metrics: Map<string, IntegrationMetrics> = new Map();
  private events: Map<string, IntegrationEvent[]> = new Map();
  private metricRecords: Map<string, MetricRecord[]> = new Map();

  /**
   * 记录监控指标
   */
  async recordMetrics(integrationId: string, record: MetricRecord): Promise<void> {
    try {
      // 获取或创建指标记录列表
      if (!this.metricRecords.has(integrationId)) {
        this.metricRecords.set(integrationId, []);
      }
      
      const records = this.metricRecords.get(integrationId)!;
      records.push(record);
      
      // 保留最近的1000条记录
      if (records.length > 1000) {
        records.splice(0, records.length - 1000);
      }
      
      // 更新聚合指标
      await this.updateAggregatedMetrics(integrationId);
      
    } catch (error) {
      logger.error(`Failed to record metrics for integration ${integrationId}:`, error);
    }
  }

  /**
   * 记录事件
   */
  async recordEvent(event: IntegrationEvent): Promise<void> {
    try {
      if (!this.events.has(event.integrationId)) {
        this.events.set(event.integrationId, []);
      }
      
      const events = this.events.get(event.integrationId)!;
      events.unshift(event); // 最新的事件在前面
      
      // 保留最近的500个事件
      if (events.length > 500) {
        events.splice(500);
      }
      
      logger.info(`Event recorded for integration ${event.integrationId}: ${event.message}`);
      
    } catch (error) {
      logger.error(`Failed to record event for integration ${event.integrationId}:`, error);
    }
  }

  /**
   * 获取集成指标
   */
  async getMetrics(integrationId: string): Promise<IntegrationMetrics | null> {
    return this.metrics.get(integrationId) || null;
  }

  /**
   * 获取集成事件
   */
  async getEvents(integrationId: string, limit = 100): Promise<IntegrationEvent[]> {
    const events = this.events.get(integrationId) || [];
    return events.slice(0, Math.min(limit, events.length));
  }

  /**
   * 获取所有集成的概览指标
   */
  async getAllMetricsOverview(): Promise<Record<string, IntegrationMetrics>> {
    const overview: Record<string, IntegrationMetrics> = {};
    
    for (const [integrationId, metrics] of this.metrics) {
      overview[integrationId] = metrics;
    }
    
    return overview;
  }

  /**
   * 清理过期数据
   */
  async cleanupOldData(retentionDays = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
    
    try {
      // 清理过期的指标记录
      for (const [integrationId, records] of this.metricRecords) {
        const validRecords = records.filter(record => record.timestamp > cutoffDate);
        this.metricRecords.set(integrationId, validRecords);
      }
      
      // 清理过期的事件
      for (const [integrationId, events] of this.events) {
        const validEvents = events.filter(event => event.timestamp > cutoffDate);
        this.events.set(integrationId, validEvents);
      }
      
      logger.info(`Cleaned up integration monitoring data older than ${retentionDays} days`);
      
    } catch (error) {
      logger.error('Failed to cleanup old monitoring data:', error);
    }
  }

  /**
   * 更新聚合指标
   */
  private async updateAggregatedMetrics(integrationId: string): Promise<void> {
    const records = this.metricRecords.get(integrationId) || [];
    if (records.length === 0) return;

    // 计算时间窗口 (最近1小时)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentRecords = records.filter(record => record.timestamp > oneHourAgo);
    
    if (recentRecords.length === 0) return;

    // 计算基本统计
    const totalRequests = recentRecords.length;
    const successfulRequests = recentRecords.filter(r => r.success).length;
    const failedRequests = totalRequests - successfulRequests;
    
    // 计算响应时间统计
    const durations = recentRecords.map(r => r.duration);
    const avgResponseTime = durations.reduce((a, b) => a + b, 0) / durations.length;
    const maxResponseTime = Math.max(...durations);
    
    // 计算错误统计
    const errorsByType: Record<string, number> = {};
    recentRecords.forEach(record => {
      if (!record.success && record.error) {
        errorsByType[record.error] = (errorsByType[record.error] || 0) + 1;
      }
    });
    
    // 计算可用性 (最近24小时)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const dayRecords = records.filter(record => record.timestamp > oneDayAgo);
    const daySuccessful = dayRecords.filter(r => r.success).length;
    const availability = dayRecords.length > 0 ? daySuccessful / dayRecords.length : 1;
    
    // 确定健康状态
    let healthStatus: 'healthy' | 'warning' | 'critical';
    if (availability >= 0.99) {
      healthStatus = 'healthy';
    } else if (availability >= 0.95) {
      healthStatus = 'warning';
    } else {
      healthStatus = 'critical';
    }

    // 更新指标
    const metrics: IntegrationMetrics = {
      integrationId,
      timestamp: new Date(),
      requests: {
        total: totalRequests,
        success: successfulRequests,
        failed: failedRequests,
        avgResponseTime,
        maxResponseTime
      },
      errors: {
        total: failedRequests,
        byType: errorsByType
      },
      availability,
      lastHealthCheck: new Date(),
      healthStatus
    };

    this.metrics.set(integrationId, metrics);
  }

  /**
   * 获取性能趋势数据
   */
  async getPerformanceTrend(integrationId: string, hours = 24): Promise<{
    timestamps: Date[];
    responseTimes: number[];
    successRates: number[];
    errorCounts: number[];
  }> {
    const records = this.metricRecords.get(integrationId) || [];
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentRecords = records.filter(record => record.timestamp > cutoffTime);
    
    // 按小时分组
    const hourlyData: Record<string, MetricRecord[]> = {};
    recentRecords.forEach(record => {
      const hour = new Date(record.timestamp);
      hour.setMinutes(0, 0, 0);
      const key = hour.toISOString();
      
      if (!hourlyData[key]) {
        hourlyData[key] = [];
      }
      hourlyData[key].push(record);
    });
    
    // 生成趋势数据
    const timestamps: Date[] = [];
    const responseTimes: number[] = [];
    const successRates: number[] = [];
    const errorCounts: number[] = [];
    
    const sortedHours = Object.keys(hourlyData).sort();
    sortedHours.forEach(hourKey => {
      const hourRecords = hourlyData[hourKey];
      const timestamp = new Date(hourKey);
      
      // 平均响应时间
      const avgResponseTime = hourRecords.reduce((sum, r) => sum + r.duration, 0) / hourRecords.length;
      
      // 成功率
      const successCount = hourRecords.filter(r => r.success).length;
      const successRate = successCount / hourRecords.length;
      
      // 错误数量
      const errorCount = hourRecords.filter(r => !r.success).length;
      
      timestamps.push(timestamp);
      responseTimes.push(Math.round(avgResponseTime));
      successRates.push(Math.round(successRate * 100) / 100);
      errorCounts.push(errorCount);
    });
    
    return {
      timestamps,
      responseTimes,
      successRates,
      errorCounts
    };
  }

  /**
   * 检查是否需要发送告警
   */
  async checkAlerts(integrationId: string): Promise<{
    shouldAlert: boolean;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    metrics?: IntegrationMetrics;
  }> {
    const metrics = this.metrics.get(integrationId);
    if (!metrics) {
      return {
        shouldAlert: false,
        severity: 'low',
        message: 'No metrics available'
      };
    }
    
    const { availability, requests, healthStatus } = metrics;
    
    // 严重告警条件
    if (availability < 0.9 || healthStatus === 'critical') {
      return {
        shouldAlert: true,
        severity: 'critical',
        message: `集成服务严重异常: 可用性 ${(availability * 100).toFixed(1)}%`,
        metrics
      };
    }
    
    // 高级告警条件
    if (availability < 0.95 || requests.avgResponseTime > 5000) {
      return {
        shouldAlert: true,
        severity: 'high',
        message: `集成服务性能告警: 可用性 ${(availability * 100).toFixed(1)}%, 平均响应时间 ${requests.avgResponseTime}ms`,
        metrics
      };
    }
    
    // 中级告警条件
    if (requests.failed > requests.success * 0.1) {
      return {
        shouldAlert: true,
        severity: 'medium',
        message: `集成服务错误率偏高: ${requests.failed}/${requests.total} 请求失败`,
        metrics
      };
    }
    
    return {
      shouldAlert: false,
      severity: 'low',
      message: '服务运行正常'
    };
  }
}