/**
 * 通信集成服务
 * 提供企业微信、钉钉、Slack、飞书等平台的集成管理
 */

import { PrismaClient } from '@prisma/client';
import { 
  CommunicationIntegration,
  CreateCommunicationIntegrationRequest,
  UpdateCommunicationIntegrationRequest,
  TestConnectionRequest,
  TestConnectionResponse,
  SendMessageRequest,
  SendMessageResponse,
  HealthCheckResponse,
  UserSyncRequest,
  UserSyncResponse,
  CommunicationPlatform,
  IntegrationStatus,
  HealthStatus,
  OperationType,
  ExecutionStatus
} from '../types/communication-integration';
import { logger } from '../utils/logger.util';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient() as any;

export class CommunicationIntegrationService {
  
  /**
   * 获取所有通信集成
   */
  async getAllIntegrations(): Promise<CommunicationIntegration[]> {
    try {
      const integrations = await prisma.communicationIntegration.findMany({
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          updatedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return integrations.map((integration: any) => ({
        ...integration,
        config: integration.config,
        createdAt: integration.createdAt,
        updatedAt: integration.updatedAt
      }));
    } catch (error) {
      logger.error('Failed to get all integrations:', error);
      throw new Error('获取集成列表失败');
    }
  }

  /**
   * 根据类型获取集成
   */
  async getIntegrationsByType(type: CommunicationPlatform): Promise<CommunicationIntegration[]> {
    try {
      const integrations = await prisma.communicationIntegration.findMany({
        where: { type },
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      });

      return integrations.map((integration: any) => ({
        ...integration,
        config: integration.config,
        createdAt: integration.createdAt,
        updatedAt: integration.updatedAt
      }));
    } catch (error) {
      logger.error(`Failed to get integrations by type ${type}:`, error);
      throw new Error('获取集成列表失败');
    }
  }

  /**
   * 获取单个集成
   */
  async getIntegration(id: string): Promise<CommunicationIntegration | null> {
    try {
      const integration = await prisma.communicationIntegration.findUnique({
        where: { id },
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          updatedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        }
      });

      if (!integration) {
        return null;
      }

      return {
        ...integration,
        config: integration.config,
        createdAt: integration.createdAt,
        updatedAt: integration.updatedAt
      };
    } catch (error) {
      logger.error(`Failed to get integration ${id}:`, error);
      throw new Error('获取集成配置失败');
    }
  }

  /**
   * 创建集成
   */
  async createIntegration(
    request: CreateCommunicationIntegrationRequest,
    userId: string
  ): Promise<CommunicationIntegration> {
    try {
      // 验证配置
      this.validateConfig(request.type, request.config);

      // 检查名称是否重复
      const existing = await prisma.communicationIntegration.findFirst({
        where: { name: request.name }
      });

      if (existing) {
        throw new Error('集成名称已存在');
      }

      // 创建集成
      const integration = await prisma.communicationIntegration.create({
        data: {
          name: request.name,
          type: request.type,
          description: request.description,
          enabled: request.enabled ?? true,
          priority: request.priority ?? 5,
          status: IntegrationStatus.CONFIGURING,
          config: request.config,
          createdBy: userId
        },
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        }
      });

      // 记录执行日志
      await this.recordExecution({
        integrationId: integration.id,
        operation: OperationType.TEST,
        status: ExecutionStatus.SUCCESS,
        requestData: request
      });

      logger.info(`Integration created: ${integration.name} (${integration.id})`);

      return {
        ...integration,
        config: integration.config,
        createdAt: integration.createdAt,
        updatedAt: integration.updatedAt
      };
    } catch (error) {
      logger.error('Failed to create integration:', error);
      throw error;
    }
  }

  /**
   * 更新集成
   */
  async updateIntegration(
    id: string,
    request: UpdateCommunicationIntegrationRequest,
    userId: string
  ): Promise<CommunicationIntegration> {
    try {
      const existing = await prisma.communicationIntegration.findUnique({
        where: { id }
      });

      if (!existing) {
        throw new Error('集成不存在');
      }

      // 如果更新了配置，验证新配置
      if (request.config) {
        this.validateConfig(existing.type as CommunicationPlatform, request.config);
      }

      // 更新集成
      const integration = await prisma.communicationIntegration.update({
        where: { id },
        data: {
          ...request,
          updatedBy: userId,
          updatedAt: new Date()
        },
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          updatedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        }
      });

      // 记录执行日志
      await this.recordExecution({
        integrationId: id,
        operation: OperationType.TEST,
        status: ExecutionStatus.SUCCESS,
        requestData: request
      });

      logger.info(`Integration updated: ${integration.name} (${id})`);

      return {
        ...integration,
        config: integration.config,
        createdAt: integration.createdAt,
        updatedAt: integration.updatedAt
      };
    } catch (error) {
      logger.error(`Failed to update integration ${id}:`, error);
      throw error;
    }
  }

  /**
   * 删除集成
   */
  async deleteIntegration(id: string): Promise<void> {
    try {
      const integration = await prisma.communicationIntegration.findUnique({
        where: { id }
      });

      if (!integration) {
        throw new Error('集成不存在');
      }

      await prisma.communicationIntegration.delete({
        where: { id }
      });

      logger.info(`Integration deleted: ${integration.name} (${id})`);
    } catch (error) {
      logger.error(`Failed to delete integration ${id}:`, error);
      throw error;
    }
  }

  /**
   * 测试连接
   */
  async testConnection(request: TestConnectionRequest): Promise<TestConnectionResponse> {
    const startTime = Date.now();
    
    try {
      // 根据平台类型进行连接测试
      let success = false;
      let message = '';
      let errorCode = '';

      switch (request.type) {
        case CommunicationPlatform.WECHAT_WORK:
          success = await this.testWeChatWorkConnection(request.config as any);
          message = success ? '企业微信连接测试成功' : '企业微信连接测试失败';
          break;
        case CommunicationPlatform.DINGTALK:
          success = await this.testDingTalkConnection(request.config as any);
          message = success ? '钉钉连接测试成功' : '钉钉连接测试失败';
          break;
        case CommunicationPlatform.SLACK:
          success = await this.testSlackConnection(request.config as any);
          message = success ? 'Slack连接测试成功' : 'Slack连接测试失败';
          break;
        case CommunicationPlatform.FEISHU:
          success = await this.testFeishuConnection(request.config as any);
          message = success ? '飞书连接测试成功' : '飞书连接测试失败';
          break;
        default:
          throw new Error('不支持的平台类型');
      }

      const responseTime = Date.now() - startTime;

      return {
        success,
        message,
        details: {
          responseTime,
          platform: request.type,
          timestamp: new Date().toISOString(),
          errorCode: success ? undefined : errorCode
        }
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Connection test failed:', error);

      return {
        success: false,
        message: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: {
          responseTime,
          platform: request.type,
          timestamp: new Date().toISOString(),
          errorCode: 'TEST_FAILED'
        }
      };
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    try {
      const integration = await this.getIntegration(request.integrationId);
      if (!integration) {
        throw new Error('集成不存在');
      }

      if (!integration.enabled) {
        throw new Error('集成已禁用');
      }

      // 根据平台类型发送消息
      let success = false;
      let messageId = '';
      let error = '';

      switch (integration.type) {
        case CommunicationPlatform.WECHAT_WORK:
          const wechatResult = await this.sendWeChatWorkMessage(integration.config as any, request);
          success = wechatResult.success;
          messageId = wechatResult.messageId || '';
          error = wechatResult.error || '';
          break;
        case CommunicationPlatform.DINGTALK:
          const dingtalkResult = await this.sendDingTalkMessage(integration.config as any, request);
          success = dingtalkResult.success;
          messageId = dingtalkResult.messageId || '';
          error = dingtalkResult.error || '';
          break;
        case CommunicationPlatform.SLACK:
          const slackResult = await this.sendSlackMessage(integration.config as any, request);
          success = slackResult.success;
          messageId = slackResult.messageId || '';
          error = slackResult.error || '';
          break;
        case CommunicationPlatform.FEISHU:
          const feishuResult = await this.sendFeishuMessage(integration.config as any, request);
          success = feishuResult.success;
          messageId = feishuResult.messageId || '';
          error = feishuResult.error || '';
          break;
        default:
          throw new Error('不支持的平台类型');
      }

      // 记录执行日志
      await this.recordExecution({
        integrationId: request.integrationId,
        operation: OperationType.SEND_MESSAGE,
        status: success ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILED,
        requestData: request,
        responseData: { success, messageId, error },
        errorMessage: error || undefined,
        duration: Date.now() - Date.now() // 这里应该计算实际耗时
      });

      return { success, messageId, error };
    } catch (error) {
      logger.error('Failed to send message:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(id: string): Promise<HealthCheckResponse> {
    try {
      const integration = await this.getIntegration(id);
      if (!integration) {
        throw new Error('集成不存在');
      }

      // 执行健康检查
      const result = await this.testConnection({
        type: integration.type,
        config: integration.config
      });

      // 更新健康状态
      await prisma.communicationIntegration.update({
        where: { id },
        data: {
          lastHealthCheck: new Date(),
          healthStatus: result.success ? HealthStatus.HEALTHY : HealthStatus.CRITICAL,
          lastErrorAt: result.success ? undefined : new Date(),
          lastErrorMessage: result.success ? undefined : result.message
        }
      });

      return {
        healthy: result.success,
        details: {
          responseTime: result.details?.responseTime || 0,
          errorCount: result.success ? 0 : 1,
          lastError: result.success ? undefined : result.message,
          platform: integration.type
        }
      };
    } catch (error) {
      logger.error(`Health check failed for integration ${id}:`, error);
      
      // 更新错误状态
      await prisma.communicationIntegration.update({
        where: { id },
        data: {
          lastHealthCheck: new Date(),
          healthStatus: HealthStatus.CRITICAL,
          lastErrorAt: new Date(),
          lastErrorMessage: error instanceof Error ? error.message : '健康检查失败'
        }
      });

      return {
        healthy: false,
        details: {
          responseTime: 0,
          errorCount: 1,
          lastError: error instanceof Error ? error.message : '健康检查失败',
          platform: 'unknown'
        }
      };
    }
  }

  /**
   * 启用/禁用集成
   */
  async toggleIntegration(id: string, enabled: boolean, userId: string): Promise<void> {
    try {
      await prisma.communicationIntegration.update({
        where: { id },
        data: {
          enabled,
          updatedBy: userId,
          updatedAt: new Date()
        }
      });

      logger.info(`Integration ${id} ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      logger.error(`Failed to toggle integration ${id}:`, error);
      throw error;
    }
  }

  /**
   * 获取集成指标
   */
  async getIntegrationMetrics(
    integrationId: string,
    period: string = 'day',
    limit: number = 30
  ) {
    try {
      const metrics = await prisma.communicationIntegrationMetrics.findMany({
        where: { 
          integrationId,
          period 
        },
        orderBy: { timestamp: 'desc' },
        take: limit
      });

      return metrics;
    } catch (error) {
      logger.error(`Failed to get metrics for integration ${integrationId}:`, error);
      throw error;
    }
  }

  // #region 私有方法

  /**
   * 验证配置
   */
  private validateConfig(type: CommunicationPlatform, config: any): void {
    switch (type) {
      case CommunicationPlatform.WECHAT_WORK:
        if (!config.corpId || !config.agentId || !config.secret) {
          throw new Error('企业微信配置不完整：缺少企业ID、应用ID或应用密钥');
        }
        break;
      case CommunicationPlatform.DINGTALK:
        if (!config.appKey || !config.appSecret || !config.agentId || !config.corpId) {
          throw new Error('钉钉配置不完整：缺少应用Key、应用密钥、应用ID或企业ID');
        }
        break;
      case CommunicationPlatform.SLACK:
        if (!config.botToken || !config.signingSecret || !config.defaultChannel) {
          throw new Error('Slack配置不完整：缺少Bot Token、Signing Secret或默认频道');
        }
        break;
      case CommunicationPlatform.FEISHU:
        if (!config.appId || !config.appSecret) {
          throw new Error('飞书配置不完整：缺少应用ID或应用密钥');
        }
        break;
      default:
        throw new Error('不支持的平台类型');
    }
  }

  /**
   * 记录执行日志
   */
  private async recordExecution(data: {
    integrationId: string;
    operation: OperationType;
    status: ExecutionStatus;
    requestData?: any;
    responseData?: any;
    errorMessage?: string;
    duration?: number;
  }): Promise<void> {
    try {
      await prisma.communicationIntegrationExecution.create({
        data: {
          id: uuidv4(),
          integrationId: data.integrationId,
          operation: data.operation,
          status: data.status,
          requestData: data.requestData,
          responseData: data.responseData,
          errorMessage: data.errorMessage,
          duration: data.duration,
          startedAt: new Date(),
          completedAt: new Date()
        }
      });
    } catch (error) {
      logger.error('Failed to record execution:', error);
    }
  }

  // #region 平台特定的连接测试方法

  private async testWeChatWorkConnection(config: any): Promise<boolean> {
    // 这里应该实现真正的企业微信API调用
    // 暂时返回模拟结果
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    return Math.random() > 0.2;
  }

  private async testDingTalkConnection(config: any): Promise<boolean> {
    // 这里应该实现真正的钉钉API调用
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    return Math.random() > 0.2;
  }

  private async testSlackConnection(config: any): Promise<boolean> {
    // 这里应该实现真正的Slack API调用
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    return Math.random() > 0.2;
  }

  private async testFeishuConnection(config: any): Promise<boolean> {
    // 这里应该实现真正的飞书API调用
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    return Math.random() > 0.2;
  }

  // #region 平台特定的消息发送方法

  private async sendWeChatWorkMessage(config: any, request: SendMessageRequest): Promise<SendMessageResponse> {
    // 这里应该实现真正的企业微信消息发送
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    const success = Math.random() > 0.1;
    return {
      success,
      messageId: success ? uuidv4() : undefined,
      error: success ? undefined : '发送失败'
    };
  }

  private async sendDingTalkMessage(config: any, request: SendMessageRequest): Promise<SendMessageResponse> {
    // 这里应该实现真正的钉钉消息发送
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    const success = Math.random() > 0.1;
    return {
      success,
      messageId: success ? uuidv4() : undefined,
      error: success ? undefined : '发送失败'
    };
  }

  private async sendSlackMessage(config: any, request: SendMessageRequest): Promise<SendMessageResponse> {
    // 这里应该实现真正的Slack消息发送
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    const success = Math.random() > 0.1;
    return {
      success,
      messageId: success ? uuidv4() : undefined,
      error: success ? undefined : '发送失败'
    };
  }

  private async sendFeishuMessage(config: any, request: SendMessageRequest): Promise<SendMessageResponse> {
    // 这里应该实现真正的飞书消息发送
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    const success = Math.random() > 0.1;
    return {
      success,
      messageId: success ? uuidv4() : undefined,
      error: success ? undefined : '发送失败'
    };
  }

  // #endregion
}

export const communicationIntegrationService = new CommunicationIntegrationService();
export default communicationIntegrationService;