# 通信集成服务

## 概述

通信集成服务提供了企业微信、钉钉、Slack、飞书等主流通信平台的集成管理功能。该服务实现了完整的CRUD操作、连接测试、健康检查、消息发送等功能。

## 功能特性

### 1. 集成管理
- **创建集成**: 支持创建各种平台的通信集成配置
- **更新集成**: 支持更新现有集成的配置信息
- **删除集成**: 支持删除不需要的集成配置
- **查询集成**: 支持按类型、ID等条件查询集成

### 2. 连接测试
- **平台验证**: 验证各平台的API密钥和配置是否正确
- **连接测试**: 测试与各平台的网络连接和认证状态
- **错误诊断**: 提供详细的错误信息和诊断建议

### 3. 健康监控
- **状态监控**: 实时监控各集成的健康状态
- **性能指标**: 收集响应时间、成功率等性能指标
- **异常告警**: 自动检测和报告集成异常

### 4. 消息发送
- **统一接口**: 提供统一的消息发送接口
- **平台适配**: 自动适配不同平台的消息格式
- **发送记录**: 记录所有消息发送的执行日志

## 支持的平台

### 企业微信 (WeChat Work)
- 企业应用集成
- 消息推送
- 文件上传
- 用户同步

### 钉钉 (DingTalk)
- 企业内部应用
- 机器人消息
- 群组通知
- 用户管理

### Slack
- 频道消息
- 文件分享
- 线程回复
- 表情反应

### 飞书 (Feishu)
- 应用集成
- 群组消息
- 加密通信
- 用户同步

## 数据库模型

### CommunicationIntegration
```sql
CREATE TABLE communication_integrations (
  id VARCHAR(191) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL,
  description TEXT,
  enabled BOOLEAN DEFAULT true,
  priority INT DEFAULT 5,
  status VARCHAR(50) DEFAULT 'active',
  config JSON NOT NULL,
  last_health_check DATETIME,
  health_status VARCHAR(50),
  last_error_at DATETIME,
  last_error_message TEXT,
  created_by VARCHAR(191) NOT NULL,
  updated_by VARCHAR(191),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### CommunicationIntegrationExecution
```sql
CREATE TABLE communication_integration_executions (
  id VARCHAR(191) PRIMARY KEY,
  integration_id VARCHAR(191) NOT NULL,
  operation VARCHAR(50) NOT NULL,
  status VARCHAR(50) NOT NULL,
  request_data JSON,
  response_data JSON,
  error_message TEXT,
  duration INT,
  started_at DATETIME NOT NULL,
  completed_at DATETIME,
  FOREIGN KEY (integration_id) REFERENCES communication_integrations(id)
);
```

### CommunicationIntegrationMetrics
```sql
CREATE TABLE communication_integration_metrics (
  id VARCHAR(191) PRIMARY KEY,
  integration_id VARCHAR(191) NOT NULL,
  timestamp DATETIME NOT NULL,
  period VARCHAR(20) NOT NULL,
  total_requests INT DEFAULT 0,
  success_requests INT DEFAULT 0,
  failed_requests INT DEFAULT 0,
  avg_response_time FLOAT DEFAULT 0,
  max_response_time FLOAT DEFAULT 0,
  min_response_time FLOAT DEFAULT 0,
  error_count INT DEFAULT 0,
  error_types JSON,
  availability FLOAT DEFAULT 0,
  UNIQUE KEY unique_metrics (integration_id, timestamp, period)
);
```

## API接口

### 基础CRUD操作
- `GET /api/v1/communication-integrations` - 获取所有集成
- `GET /api/v1/communication-integrations/:id` - 获取单个集成
- `POST /api/v1/communication-integrations` - 创建集成
- `PUT /api/v1/communication-integrations/:id` - 更新集成
- `DELETE /api/v1/communication-integrations/:id` - 删除集成

### 功能操作
- `POST /api/v1/communication-integrations/test-connection` - 测试连接
- `POST /api/v1/communication-integrations/send-message` - 发送消息
- `POST /api/v1/communication-integrations/:id/health-check` - 健康检查
- `PATCH /api/v1/communication-integrations/:id/toggle` - 启用/禁用集成
- `GET /api/v1/communication-integrations/:id/metrics` - 获取指标

## 使用示例

### 创建企业微信集成
```typescript
const integration = await communicationIntegrationService.createIntegration({
  name: '企业微信通知',
  type: CommunicationPlatform.WECHAT_WORK,
  description: '用于发送系统通知',
  enabled: true,
  priority: 5,
  config: {
    corpId: 'your_corp_id',
    agentId: 'your_agent_id',
    secret: 'your_secret',
    settings: {
      enableMessagePush: true,
      enableFileUpload: false,
      enableUserSync: false,
      messageTemplate: '系统通知：{message}',
      retryCount: 3,
      timeout: 5000
    }
  }
}, userId);
```

### 测试连接
```typescript
const result = await communicationIntegrationService.testConnection({
  type: CommunicationPlatform.WECHAT_WORK,
  config: {
    corpId: 'test_corp_id',
    agentId: 'test_agent_id',
    secret: 'test_secret',
    // ... 其他配置
  }
});
```

### 发送消息
```typescript
const result = await communicationIntegrationService.sendMessage({
  integrationId: 'integration_id',
  message: '这是一条测试消息',
  recipients: ['user1', 'user2'],
  channel: 'general'
});
```

## 配置说明

### 企业微信配置
- `corpId`: 企业ID，在企业微信管理后台获取
- `agentId`: 应用ID，在企业微信应用管理中获取
- `secret`: 应用密钥，用于获取访问令牌
- `token`: 可选，用于消息验证
- `encodingAESKey`: 可选，用于消息加解密

### 钉钉配置
- `appKey`: 应用Key，在钉钉开放平台获取
- `appSecret`: 应用密钥，用于获取访问令牌
- `agentId`: 应用ID，在企业内部应用管理中获取
- `corpId`: 企业ID，在钉钉企业管理后台获取

### Slack配置
- `botToken`: Bot用户OAuth令牌
- `signingSecret`: 应用签名密钥
- `defaultChannel`: 默认发送频道
- `appToken`: 可选，用于Socket Mode

### 飞书配置
- `appId`: 应用ID，在飞书开放平台获取
- `appSecret`: 应用密钥，用于获取访问令牌
- `verificationToken`: 可选，用于验证请求来源
- `encryptKey`: 可选，用于消息加解密

## 监控和日志

### 执行日志
所有集成操作都会记录详细的执行日志，包括：
- 操作类型和时间
- 请求和响应数据
- 执行状态和错误信息
- 执行时长统计

### 性能指标
系统自动收集以下性能指标：
- 请求总数和成功率
- 平均响应时间
- 错误类型统计
- 可用性百分比

### 健康状态
集成健康状态分为三个级别：
- `healthy`: 正常状态
- `warning`: 警告状态
- `critical`: 严重错误状态

## 扩展开发

### 添加新平台
1. 在`CommunicationPlatform`枚举中添加新平台
2. 创建平台特定的配置接口
3. 实现平台特定的连接测试和消息发送方法
4. 更新类型定义和验证逻辑

### 自定义消息格式
1. 扩展`MessageConfig`接口
2. 实现平台特定的消息构建方法
3. 添加消息模板支持

### 增强监控
1. 扩展`CommunicationIntegrationMetrics`接口
2. 实现自定义指标收集
3. 添加告警规则配置

## 注意事项

1. **安全性**: 所有敏感配置信息都经过加密存储
2. **限流**: 各平台API都有相应的限流策略
3. **重试**: 支持配置重试次数和超时时间
4. **日志**: 所有操作都有完整的审计日志
5. **错误处理**: 完善的错误处理和恢复机制

## 故障排除

### 常见问题
1. **连接失败**: 检查网络连接和防火墙设置
2. **认证失败**: 验证API密钥和权限配置
3. **消息发送失败**: 检查消息格式和接收者配置
4. **性能问题**: 查看监控指标和日志分析

### 调试方法
1. 启用详细日志记录
2. 使用连接测试功能
3. 检查健康状态报告
4. 分析执行日志和错误信息
