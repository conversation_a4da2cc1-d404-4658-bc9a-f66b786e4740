/**
 * 外部API集成服务
 * 提供统一的外部系统集成接口和管理功能
 */

import { 
  IntegrationConfig, 
  IntegrationRequest, 
  IntegrationResponse, 
  IntegrationMetrics,
  IntegrationEvent,
  BatchIntegrationRequest,
  BatchIntegrationResponse,
  IntegrationExecution,
  IntegrationContext,
  IntegrationType,
  AuthType,
  IntegrationStatus,
  OperationType
} from '../types/integration';
import { IntegrationAuthManager } from './integration-auth.service';
import { IntegrationMonitor } from './integration-monitor.service';
import { logger } from '../utils/logger.util';
import { v4 as uuidv4 } from 'uuid';

/**
 * 外部API集成管理器
 * 负责管理所有外部系统的集成配置和执行
 */
export class IntegrationManager {
  private integrations: Map<string, IntegrationConfig> = new Map();
  private authManager: IntegrationAuthManager;
  private monitor: IntegrationMonitor;
  private activeExecutions: Map<string, IntegrationExecution> = new Map();

  constructor() {
    this.authManager = new IntegrationAuthManager();
    this.monitor = new IntegrationMonitor();
  }

  // #region 集成配置管理
  
  /**
   * 注册新的集成配置
   */
  async registerIntegration(config: IntegrationConfig): Promise<void> {
    try {
      // 验证配置
      this.validateIntegrationConfig(config);
      
      // 测试连接
      await this.testConnection(config);
      
      // 存储配置
      this.integrations.set(config.id, config);
      
      // 记录事件
      await this.monitor.recordEvent({
        id: uuidv4(),
        integrationId: config.id,
        type: 'info',
        operation: OperationType.CREATE,
        message: `集成 ${config.name} 注册成功`,
        timestamp: new Date()
      });
      
      logger.info(`Integration registered: ${config.name} (${config.id})`);
    } catch (error) {
      logger.error(`Failed to register integration ${config.name}:`, error);
      throw error;
    }
  }

  /**
   * 更新集成配置
   */
  async updateIntegration(integrationId: string, updates: Partial<IntegrationConfig>): Promise<void> {
    const existing = this.integrations.get(integrationId);
    if (!existing) {
      throw new Error(`Integration not found: ${integrationId}`);
    }

    try {
      const updated = { ...existing, ...updates, updatedAt: new Date() };
      
      // 验证更新后的配置
      this.validateIntegrationConfig(updated);
      
      // 如果凭据发生变化，重新测试连接
      if (updates.auth) {
        await this.testConnection(updated);
      }
      
      this.integrations.set(integrationId, updated);
      
      await this.monitor.recordEvent({
        id: uuidv4(),
        integrationId,
        type: 'info',
        operation: OperationType.UPDATE,
        message: `集成 ${updated.name} 更新成功`,
        timestamp: new Date()
      });
      
      logger.info(`Integration updated: ${integrationId}`);
    } catch (error) {
      logger.error(`Failed to update integration ${integrationId}:`, error);
      throw error;
    }
  }

  /**
   * 删除集成配置
   */
  async removeIntegration(integrationId: string): Promise<void> {
    const integration = this.integrations.get(integrationId);
    if (!integration) {
      throw new Error(`Integration not found: ${integrationId}`);
    }

    // 停止所有正在执行的操作
    for (const [execId, execution] of this.activeExecutions) {
      if (execution.integrationId === integrationId) {
        await this.cancelExecution(execId);
      }
    }

    this.integrations.delete(integrationId);
    
    await this.monitor.recordEvent({
      id: uuidv4(),
      integrationId,
      type: 'info',
      operation: OperationType.DELETE,
      message: `集成 ${integration.name} 已删除`,
      timestamp: new Date()
    });
    
    logger.info(`Integration removed: ${integrationId}`);
  }

  /**
   * 获取集成配置
   */
  getIntegration(integrationId: string): IntegrationConfig | undefined {
    return this.integrations.get(integrationId);
  }

  /**
   * 获取所有集成配置
   */
  getAllIntegrations(): IntegrationConfig[] {
    return Array.from(this.integrations.values());
  }

  /**
   * 按类型获取集成配置
   */
  getIntegrationsByType(type: IntegrationType): IntegrationConfig[] {
    return Array.from(this.integrations.values()).filter(integration => integration.type === type);
  }

  // #endregion

  // #region 操作执行

  /**
   * 执行单个集成操作
   */
  async executeOperation(
    request: IntegrationRequest, 
    context: IntegrationContext
  ): Promise<IntegrationResponse> {
    const startTime = Date.now();
    const requestId = uuidv4();
    
    try {
      const integration = this.integrations.get(request.integrationId);
      if (!integration) {
        throw new Error(`Integration not found: ${request.integrationId}`);
      }

      if (!integration.enabled) {
        throw new Error(`Integration is disabled: ${request.integrationId}`);
      }

      // 创建执行记录
      const execution: IntegrationExecution = {
        id: requestId,
        integrationId: request.integrationId,
        operation: request.operation,
        status: 'running',
        startedAt: new Date(),
        request,
        context
      };
      this.activeExecutions.set(requestId, execution);

      // 获取认证信息
      const authHeaders = await this.authManager.getAuthHeaders(integration.auth);
      
      // 构建完整的请求
      const endpoint = integration.endpoints[request.endpoint];
      if (!endpoint) {
        throw new Error(`Endpoint not found: ${request.endpoint}`);
      }

      const fullUrl = this.buildUrl(endpoint.url, request.parameters);
      const headers = {
        ...endpoint.headers,
        ...authHeaders,
        ...request.headers
      };

      // 执行API调用
      const response = await this.makeApiCall({
        url: fullUrl,
        method: endpoint.method,
        headers,
        body: request.body,
        timeout: request.timeout || endpoint.timeout || 30000
      });

      const duration = Date.now() - startTime;
      
      // 处理响应数据映射
      const processedData = this.processResponseData(response.data, integration, request.endpoint);

      // 构建响应
      const integrationResponse: IntegrationResponse = {
        success: true,
        data: processedData,
        metadata: {
          requestId,
          timestamp: new Date(),
          duration,
          endpoint: request.endpoint,
          statusCode: response.status
        }
      };

      // 更新执行记录
      execution.status = 'completed';
      execution.completedAt = new Date();
      execution.duration = duration;
      execution.response = integrationResponse;

      // 记录监控指标
      await this.monitor.recordMetrics(request.integrationId, {
        success: true,
        duration,
        operation: request.operation,
        timestamp: new Date()
      });

      this.activeExecutions.delete(requestId);
      return integrationResponse;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      // 更新执行记录
      const execution = this.activeExecutions.get(requestId);
      if (execution) {
        execution.status = 'failed';
        execution.completedAt = new Date();
        execution.duration = duration;
        execution.error = {
          code: 'EXECUTION_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        };
      }

      // 记录错误事件
      await this.monitor.recordEvent({
        id: uuidv4(),
        integrationId: request.integrationId,
        type: 'error',
        operation: request.operation,
        message: `操作执行失败: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { request, error: error instanceof Error ? error.stack : error },
        timestamp: new Date()
      });

      // 记录监控指标
      await this.monitor.recordMetrics(request.integrationId, {
        success: false,
        duration,
        operation: request.operation,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      this.activeExecutions.delete(requestId);

      // 构建错误响应
      const errorResponse: IntegrationResponse = {
        success: false,
        error: {
          code: 'EXECUTION_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          details: error
        },
        metadata: {
          requestId,
          timestamp: new Date(),
          duration,
          endpoint: request.endpoint
        }
      };

      return errorResponse;
    }
  }

  /**
   * 批量执行集成操作
   */
  async executeBatchOperations(
    batchRequest: BatchIntegrationRequest,
    context: IntegrationContext
  ): Promise<BatchIntegrationResponse> {
    const startTime = Date.now();
    const results: IntegrationResponse[] = [];
    
    try {
      if (batchRequest.parallel) {
        // 并行执行
        const promises = batchRequest.requests.map(request => 
          this.executeOperation(request, context)
        );
        
        if (batchRequest.continueOnError) {
          // 继续执行即使有错误
          const settledResults = await Promise.allSettled(promises);
          results.push(...settledResults.map(result => 
            result.status === 'fulfilled' ? result.value : {
              success: false,
              error: {
                code: 'BATCH_ERROR',
                message: 'Operation failed in batch execution',
                details: result.reason
              },
              metadata: {
                requestId: uuidv4(),
                timestamp: new Date(),
                duration: 0,
                endpoint: 'unknown'
              }
            }
          ));
        } else {
          // 任何错误都停止执行
          results.push(...await Promise.all(promises));
        }
      } else {
        // 串行执行
        for (const request of batchRequest.requests) {
          try {
            const result = await this.executeOperation(request, context);
            results.push(result);
            
            if (!result.success && !batchRequest.continueOnError) {
              break;
            }
          } catch (error) {
            const errorResult: IntegrationResponse = {
              success: false,
              error: {
                code: 'BATCH_ERROR',
                message: error instanceof Error ? error.message : 'Unknown error',
                details: error
              },
              metadata: {
                requestId: uuidv4(),
                timestamp: new Date(),
                duration: 0,
                endpoint: 'unknown'
              }
            };
            results.push(errorResult);
            
            if (!batchRequest.continueOnError) {
              break;
            }
          }
        }
      }

      const duration = Date.now() - startTime;
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.length - successCount;

      return {
        results,
        summary: {
          total: batchRequest.requests.length,
          success: successCount,
          failed: failedCount,
          duration
        }
      };

    } catch (error) {
      logger.error('Batch execution failed:', error);
      throw error;
    }
  }

  // #endregion

  // #region 监控和健康检查

  /**
   * 获取集成监控指标
   */
  async getMetrics(integrationId: string): Promise<IntegrationMetrics | null> {
    return this.monitor.getMetrics(integrationId);
  }

  /**
   * 获取集成事件
   */
  async getEvents(integrationId: string, limit = 100): Promise<IntegrationEvent[]> {
    return this.monitor.getEvents(integrationId, limit);
  }

  /**
   * 健康检查
   */
  async healthCheck(integrationId: string): Promise<{ healthy: boolean; details?: any }> {
    try {
      const integration = this.integrations.get(integrationId);
      if (!integration) {
        return { healthy: false, details: 'Integration not found' };
      }

      // 执行简单的健康检查请求
      const healthEndpoint = integration.endpoints['health'] || integration.endpoints['ping'];
      if (healthEndpoint) {
        const response = await this.executeOperation({
          integrationId,
          operation: OperationType.HEALTH_CHECK,
          endpoint: 'health',
          timeout: 10000
        }, {
          userId: 'system',
          userRole: 'system',
          permissions: [],
          environment: 'production'
        });

        return { 
          healthy: response.success, 
          details: response.success ? response.data : response.error 
        };
      }

      // 如果没有健康检查端点，检查最近的执行状态
      const metrics = await this.getMetrics(integrationId);
      if (metrics) {
        return { 
          healthy: metrics.healthStatus === 'healthy',
          details: { 
            availability: metrics.availability,
            lastHealthCheck: metrics.lastHealthCheck,
            status: metrics.healthStatus
          }
        };
      }

      return { healthy: true, details: 'No health check endpoint configured' };

    } catch (error) {
      logger.error(`Health check failed for integration ${integrationId}:`, error);
      return { 
        healthy: false, 
        details: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // #endregion

  // #region 私有方法

  /**
   * 验证集成配置
   */
  private validateIntegrationConfig(config: IntegrationConfig): void {
    if (!config.id || !config.name || !config.type) {
      throw new Error('Integration config must have id, name, and type');
    }

    if (!config.auth || !config.endpoints || Object.keys(config.endpoints).length === 0) {
      throw new Error('Integration config must have auth and at least one endpoint');
    }

    // 验证认证配置
    this.authManager.validateAuthConfig(config.auth);

    // 验证端点配置
    for (const [key, endpoint] of Object.entries(config.endpoints)) {
      if (!endpoint.url || !endpoint.method) {
        throw new Error(`Endpoint ${key} must have url and method`);
      }
    }
  }

  /**
   * 测试连接
   */
  private async testConnection(config: IntegrationConfig): Promise<void> {
    try {
      // 找到测试端点
      const testEndpoint = config.endpoints['test'] || 
                          config.endpoints['health'] || 
                          config.endpoints['ping'] ||
                          Object.values(config.endpoints)[0];

      if (!testEndpoint) {
        throw new Error('No endpoint available for connection test');
      }

      const authHeaders = await this.authManager.getAuthHeaders(config.auth);
      
      await this.makeApiCall({
        url: testEndpoint.url,
        method: 'GET',
        headers: { ...testEndpoint.headers, ...authHeaders },
        timeout: 10000
      });

      logger.info(`Connection test successful for integration: ${config.name}`);
    } catch (error) {
      logger.error(`Connection test failed for integration ${config.name}:`, error);
      throw new Error(`Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 构建完整URL
   */
  private buildUrl(baseUrl: string, parameters?: Record<string, any>): string {
    let url = baseUrl;
    
    if (parameters) {
      // 替换路径参数 (如 /api/users/{userId})
      for (const [key, value] of Object.entries(parameters)) {
        url = url.replace(`{${key}}`, encodeURIComponent(String(value)));
      }
      
      // 添加查询参数
      const queryParams = new URLSearchParams();
      for (const [key, value] of Object.entries(parameters)) {
        if (!url.includes(`{${key}}`)) {
          queryParams.append(key, String(value));
        }
      }
      
      if (queryParams.toString()) {
        url += (url.includes('?') ? '&' : '?') + queryParams.toString();
      }
    }
    
    return url;
  }

  /**
   * 发送API请求
   */
  private async makeApiCall(options: {
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
  }): Promise<{ data: any; status: number; headers: Record<string, string> }> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000);

    try {
      const response = await fetch(options.url, {
        method: options.method,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        body: options.body ? JSON.stringify(options.body) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      let data;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return {
        data,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  /**
   * 处理响应数据映射
   */
  private processResponseData(data: any, integration: IntegrationConfig, endpointKey: string): any {
    const mappings = integration.dataMapping[endpointKey];
    if (!mappings || mappings.length === 0) {
      return data;
    }

    const result: any = {};
    
    for (const mapping of mappings) {
      try {
        let value = this.getNestedValue(data, mapping.source);
        
        // 应用转换
        if (mapping.transform && value !== undefined) {
          // 简单的JavaScript表达式求值 (生产环境中应该使用更安全的方法)
          const func = new Function('value', `return ${mapping.transform}`);
          value = func(value);
        }
        
        // 使用默认值
        if (value === undefined && mapping.defaultValue !== undefined) {
          value = mapping.defaultValue;
        }
        
        // 检查必填字段
        if (mapping.required && value === undefined) {
          throw new Error(`Required field ${mapping.target} is missing`);
        }
        
        if (value !== undefined) {
          this.setNestedValue(result, mapping.target, value);
        }
      } catch (error) {
        logger.warn(`Data mapping failed for ${mapping.source} -> ${mapping.target}:`, error);
      }
    }
    
    return result;
  }

  /**
   * 获取嵌套对象值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 设置嵌套对象值
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!(key in current)) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    target[lastKey] = value;
  }

  /**
   * 取消执行
   */
  private async cancelExecution(executionId: string): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (execution) {
      execution.status = 'cancelled';
      execution.completedAt = new Date();
      execution.duration = Date.now() - execution.startedAt.getTime();
      this.activeExecutions.delete(executionId);
      
      logger.info(`Execution cancelled: ${executionId}`);
    }
  }

  // #endregion
}

// 导出单例实例
export const integrationManager = new IntegrationManager();