/**
 * 工作流模板数据库服务
 * 处理工作流模板的数据库操作和数据迁移
 */

import { PrismaClient } from '@prisma/client';
import { WorkflowTemplate, TemplateCategory } from './workflow-template.service';
import type { 
  WorkflowCategory as WorkflowCategoryEnum,
  WorkflowTemplateDifficulty,
  WorkflowTemplateUsageAction 
} from '@prisma/client';

const prisma = new PrismaClient();

export interface DatabaseWorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  category: WorkflowCategoryEnum;
  subcategory: string;
  tags: string[];
  author: string;
  version: string;
  isBuiltin: boolean;
  isPopular: boolean;
  isActive: boolean;
  usageCount: number;
  rating: number;
  difficulty: WorkflowTemplateDifficulty;
  estimatedDuration: number;
  previewThumbnail?: string;
  previewScreenshots: string[];
  previewDemoVideo?: string;
  requirementsPermissions: string[];
  requirementsIntegrations: string[];
  requirementsMinimumVersion: string;
  workflowDefinition: any;
  metadata?: any;
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

class WorkflowTemplateDbService {

  /**
   * 初始化模板数据库
   * 创建预置分类和模板数据
   */
  async initializeTemplateDatabase(): Promise<void> {
    console.log('开始初始化工作流模板数据库...');

    try {
      // 1. 检查是否已经初始化
      const existingCategories = await prisma.workflowTemplateCategory.count();
      if (existingCategories > 0) {
        console.log('模板数据库已经初始化，跳过初始化过程');
        return;
      }

      // 2. 初始化分类数据
      await this.initializeCategories();

      // 3. 初始化预置模板
      await this.initializeBuiltinTemplates();

      console.log('工作流模板数据库初始化完成');
    } catch (error) {
      console.error('初始化工作流模板数据库失败:', error);
      throw error;
    }
  }

  /**
   * 初始化分类数据
   */
  private async initializeCategories(): Promise<void> {
    console.log('初始化模板分类...');

    const categories = [
      {
        categoryId: 'SERVICE_AUTOMATION' as WorkflowCategoryEnum,
        name: '服务自动化',
        description: '自动化服务管理和运维任务',
        icon: '🔧',
        subcategories: [
          { id: 'deployment', name: '部署自动化', description: '应用部署和更新流程' },
          { id: 'scaling', name: '弹性伸缩', description: '资源自动伸缩管理' },
          { id: 'restart', name: '服务重启', description: '服务异常自动恢复' },
          { id: 'health_check', name: '健康检查', description: '服务健康状态监控' }
        ],
        displayOrder: 1
      },
      {
        categoryId: 'SLA_MONITORING' as WorkflowCategoryEnum,
        name: 'SLA监控',
        description: 'SLA指标监控和违规处理',
        icon: '📊',
        subcategories: [
          { id: 'availability', name: '可用性监控', description: '服务可用性指标跟踪' },
          { id: 'performance', name: '性能监控', description: '响应时间和吞吐量监控' },
          { id: 'violation', name: '违规处理', description: 'SLA违规自动响应流程' },
          { id: 'reporting', name: '报告生成', description: 'SLA报告自动生成' }
        ],
        displayOrder: 2
      },
      {
        categoryId: 'ALERT_PROCESSING' as WorkflowCategoryEnum,
        name: '告警处理',
        description: '智能告警处理和响应',
        icon: '🚨',
        subcategories: [
          { id: 'escalation', name: '告警升级', description: '告警逐级升级处理' },
          { id: 'correlation', name: '告警关联', description: '相关告警自动关联' },
          { id: 'suppression', name: '告警抑制', description: '重复告警智能过滤' },
          { id: 'notification', name: '通知分发', description: '多渠道告警通知' }
        ],
        displayOrder: 3
      },
      {
        categoryId: 'BACKUP_AUTOMATION' as WorkflowCategoryEnum,
        name: '备份自动化',
        description: '数据备份和恢复流程',
        icon: '💾',
        subcategories: [
          { id: 'database', name: '数据库备份', description: '数据库定期备份' },
          { id: 'file_backup', name: '文件备份', description: '重要文件系统备份' },
          { id: 'disaster_recovery', name: '灾难恢复', description: '灾难恢复流程' },
          { id: 'cleanup', name: '备份清理', description: '过期备份自动清理' }
        ],
        displayOrder: 4
      },
      {
        categoryId: 'MAINTENANCE' as WorkflowCategoryEnum,
        name: '维护任务',
        description: '系统维护和优化任务',
        icon: '🔧',
        subcategories: [
          { id: 'patch_management', name: '补丁管理', description: '系统补丁自动更新' },
          { id: 'cleanup', name: '系统清理', description: '日志和临时文件清理' },
          { id: 'optimization', name: '性能优化', description: '系统性能调优' },
          { id: 'security_scan', name: '安全扫描', description: '安全漏洞扫描' }
        ],
        displayOrder: 5
      },
      {
        categoryId: 'APPROVAL_PROCESS' as WorkflowCategoryEnum,
        name: '审批流程',
        description: '运维操作审批流程',
        icon: '✅',
        subcategories: [
          { id: 'change_request', name: '变更请求', description: '系统变更审批流程' },
          { id: 'access_request', name: '权限申请', description: '系统访问权限审批' },
          { id: 'resource_request', name: '资源申请', description: '计算资源申请审批' },
          { id: 'emergency', name: '紧急操作', description: '紧急操作快速审批' }
        ],
        displayOrder: 6
      },
      {
        categoryId: 'NOTIFICATION' as WorkflowCategoryEnum,
        name: '通知流程',
        description: '多渠道通知和消息推送',
        icon: '📢',
        subcategories: [
          { id: 'incident', name: '事件通知', description: '故障事件通知流程' },
          { id: 'maintenance', name: '维护通知', description: '系统维护通知' },
          { id: 'report', name: '报告推送', description: '定期报告推送' },
          { id: 'status_update', name: '状态更新', description: '服务状态变更通知' }
        ],
        displayOrder: 7
      },
      {
        categoryId: 'DATA_PROCESSING' as WorkflowCategoryEnum,
        name: '数据处理',
        description: '数据采集、处理和分析',
        icon: '📈',
        subcategories: [
          { id: 'collection', name: '数据采集', description: '系统数据自动采集' },
          { id: 'analysis', name: '数据分析', description: '运维数据分析处理' },
          { id: 'migration', name: '数据迁移', description: '数据迁移和同步' },
          { id: 'archival', name: '数据归档', description: '历史数据归档处理' }
        ],
        displayOrder: 8
      },
      {
        categoryId: 'INTEGRATION' as WorkflowCategoryEnum,
        name: '集成流程',
        description: '第三方系统集成',
        icon: '🔗',
        subcategories: [
          { id: 'api_sync', name: 'API同步', description: '第三方API数据同步' },
          { id: 'webhook', name: 'Webhook处理', description: 'Webhook事件处理' },
          { id: 'file_transfer', name: '文件传输', description: 'FTP/SFTP文件传输' },
          { id: 'message_queue', name: '消息队列', description: '消息队列处理' }
        ],
        displayOrder: 9
      },
      {
        categoryId: 'CUSTOM' as WorkflowCategoryEnum,
        name: '自定义模板',
        description: '用户自定义工作流模板',
        icon: '🎨',
        subcategories: [
          { id: 'user_created', name: '用户创建', description: '用户自定义模板' },
          { id: 'organization', name: '组织模板', description: '组织内部模板' },
          { id: 'imported', name: '导入模板', description: '外部导入模板' }
        ],
        displayOrder: 10
      }
    ];

    for (const category of categories) {
      await prisma.workflowTemplateCategory.create({
        data: {
          categoryId: category.categoryId,
          name: category.name,
          description: category.description,
          icon: category.icon,
          subcategories: category.subcategories,
          displayOrder: category.displayOrder
        }
      });
      console.log(`创建分类: ${category.name}`);
    }

    console.log(`成功创建 ${categories.length} 个模板分类`);
  }

  /**
   * 初始化预置模板
   */
  private async initializeBuiltinTemplates(): Promise<void> {
    console.log('初始化预置模板...');

    const templates = [
      // 自动部署模板
      {
        id: 'builtin-auto-deployment',
        name: '自动部署流程',
        description: '应用程序自动部署和健康检查',
        category: 'SERVICE_AUTOMATION' as WorkflowCategoryEnum,
        subcategory: 'deployment',
        tags: ['部署', '自动化', 'CI/CD', '健康检查'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        isActive: true,
        usageCount: 150,
        rating: 4.8,
        difficulty: 'intermediate' as WorkflowTemplateDifficulty,
        estimatedDuration: 15,
        previewThumbnail: '/templates/auto-deployment-thumb.png',
        previewScreenshots: ['/templates/auto-deployment-1.png'],
        previewDemoVideo: '/templates/auto-deployment-demo.mp4',
        requirementsPermissions: ['deployment.manage', 'service.restart'],
        requirementsIntegrations: ['docker', 'kubernetes'],
        requirementsMinimumVersion: '1.0.0',
        workflowDefinition: this.getAutoDeploymentWorkflow(),
        metadata: {
          stepsCount: 6,
          hasVariables: true,
          downloads: 150
        }
      },

      // SLA违规响应模板
      {
        id: 'builtin-sla-violation',
        name: 'SLA违规响应',
        description: 'SLA违规自动检测和响应处理',
        category: 'SLA_MONITORING' as WorkflowCategoryEnum,
        subcategory: 'violation',
        tags: ['SLA', '监控', '告警', '自动响应'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        isActive: true,
        usageCount: 89,
        rating: 4.6,
        difficulty: 'advanced' as WorkflowTemplateDifficulty,
        estimatedDuration: 5,
        previewThumbnail: '/templates/sla-violation-thumb.png',
        previewScreenshots: ['/templates/sla-violation-1.png'],
        requirementsPermissions: ['sla.monitor', 'alert.create'],
        requirementsIntegrations: ['monitoring', 'notification'],
        requirementsMinimumVersion: '1.0.0',
        workflowDefinition: this.getSLAViolationWorkflow(),
        metadata: {
          stepsCount: 4,
          hasVariables: true,
          downloads: 89
        }
      },

      // 告警升级模板
      {
        id: 'builtin-alert-escalation',
        name: '告警升级流程',
        description: '多级告警升级和通知流程',
        category: 'ALERT_PROCESSING' as WorkflowCategoryEnum,
        subcategory: 'escalation',
        tags: ['告警', '升级', '通知', 'oncall'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        isActive: true,
        usageCount: 124,
        rating: 4.7,
        difficulty: 'intermediate' as WorkflowTemplateDifficulty,
        estimatedDuration: 10,
        previewThumbnail: '/templates/alert-escalation-thumb.png',
        previewScreenshots: ['/templates/alert-escalation-1.png'],
        requirementsPermissions: ['alert.manage', 'notification.send'],
        requirementsIntegrations: ['alerting', 'oncall'],
        requirementsMinimumVersion: '1.0.0',
        workflowDefinition: this.getAlertEscalationWorkflow(),
        metadata: {
          stepsCount: 5,
          hasVariables: true,
          downloads: 124
        }
      },

      // 数据库备份模板
      {
        id: 'builtin-database-backup',
        name: '数据库定时备份',
        description: '数据库定时备份和验证流程',
        category: 'BACKUP_AUTOMATION' as WorkflowCategoryEnum,
        subcategory: 'database',
        tags: ['备份', '数据库', '定时任务', '验证'],
        author: 'System',
        version: '1.0.0',
        isBuiltin: true,
        isPopular: true,
        isActive: true,
        usageCount: 203,
        rating: 4.9,
        difficulty: 'beginner' as WorkflowTemplateDifficulty,
        estimatedDuration: 30,
        previewThumbnail: '/templates/db-backup-thumb.png',
        previewScreenshots: ['/templates/db-backup-1.png'],
        requirementsPermissions: ['database.backup', 'storage.write'],
        requirementsIntegrations: ['database', 'storage'],
        requirementsMinimumVersion: '1.0.0',
        workflowDefinition: this.getDatabaseBackupWorkflow(),
        metadata: {
          stepsCount: 6,
          hasVariables: true,
          downloads: 203
        }
      }
    ];

    for (const template of templates) {
      await prisma.workflowTemplate.create({
        data: {
          id: template.id,
          name: template.name,
          description: template.description,
          category: template.category,
          subcategory: template.subcategory,
          tags: template.tags,
          author: template.author,
          version: template.version,
          isBuiltin: template.isBuiltin,
          isPopular: template.isPopular,
          isActive: template.isActive,
          usageCount: template.usageCount,
          rating: template.rating,
          difficulty: template.difficulty,
          estimatedDuration: template.estimatedDuration,
          previewThumbnail: template.previewThumbnail,
          previewScreenshots: template.previewScreenshots,
          previewDemoVideo: template.previewDemoVideo,
          requirementsPermissions: template.requirementsPermissions,
          requirementsIntegrations: template.requirementsIntegrations,
          requirementsMinimumVersion: template.requirementsMinimumVersion,
          workflowDefinition: template.workflowDefinition,
          metadata: template.metadata
        }
      });
      console.log(`创建预置模板: ${template.name}`);
    }

    console.log(`成功创建 ${templates.length} 个预置模板`);
  }

  /**
   * 获取所有数据库模板
   */
  async getAllTemplates(): Promise<DatabaseWorkflowTemplate[]> {
    const templates = await prisma.workflowTemplate.findMany({
      orderBy: [
        { isPopular: 'desc' },
        { rating: 'desc' },
        { usageCount: 'desc' }
      ]
    });

    return templates.map(template => this.transformToDbFormat(template));
  }

  /**
   * 根据分类获取模板
   */
  async getTemplatesByCategory(category: WorkflowCategoryEnum): Promise<DatabaseWorkflowTemplate[]> {
    const templates = await prisma.workflowTemplate.findMany({
      where: { category, isActive: true },
      orderBy: { usageCount: 'desc' }
    });

    return templates.map(template => this.transformToDbFormat(template));
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(limit = 10): Promise<DatabaseWorkflowTemplate[]> {
    const templates = await prisma.workflowTemplate.findMany({
      where: { isPopular: true, isActive: true },
      orderBy: { usageCount: 'desc' },
      take: limit
    });

    return templates.map(template => this.transformToDbFormat(template));
  }

  /**
   * 搜索模板
   */
  async searchTemplates(query: string): Promise<DatabaseWorkflowTemplate[]> {
    const templates = await prisma.workflowTemplate.findMany({
      where: {
        isActive: true,
        OR: [
          { name: { contains: query } },
          { description: { contains: query } }
        ]
      },
      orderBy: { rating: 'desc' }
    });

    return templates.map(template => this.transformToDbFormat(template));
  }

  /**
   * 创建模板
   */
  async createTemplate(data: Omit<DatabaseWorkflowTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const template = await prisma.workflowTemplate.create({
      data: {
        name: data.name,
        description: data.description,
        category: data.category,
        subcategory: data.subcategory,
        tags: data.tags,
        author: data.author,
        version: data.version,
        isBuiltin: data.isBuiltin,
        isPopular: data.isPopular,
        isActive: data.isActive,
        usageCount: data.usageCount,
        rating: data.rating,
        difficulty: data.difficulty,
        estimatedDuration: data.estimatedDuration,
        previewThumbnail: data.previewThumbnail,
        previewScreenshots: data.previewScreenshots,
        previewDemoVideo: data.previewDemoVideo,
        requirementsPermissions: data.requirementsPermissions,
        requirementsIntegrations: data.requirementsIntegrations,
        requirementsMinimumVersion: data.requirementsMinimumVersion,
        workflowDefinition: data.workflowDefinition,
        metadata: data.metadata,
        createdBy: data.createdBy
      }
    });

    return template.id;
  }

  /**
   * 记录模板使用
   */
  async logTemplateUsage(
    templateId: string, 
    userId: string, 
    action: WorkflowTemplateUsageAction, 
    metadata?: any
  ): Promise<void> {
    await prisma.workflowTemplateUsageLog.create({
      data: {
        templateId,
        userId,
        action,
        metadata
      }
    });

    // 更新使用计数
    if (action === 'apply') {
      await prisma.workflowTemplate.update({
        where: { id: templateId },
        data: {
          usageCount: { increment: 1 }
        }
      });
    }
  }

  /**
   * 更新模板评分
   */
  async updateTemplateRating(templateId: string, userId: string, rating: number, comment?: string): Promise<void> {
    // 创建或更新评分
    await prisma.workflowTemplateRating.upsert({
      where: {
        templateId_userId: {
          templateId,
          userId
        }
      },
      update: {
        rating,
        comment
      },
      create: {
        templateId,
        userId,
        rating,
        comment
      }
    });

    // 重新计算平均评分
    const avgRating = await prisma.workflowTemplateRating.aggregate({
      where: { templateId },
      _avg: { rating: true }
    });

    if (avgRating._avg.rating !== null) {
      await prisma.workflowTemplate.update({
        where: { id: templateId },
        data: {
          rating: avgRating._avg.rating
        }
      });
    }
  }

  /**
   * 获取模板统计信息
   */
  async getTemplateStats(): Promise<{
    totalTemplates: number;
    builtinTemplates: number;
    customTemplates: number;
    popularTemplates: number;
    categoryStats: Array<{
      category: WorkflowCategoryEnum;
      count: number;
    }>;
    difficultyStats: {
      beginner: number;
      intermediate: number;
      advanced: number;
    };
  }> {
    const [total, builtin, popular, categoryStats, difficultyStats] = await Promise.all([
      prisma.workflowTemplate.count({ where: { isActive: true } }),
      prisma.workflowTemplate.count({ where: { isBuiltin: true, isActive: true } }),
      prisma.workflowTemplate.count({ where: { isPopular: true, isActive: true } }),
      prisma.workflowTemplate.groupBy({
        by: ['category'],
        where: { isActive: true },
        _count: true
      }),
      prisma.workflowTemplate.groupBy({
        by: ['difficulty'],
        where: { isActive: true },
        _count: true
      })
    ]);

    return {
      totalTemplates: total,
      builtinTemplates: builtin,
      customTemplates: total - builtin,
      popularTemplates: popular,
      categoryStats: categoryStats.map(stat => ({
        category: stat.category,
        count: stat._count
      })),
      difficultyStats: {
        beginner: difficultyStats.find(d => d.difficulty === 'beginner')?._count || 0,
        intermediate: difficultyStats.find(d => d.difficulty === 'intermediate')?._count || 0,
        advanced: difficultyStats.find(d => d.difficulty === 'advanced')?._count || 0
      }
    };
  }

  /**
   * 转换为数据库格式
   */
  private transformToDbFormat(template: any): DatabaseWorkflowTemplate {
    return {
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      subcategory: template.subcategory,
      tags: template.tags as string[],
      author: template.author,
      version: template.version,
      isBuiltin: template.isBuiltin,
      isPopular: template.isPopular,
      isActive: template.isActive,
      usageCount: template.usageCount,
      rating: parseFloat(template.rating.toString()),
      difficulty: template.difficulty,
      estimatedDuration: template.estimatedDuration,
      previewThumbnail: template.previewThumbnail,
      previewScreenshots: template.previewScreenshots as string[],
      previewDemoVideo: template.previewDemoVideo,
      requirementsPermissions: template.requirementsPermissions as string[],
      requirementsIntegrations: template.requirementsIntegrations as string[],
      requirementsMinimumVersion: template.requirementsMinimumVersion,
      workflowDefinition: template.workflowDefinition,
      metadata: template.metadata,
      createdBy: template.createdBy,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    };
  }

  // 预置工作流定义方法
  private getAutoDeploymentWorkflow() {
    return {
      name: '自动部署流程',
      description: '从代码提交到生产部署的完整自动化流程',
      category: 'SERVICE_AUTOMATION',
      priority: 'MEDIUM',
      version: '1.0.0',
      isActive: true,
      steps: [
        {
          index: 0,
          name: '获取部署参数',
          type: 'ACTION',
          description: '获取和验证部署参数',
          config: {
            actionType: 'parameter_validation',
            parameters: {
              required_fields: ['service_name', 'docker_image', 'deployment_env']
            }
          },
          timeout: 10000,
          retry: { maxAttempts: 2, delay: 1000 }
        },
        {
          index: 1,
          name: '拉取Docker镜像',
          type: 'HTTP_REQUEST',
          description: '从镜像仓库拉取最新镜像',
          config: {
            method: 'POST',
            url: 'http://registry.internal/v2/{{service_name}}/pull',
            headers: { 'Content-Type': 'application/json' },
            body: { tag: '{{docker_image}}' },
            timeout: 300000
          },
          timeout: 300000,
          retry: { maxAttempts: 3, delay: 5000 }
        },
        {
          index: 2,
          name: '停止旧版本服务',
          type: 'SCRIPT',
          description: '优雅停止当前运行的服务',
          config: {
            language: 'bash',
            code: `kubectl scale deployment {{service_name}} --replicas=0 -n {{deployment_env}}
kubectl rollout status deployment/{{service_name}} -n {{deployment_env}} --timeout=60s`,
            timeout: 60000
          },
          timeout: 60000,
          retry: { maxAttempts: 2, delay: 5000 }
        },
        {
          index: 3,
          name: '部署新版本',
          type: 'SCRIPT',
          description: '部署新版本服务',
          config: {
            language: 'bash',
            code: `kubectl set image deployment/{{service_name}} {{service_name}}={{docker_image}} -n {{deployment_env}}
kubectl rollout status deployment/{{service_name}} -n {{deployment_env}} --timeout=300s`,
            timeout: 300000
          },
          timeout: 300000,
          retry: { maxAttempts: 3, delay: 10000 }
        },
        {
          index: 4,
          name: '健康检查',
          type: 'HTTP_REQUEST',
          description: '验证服务健康状态',
          config: {
            method: 'GET',
            url: '{{health_check_url}}/health',
            expectedStatus: 200,
            timeout: 30000,
            retryCount: 5,
            retryDelay: 10000
          },
          timeout: 60000,
          retry: { maxAttempts: 5, delay: 10000 }
        },
        {
          index: 5,
          name: '发送部署通知',
          type: 'NOTIFICATION',
          description: '发送部署成功通知',
          config: {
            type: 'email',
            recipients: ['{{notifications}}'],
            subject: '{{service_name}} 部署成功',
            content: '服务 {{service_name}} 已成功部署到 {{deployment_env}} 环境'
          },
          timeout: 30000,
          retry: { maxAttempts: 2, delay: 5000 }
        }
      ],
      variables: {
        service_name: '',
        deployment_env: 'production',
        docker_image: '',
        health_check_url: '',
        rollback_enabled: true
      },
      settings: {
        timeout: 1800000,
        maxRetries: 3,
        errorHandling: 'rollback',
        notifications: ['<EMAIL>']
      },
      tags: ['deployment', 'automation']
    };
  }

  private getSLAViolationWorkflow() {
    return {
      name: 'SLA违规响应',
      description: 'SLA指标违规时的自动响应和升级流程',
      category: 'SLA_MONITORING',
      priority: 'HIGH',
      version: '1.0.0',
      isActive: true,
      steps: [
        {
          index: 0,
          name: '检测SLA违规',
          type: 'CONDITION',
          description: '检查SLA指标是否违规',
          config: {
            expression: 'sla_current < sla_threshold',
            trueStep: 1,
            falseStep: -1
          },
          timeout: 5000,
          retry: { maxAttempts: 1, delay: 1000 }
        },
        {
          index: 1,
          name: '记录违规事件',
          type: 'DATABASE_OPERATION',
          description: '在数据库中记录SLA违规事件',
          config: {
            operation: 'INSERT',
            connection: 'main_db',
            query: 'INSERT INTO sla_violations (service_id, violation_time, severity, current_value, threshold) VALUES (?, NOW(), ?, ?, ?)',
            parameters: ['{{service_id}}', '{{severity}}', '{{sla_current}}', '{{sla_threshold}}']
          },
          timeout: 10000,
          retry: { maxAttempts: 3, delay: 2000 }
        },
        {
          index: 2,
          name: '发送告警通知',
          type: 'NOTIFICATION',
          description: '发送SLA违规告警',
          config: {
            type: 'multi_channel',
            channels: '{{notification_channels}}',
            recipients: ['<EMAIL>', '<EMAIL>'],
            subject: 'SLA违规告警 - {{service_name}}',
            content: '服务 {{service_name}} SLA违规，当前值: {{sla_current}}%，阈值: {{sla_threshold}}%'
          },
          timeout: 30000,
          retry: { maxAttempts: 3, delay: 5000 }
        },
        {
          index: 3,
          name: '启动自动恢复',
          type: 'SUBPROCESS',
          description: '启动服务自动恢复流程',
          config: {
            workflowId: 'service-recovery',
            context: {
              service_name: '{{service_name}}',
              severity: '{{severity}}'
            },
            waitForCompletion: false
          },
          timeout: 10000,
          retry: { maxAttempts: 1, delay: 0 }
        }
      ],
      variables: {
        sla_threshold: 95,
        escalation_level: 1,
        notification_channels: ['email', 'sms', 'slack']
      },
      settings: {
        timeout: 300000,
        maxRetries: 1,
        errorHandling: 'continue',
        notifications: ['<EMAIL>']
      },
      tags: ['sla', 'monitoring', 'alert']
    };
  }

  private getAlertEscalationWorkflow() {
    return {
      name: '告警升级流程',
      description: '根据告警严重程度和响应时间进行逐级升级',
      category: 'ALERT_PROCESSING',
      priority: 'HIGH',
      version: '1.0.0',
      isActive: true,
      steps: [
        {
          index: 0,
          name: '一级响应',
          type: 'NOTIFICATION',
          description: '通知一级响应团队',
          config: {
            type: 'pager',
            recipients: ['{{oncall_rotation}}.primary'],
            subject: '{{alert_severity}} 级告警',
            content: '告警详情: {{alert_details}}',
            urgency: 'high'
          },
          timeout: 30000,
          retry: { maxAttempts: 3, delay: 10000 }
        },
        {
          index: 1,
          name: '等待响应',
          type: 'DELAY',
          description: '等待一级响应确认',
          config: {
            duration: '{{escalation_delay}}',
            unit: 'seconds'
          },
          timeout: 0,
          retry: { maxAttempts: 1, delay: 0 }
        },
        {
          index: 2,
          name: '检查响应状态',
          type: 'CONDITION',
          description: '检查告警是否已被响应',
          config: {
            expression: 'alert_acknowledged == false && escalation_level < max_escalation_level',
            trueStep: 3,
            falseStep: -1
          },
          timeout: 5000,
          retry: { maxAttempts: 1, delay: 0 }
        },
        {
          index: 3,
          name: '二级响应',
          type: 'NOTIFICATION',
          description: '升级至二级响应团队',
          config: {
            type: 'pager',
            recipients: ['{{oncall_rotation}}.secondary', '<EMAIL>'],
            subject: '升级告警 - {{alert_severity}}',
            content: '告警未及时响应，已升级至二级团队',
            urgency: 'critical'
          },
          timeout: 30000,
          retry: { maxAttempts: 3, delay: 10000 }
        },
        {
          index: 4,
          name: '更新升级状态',
          type: 'DATABASE_OPERATION',
          description: '更新告警升级状态',
          config: {
            operation: 'UPDATE',
            connection: 'main_db',
            query: 'UPDATE alerts SET escalation_level = ?, escalated_at = NOW() WHERE alert_id = ?',
            parameters: [2, '{{alert_id}}']
          },
          timeout: 10000,
          retry: { maxAttempts: 3, delay: 2000 }
        }
      ],
      variables: {
        alert_severity: 'HIGH',
        escalation_delay: 900,
        max_escalation_level: 3,
        oncall_rotation: 'primary'
      },
      settings: {
        timeout: 3600000,
        maxRetries: 2,
        errorHandling: 'continue',
        notifications: ['<EMAIL>']
      },
      tags: ['alert', 'escalation', 'oncall']
    };
  }

  private getDatabaseBackupWorkflow() {
    return {
      name: '数据库定时备份',
      description: '定期执行数据库备份并验证备份完整性',
      category: 'BACKUP_AUTOMATION',
      priority: 'MEDIUM',
      version: '1.0.0',
      isActive: true,
      steps: [
        {
          index: 0,
          name: '检查备份目录',
          type: 'SCRIPT',
          description: '检查并创建备份目录',
          config: {
            language: 'bash',
            code: `mkdir -p {{backup_location}}/$(date +%Y%m%d)
if [ ! -d "{{backup_location}}" ]; then
  echo "备份目录创建失败"
  exit 1
fi`,
            timeout: 10000
          },
          timeout: 10000,
          retry: { maxAttempts: 2, delay: 1000 }
        },
        {
          index: 1,
          name: '执行数据库备份',
          type: 'SCRIPT',
          description: '执行数据库导出',
          config: {
            language: 'bash',
            code: `BACKUP_FILE="{{backup_location}}/$(date +%Y%m%d)/backup_$(date +%H%M%S).sql"
mysqldump --single-transaction --routines --triggers --all-databases > $BACKUP_FILE
if [ $? -eq 0 ]; then
  echo "备份成功: $BACKUP_FILE"
  echo "backup_file=$BACKUP_FILE" >> $GITHUB_ENV
else
  echo "备份失败"
  exit 1
fi`,
            timeout: 3600000
          },
          timeout: 3600000,
          retry: { maxAttempts: 2, delay: 30000 }
        },
        {
          index: 2,
          name: '压缩备份文件',
          type: 'SCRIPT',
          description: '压缩备份文件以节省空间',
          config: {
            language: 'bash',
            code: `if [ "{{compression_enabled}}" = "true" ]; then
  gzip $backup_file
  COMPRESSED_FILE="$backup_file.gz"
  echo "压缩完成: $COMPRESSED_FILE"
  echo "final_backup_file=$COMPRESSED_FILE" >> $GITHUB_ENV
else
  echo "final_backup_file=$backup_file" >> $GITHUB_ENV
fi`,
            timeout: 300000
          },
          timeout: 300000,
          retry: { maxAttempts: 2, delay: 10000 }
        },
        {
          index: 3,
          name: '验证备份完整性',
          type: 'SCRIPT',
          description: '验证备份文件的完整性',
          config: {
            language: 'bash',
            code: `if [ -f "$final_backup_file" ]; then
  FILE_SIZE=$(stat -f%z "$final_backup_file" 2>/dev/null || stat -c%s "$final_backup_file")
  if [ $FILE_SIZE -gt 1000 ]; then
    echo "备份文件验证成功，大小: $FILE_SIZE bytes"
  else
    echo "备份文件太小，可能有问题"
    exit 1
  fi
else
  echo "备份文件不存在"
  exit 1
fi`,
            timeout: 60000
          },
          timeout: 60000,
          retry: { maxAttempts: 1, delay: 0 }
        },
        {
          index: 4,
          name: '清理过期备份',
          type: 'SCRIPT',
          description: '清理超过保留期的备份文件',
          config: {
            language: 'bash',
            code: `find {{backup_location}} -type f -name "*.sql*" -mtime +{{retention_days}} -delete
echo "过期备份清理完成"`,
            timeout: 60000
          },
          timeout: 60000,
          retry: { maxAttempts: 2, delay: 5000 }
        },
        {
          index: 5,
          name: '发送备份报告',
          type: 'NOTIFICATION',
          description: '发送备份完成通知',
          config: {
            type: 'email',
            recipients: ['{{notifications}}'],
            subject: '数据库备份完成 - {{database_type}}',
            content: '数据库备份已完成\n备份文件: {{final_backup_file}}\n执行时间: {{execution_time}}'
          },
          timeout: 30000,
          retry: { maxAttempts: 3, delay: 5000 }
        }
      ],
      variables: {
        database_type: 'mysql',
        backup_location: '/backup/db',
        retention_days: 30,
        compression_enabled: true
      },
      settings: {
        timeout: 7200000,
        maxRetries: 2,
        errorHandling: 'stop',
        notifications: ['<EMAIL>']
      },
      tags: ['backup', 'database', 'scheduled']
    };
  }
}

export const workflowTemplateDbService = new WorkflowTemplateDbService();
export default workflowTemplateDbService;