/**
 * 工作流模板统一服务
 * 整合内存模板和数据库模板的统一管理服务
 */

import { 
  WorkflowDefinition,
  WorkflowStep,
  WorkflowStepType,
  WorkflowCategory,
  TriggerType 
} from '../types/workflow';
import { workflowTemplateDbService, DatabaseWorkflowTemplate } from './workflow-template-db.service';
import { WorkflowCategory as WorkflowCategoryEnum } from '@prisma/client';

/**
 * 工作流模板接口
 */
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  subcategory: string;
  tags: string[];
  author: string;
  version: string;
  isBuiltin: boolean;
  isPopular: boolean;
  usageCount: number;
  rating: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // 预计执行时间（分钟）
  preview: {
    thumbnail: string;
    screenshots: string[];
    demoVideo?: string;
  };
  requirements: {
    permissions: string[];
    integrations: string[];
    minimumVersion: string;
  };
  workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
  metadata: {
    createdAt: string;
    updatedAt: string;
    downloads: number;
    lastUsed?: string;
  };
}

/**
 * 模板分类配置
 */
export interface TemplateCategory {
  id: WorkflowCategory;
  name: string;
  description: string;
  icon: string;
  subcategories: Array<{
    id: string;
    name: string;
    description: string;
  }>;
}

class WorkflowTemplateUnifiedService {
  private categories: TemplateCategory[] = [];
  private dbInitialized = false;

  constructor() {
    this.initializeCategories();
    this.ensureDbInitialized();
  }

  /**
   * 确保数据库已初始化
   */
  private async ensureDbInitialized(): Promise<void> {
    if (!this.dbInitialized) {
      try {
        await workflowTemplateDbService.initializeTemplateDatabase();
        this.dbInitialized = true;
        console.log('工作流模板数据库初始化完成');
      } catch (error) {
        console.error('工作流模板数据库初始化失败:', error);
      }
    }
  }

  /**
   * 初始化模板分类
   */
  private initializeCategories(): void {
    this.categories = [
      {
        id: 'SERVICE_AUTOMATION',
        name: '服务自动化',
        description: '自动化服务管理和运维任务',
        icon: '🔧',
        subcategories: [
          { id: 'deployment', name: '部署自动化', description: '应用部署和更新流程' },
          { id: 'scaling', name: '弹性伸缩', description: '资源自动伸缩管理' },
          { id: 'restart', name: '服务重启', description: '服务异常自动恢复' },
          { id: 'health_check', name: '健康检查', description: '服务健康状态监控' }
        ]
      },
      {
        id: 'SLA_MONITORING',
        name: 'SLA监控',
        description: 'SLA指标监控和违规处理',
        icon: '📊',
        subcategories: [
          { id: 'availability', name: '可用性监控', description: '服务可用性指标跟踪' },
          { id: 'performance', name: '性能监控', description: '响应时间和吞吐量监控' },
          { id: 'violation', name: '违规处理', description: 'SLA违规自动响应流程' },
          { id: 'reporting', name: '报告生成', description: 'SLA报告自动生成' }
        ]
      },
      {
        id: 'ALERT_PROCESSING',
        name: '告警处理',
        description: '智能告警处理和响应',
        icon: '🚨',
        subcategories: [
          { id: 'escalation', name: '告警升级', description: '告警逐级升级处理' },
          { id: 'correlation', name: '告警关联', description: '相关告警自动关联' },
          { id: 'suppression', name: '告警抑制', description: '重复告警智能过滤' },
          { id: 'notification', name: '通知分发', description: '多渠道告警通知' }
        ]
      },
      {
        id: 'BACKUP_AUTOMATION',
        name: '备份自动化',
        description: '数据备份和恢复流程',
        icon: '💾',
        subcategories: [
          { id: 'database', name: '数据库备份', description: '数据库定期备份' },
          { id: 'file_backup', name: '文件备份', description: '重要文件系统备份' },
          { id: 'disaster_recovery', name: '灾难恢复', description: '灾难恢复流程' },
          { id: 'cleanup', name: '备份清理', description: '过期备份自动清理' }
        ]
      },
      {
        id: 'MAINTENANCE',
        name: '维护任务',
        description: '系统维护和优化任务',
        icon: '🔧',
        subcategories: [
          { id: 'patch_management', name: '补丁管理', description: '系统补丁自动更新' },
          { id: 'cleanup', name: '系统清理', description: '日志和临时文件清理' },
          { id: 'optimization', name: '性能优化', description: '系统性能调优' },
          { id: 'security_scan', name: '安全扫描', description: '安全漏洞扫描' }
        ]
      },
      {
        id: 'APPROVAL_PROCESS',
        name: '审批流程',
        description: '运维操作审批流程',
        icon: '✅',
        subcategories: [
          { id: 'change_request', name: '变更请求', description: '系统变更审批流程' },
          { id: 'access_request', name: '权限申请', description: '系统访问权限审批' },
          { id: 'resource_request', name: '资源申请', description: '计算资源申请审批' },
          { id: 'emergency', name: '紧急操作', description: '紧急操作快速审批' }
        ]
      },
      {
        id: 'NOTIFICATION',
        name: '通知流程',
        description: '多渠道通知和消息推送',
        icon: '📢',
        subcategories: [
          { id: 'incident', name: '事件通知', description: '故障事件通知流程' },
          { id: 'maintenance', name: '维护通知', description: '系统维护通知' },
          { id: 'report', name: '报告推送', description: '定期报告推送' },
          { id: 'status_update', name: '状态更新', description: '服务状态变更通知' }
        ]
      },
      {
        id: 'DATA_PROCESSING',
        name: '数据处理',
        description: '数据采集、处理和分析',
        icon: '📈',
        subcategories: [
          { id: 'collection', name: '数据采集', description: '系统数据自动采集' },
          { id: 'analysis', name: '数据分析', description: '运维数据分析处理' },
          { id: 'migration', name: '数据迁移', description: '数据迁移和同步' },
          { id: 'archival', name: '数据归档', description: '历史数据归档处理' }
        ]
      },
      {
        id: 'INTEGRATION',
        name: '集成流程',
        description: '第三方系统集成',
        icon: '🔗',
        subcategories: [
          { id: 'api_sync', name: 'API同步', description: '第三方API数据同步' },
          { id: 'webhook', name: 'Webhook处理', description: 'Webhook事件处理' },
          { id: 'file_transfer', name: '文件传输', description: 'FTP/SFTP文件传输' },
          { id: 'message_queue', name: '消息队列', description: '消息队列处理' }
        ]
      },
      {
        id: 'CUSTOM',
        name: '自定义模板',
        description: '用户自定义工作流模板',
        icon: '🎨',
        subcategories: [
          { id: 'user_created', name: '用户创建', description: '用户自定义模板' },
          { id: 'organization', name: '组织模板', description: '组织内部模板' },
          { id: 'imported', name: '导入模板', description: '外部导入模板' }
        ]
      }
    ];
  }

  /**
   * 获取所有模板分类
   */
  async getCategories(): Promise<TemplateCategory[]> {
    await this.ensureDbInitialized();
    return this.categories;
  }

  /**
   * 获取所有模板(带分页)
   */
  async getAllTemplates(params?: {
    category?: WorkflowCategory;
    subcategory?: string;
    search?: string;
    difficulty?: string;
    popular?: boolean;
    limit?: number;
    offset?: number;
    sort?: 'rating' | 'usage' | 'recent' | 'name';
    order?: 'asc' | 'desc';
  }): Promise<{
    templates: WorkflowTemplate[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      pages: number;
    };
  }> {
    await this.ensureDbInitialized();
    
    let dbTemplates = await workflowTemplateDbService.getAllTemplates();
    
    // 应用筛选条件
    if (params?.category) {
      dbTemplates = dbTemplates.filter(t => t.category === params.category);
    }
    if (params?.subcategory) {
      dbTemplates = dbTemplates.filter(t => t.subcategory === params.subcategory);
    }
    if (params?.search) {
      const query = params.search.toLowerCase();
      dbTemplates = dbTemplates.filter(t => 
        t.name.toLowerCase().includes(query) ||
        (t.description && t.description.toLowerCase().includes(query)) ||
        t.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    if (params?.difficulty) {
      dbTemplates = dbTemplates.filter(t => t.difficulty === params.difficulty);
    }
    if (params?.popular) {
      dbTemplates = dbTemplates.filter(t => t.isPopular);
    }
    
    // 排序
    if (params?.sort) {
      const order = params.order || 'desc';
      dbTemplates.sort((a, b) => {
        let aVal: any, bVal: any;
        switch (params.sort) {
          case 'rating':
            aVal = a.rating;
            bVal = b.rating;
            break;
          case 'usage':
            aVal = a.usageCount;
            bVal = b.usageCount;
            break;
          case 'recent':
            aVal = new Date(a.updatedAt).getTime();
            bVal = new Date(b.updatedAt).getTime();
            break;
          case 'name':
            aVal = a.name;
            bVal = b.name;
            break;
          default:
            aVal = a.rating;
            bVal = b.rating;
        }
        
        if (order === 'asc') {
          return aVal > bVal ? 1 : -1;
        } else {
          return aVal < bVal ? 1 : -1;
        }
      });
    }
    
    const total = dbTemplates.length;
    const limit = params?.limit || 20;
    const offset = params?.offset || 0;
    const pages = Math.ceil(total / limit);
    
    const paginatedTemplates = dbTemplates.slice(offset, offset + limit);
    const templates = this.transformDbTemplates(paginatedTemplates);
    
    return {
      templates,
      pagination: {
        total,
        limit,
        offset,
        pages
      }
    };
  }

  /**
   * 获取指定分类的模板
   */
  async getTemplatesByCategory(category: WorkflowCategory): Promise<WorkflowTemplate[]> {
    await this.ensureDbInitialized();
    
    // 从数据库获取模板
    const dbTemplates = await workflowTemplateDbService.getTemplatesByCategory(category as WorkflowCategoryEnum);
    return this.transformDbTemplates(dbTemplates);
  }

  /**
   * 获取指定子分类的模板
   */
  async getTemplatesBySubcategory(category: WorkflowCategory, subcategory: string): Promise<WorkflowTemplate[]> {
    await this.ensureDbInitialized();
    
    const dbTemplates = await workflowTemplateDbService.getAllTemplates();
    const filtered = dbTemplates.filter(t => 
      t.category === category && t.subcategory === subcategory
    );
    return this.transformDbTemplates(filtered).sort((a, b) => b.rating - a.rating);
  }

  /**
   * 搜索模板
   */
  async searchTemplates(query: string, filters?: {
    category?: WorkflowCategory;
    difficulty?: string;
    tags?: string[];
    isPopular?: boolean;
  }): Promise<WorkflowTemplate[]> {
    await this.ensureDbInitialized();

    const dbTemplates = await workflowTemplateDbService.searchTemplates(query);
    let results = this.transformDbTemplates(dbTemplates);

    // 应用过滤器
    if (filters) {
      if (filters.category) {
        results = results.filter(template => template.category === filters.category);
      }
      if (filters.difficulty) {
        results = results.filter(template => template.difficulty === filters.difficulty);
      }
      if (filters.tags && filters.tags.length > 0) {
        results = results.filter(template => 
          filters.tags!.some(tag => template.tags.includes(tag))
        );
      }
      if (filters.isPopular) {
        results = results.filter(template => template.isPopular);
      }
    }

    return results.sort((a, b) => b.rating - a.rating);
  }

  /**
   * 获取模板详情
   */
  async getTemplate(templateId: string): Promise<WorkflowTemplate | undefined> {
    await this.ensureDbInitialized();
    
    const dbTemplates = await workflowTemplateDbService.getAllTemplates();
    const template = dbTemplates.find(t => t.id === templateId);
    
    if (template) {
      return this.transformDbTemplate(template);
    }
    
    return undefined;
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(limit = 10): Promise<WorkflowTemplate[]> {
    await this.ensureDbInitialized();
    
    const dbTemplates = await workflowTemplateDbService.getPopularTemplates(limit);
    return this.transformDbTemplates(dbTemplates);
  }

  /**
   * 获取最新模板
   */
  async getRecentTemplates(limit = 10): Promise<WorkflowTemplate[]> {
    await this.ensureDbInitialized();
    
    const dbTemplates = await workflowTemplateDbService.getAllTemplates();
    const sorted = dbTemplates.sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    ).slice(0, limit);
    
    return this.transformDbTemplates(sorted);
  }

  /**
   * 应用模板创建工作流
   */
  async applyTemplate(templateId: string, userId: string, customizations?: {
    name?: string;
    description?: string;
    variables?: Record<string, any>;
    settings?: Partial<WorkflowDefinition['settings']>;
  }): Promise<Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>> {
    const template = await this.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template not found: ${templateId}`);
    }

    // 增加使用计数
    await this.incrementUsageCount(templateId, userId);

    // 应用自定义配置
    const workflowDef = { ...template.workflow };
    
    if (customizations) {
      if (customizations.name) {
        workflowDef.name = customizations.name;
      }
      if (customizations.description) {
        workflowDef.description = customizations.description;
      }
      if (customizations.variables) {
        workflowDef.variables = { ...workflowDef.variables, ...customizations.variables };
      }
      if (customizations.settings) {
        workflowDef.settings = { ...workflowDef.settings, ...customizations.settings };
      }
    }

    return workflowDef;
  }

  /**
   * 增加模板使用计数
   */
  async incrementUsageCount(templateId: string, userId: string): Promise<void> {
    await this.ensureDbInitialized();
    
    await workflowTemplateDbService.logTemplateUsage(
      templateId, 
      userId, 
      'apply'
    );
  }

  /**
   * 更新模板评分
   */
  async updateTemplateRating(templateId: string, userId: string, rating: number, comment?: string): Promise<void> {
    await this.ensureDbInitialized();
    
    if (rating >= 1 && rating <= 5) {
      await workflowTemplateDbService.updateTemplateRating(templateId, userId, rating, comment);
    } else {
      throw new Error('Rating must be between 1 and 5');
    }
  }

  /**
   * 创建自定义模板
   */
  async createCustomTemplate(data: {
    name: string;
    description: string;
    category: WorkflowCategory;
    subcategory?: string;
    tags?: string[];
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    estimatedDuration?: number;
    workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
  }, userId: string): Promise<string> {
    await this.ensureDbInitialized();
    
    const dbTemplate: Omit<DatabaseWorkflowTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
      name: data.name,
      description: data.description,
      category: data.category as WorkflowCategoryEnum,
      subcategory: data.subcategory || 'user_created',
      tags: data.tags || [],
      author: userId,
      version: '1.0.0',
      isBuiltin: false,
      isPopular: false,
      isActive: true,
      usageCount: 0,
      rating: 0,
      difficulty: data.difficulty as any || 'beginner',
      estimatedDuration: data.estimatedDuration || 5,
      previewThumbnail: undefined,
      previewScreenshots: [],
      previewDemoVideo: undefined,
      requirementsPermissions: [],
      requirementsIntegrations: [],
      requirementsMinimumVersion: '1.0.0',
      workflowDefinition: data.workflow,
      metadata: {
        stepsCount: data.workflow.steps?.length || 0,
        hasVariables: Object.keys(data.workflow.variables || {}).length > 0
      },
      createdBy: userId
    };
    
    return await workflowTemplateDbService.createTemplate(dbTemplate);
  }

  /**
   * 导出模板
   */
  async exportTemplate(templateId: string): Promise<WorkflowTemplate | undefined> {
    return await this.getTemplate(templateId);
  }

  /**
   * 导入模板
   */
  async importTemplate(templateData: WorkflowTemplate, userId: string): Promise<string> {
    await this.ensureDbInitialized();
    
    const dbTemplate = this.transformToDbFormat(templateData, userId);
    const templateId = await workflowTemplateDbService.createTemplate(dbTemplate);
    
    return templateId;
  }

  /**
   * 获取模板统计信息
   */
  async getTemplateStats(): Promise<any> {
    await this.ensureDbInitialized();
    return await workflowTemplateDbService.getTemplateStats();
  }

  /**
   * 转换数据库模板为接口模板
   */
  private transformDbTemplate(dbTemplate: DatabaseWorkflowTemplate): WorkflowTemplate {
    return {
      id: dbTemplate.id,
      name: dbTemplate.name,
      description: dbTemplate.description || '',
      category: dbTemplate.category as WorkflowCategory,
      subcategory: dbTemplate.subcategory,
      tags: dbTemplate.tags,
      author: dbTemplate.author,
      version: dbTemplate.version,
      isBuiltin: dbTemplate.isBuiltin,
      isPopular: dbTemplate.isPopular,
      usageCount: dbTemplate.usageCount,
      rating: dbTemplate.rating,
      difficulty: dbTemplate.difficulty as 'beginner' | 'intermediate' | 'advanced',
      estimatedDuration: dbTemplate.estimatedDuration,
      preview: {
        thumbnail: dbTemplate.previewThumbnail || '',
        screenshots: dbTemplate.previewScreenshots,
        demoVideo: dbTemplate.previewDemoVideo
      },
      requirements: {
        permissions: dbTemplate.requirementsPermissions,
        integrations: dbTemplate.requirementsIntegrations,
        minimumVersion: dbTemplate.requirementsMinimumVersion
      },
      workflow: dbTemplate.workflowDefinition as any,
      metadata: {
        createdAt: dbTemplate.createdAt.toISOString(),
        updatedAt: dbTemplate.updatedAt.toISOString(),
        downloads: (dbTemplate.metadata as any)?.downloads || 0,
        lastUsed: undefined
      }
    };
  }

  /**
   * 批量转换数据库模板
   */
  private transformDbTemplates(dbTemplates: DatabaseWorkflowTemplate[]): WorkflowTemplate[] {
    return dbTemplates.map(t => this.transformDbTemplate(t));
  }

  /**
   * 转换接口模板为数据库模板格式
   */
  private transformToDbFormat(
    template: WorkflowTemplate, 
    userId: string
  ): Omit<DatabaseWorkflowTemplate, 'id' | 'createdAt' | 'updatedAt'> {
    return {
      name: template.name,
      description: template.description,
      category: template.category as WorkflowCategoryEnum,
      subcategory: template.subcategory,
      tags: template.tags,
      author: template.author,
      version: template.version,
      isBuiltin: false,
      isPopular: template.isPopular,
      isActive: true,
      usageCount: 0,
      rating: 0,
      difficulty: template.difficulty as any,
      estimatedDuration: template.estimatedDuration,
      previewThumbnail: template.preview.thumbnail,
      previewScreenshots: template.preview.screenshots,
      previewDemoVideo: template.preview.demoVideo,
      requirementsPermissions: template.requirements.permissions,
      requirementsIntegrations: template.requirements.integrations,
      requirementsMinimumVersion: template.requirements.minimumVersion,
      workflowDefinition: template.workflow,
      metadata: template.metadata,
      createdBy: userId
    };
  }
}

export const workflowTemplateUnifiedService = new WorkflowTemplateUnifiedService();
export default workflowTemplateUnifiedService;