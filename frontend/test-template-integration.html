<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流模板集成测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e1e1;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.warning {
            background: #ffc107;
            color: #212529;
        }
        .button.danger {
            background: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .json-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 工作流模板集成测试</h1>
        
        <div class="info">
            <strong>测试说明：</strong>验证从工作流模板库创建工作流功能是否正常工作
        </div>

        <div class="test-section">
            <h2>📋 测试步骤</h2>
            <div class="steps">
                <ol>
                    <li>访问工作流模板库页面</li>
                    <li>选择一个模板并点击"应用"按钮</li>
                    <li>系统应该自动跳转到工作流设计器并预填充模板内容</li>
                    <li>检查URL是否包含正确的templateId和mode参数</li>
                    <li>验证工作流设计器是否正确加载了模板数据</li>
                </ol>
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 测试链接</h2>
            
            <h3>1. 访问模板库</h3>
            <a href="http://localhost:3001/admin/workflow-templates" class="test-link" target="_blank">
                打开工作流模板库
            </a>
            
            <h3>2. 模拟模板应用（直接URL）</h3>
            <a href="http://localhost:3001/admin/workflow-designer?templateId=template_daily_report&mode=template" class="test-link" target="_blank">
                从日报模板创建工作流
            </a>
            
            <a href="http://localhost:3001/admin/workflow-designer?templateId=template_alert_handling&mode=template" class="test-link" target="_blank">
                从告警处理模板创建工作流
            </a>
            
            <a href="http://localhost:3001/admin/workflow-designer?templateId=template_backup_verification&mode=template" class="test-link" target="_blank">
                从备份验证模板创建工作流
            </a>
            
            <h3>3. 普通工作流设计器</h3>
            <a href="http://localhost:3001/admin/workflow-designer" class="test-link" target="_blank">
                创建新的工作流
            </a>
        </div>

        <div class="test-section">
            <h2>✅ 预期结果</h2>
            <ul>
                <li>模板库页面应该能正常显示模板列表</li>
                <li>点击模板的"应用"按钮后，应该新窗口打开工作流设计器</li>
                <li>设计器URL应该包含 <code>?templateId=xxx&mode=template</code></li>
                <li>设计器应该显示"基于模板: [模板名称]"作为工作流名称</li>
                <li>画布应该预填充模板中定义的节点和连接</li>
                <li>工作流应该标记为已修改状态（需要保存）</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🐛 可能的问题</h2>
            <ul>
                <li><strong>404错误</strong>: 路由配置问题</li>
                <li><strong>空白页面</strong>: JavaScript错误或组件加载失败</li>
                <li><strong>模板数据未加载</strong>: API调用失败或数据格式问题</li>
                <li><strong>工作流不显示</strong>: 节点转换或画布渲染问题</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🔍 调试信息</h2>
            <p>如果遇到问题，请检查：</p>
            <ul>
                <li>浏览器开发者工具的Console标签（查看JavaScript错误）</li>
                <li>Network标签（查看API请求状态）</li>
                <li>后端服务器日志（查看API处理情况）</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟参数替换函数（和前端实际实现一致）
        function replaceParametersInObject(obj, parameters) {
            if (typeof obj === 'string') {
                return obj.replace(/\{\{(\w+)\}\}/g, (match, paramName) => {
                    return parameters[paramName] !== undefined ? parameters[paramName] : match;
                });
            } else if (Array.isArray(obj)) {
                return obj.map(item => replaceParametersInObject(item, parameters));
            } else if (obj && typeof obj === 'object') {
                const result = {};
                for (const [key, value] of Object.entries(obj)) {
                    result[key] = replaceParametersInObject(value, parameters);
                }
                return result;
            }
            return obj;
        }

        // 模拟模板数据
        const mockTemplate = {
            id: 'template-001',
            name: '服务器健康检查',
            description: '定期检查服务器状态并发送告警',
            workflow: {
                steps: [
                    {
                        id: 'step1',
                        name: '检查服务器状态',
                        type: 'HTTP_REQUEST',
                        config: {
                            url: '{{serverUrl}}/health',
                            method: 'GET',
                            timeout: '{{timeout}}'
                        }
                    },
                    {
                        id: 'step2',
                        name: '发送通知',
                        type: 'NOTIFICATION',
                        config: {
                            message: '服务器 {{serverName}} 状态检查完成',
                            recipients: ['{{adminEmail}}']
                        }
                    }
                ],
                variables: {
                    serverUrl: 'https://api.example.com',
                    serverName: '默认服务器',
                    timeout: 5000,
                    adminEmail: '<EMAIL>'
                }
            }
        };

        // 模拟用户配置参数
        const mockUserConfig = {
            workflowName: '生产环境服务器监控',
            workflowDescription: '监控生产环境服务器健康状态',
            variables: {
                serverUrl: 'https://prod-api.mycompany.com',
                serverName: '生产服务器',
                timeout: 10000,
                adminEmail: '<EMAIL>'
            }
        };

        // 测试参数替换功能
        function testParameterReplacement() {
            console.log('🧪 开始测试参数替换功能...');
            
            try {
                const processedSteps = replaceParametersInObject(mockTemplate.workflow.steps, mockUserConfig.variables);
                
                console.log('✅ 参数替换测试结果:');
                console.log('原始步骤:', mockTemplate.workflow.steps);
                console.log('处理后步骤:', processedSteps);
                
                // 验证替换结果
                const step1Config = processedSteps[0].config;
                const step2Config = processedSteps[1].config;
                
                console.assert(
                    step1Config.url === 'https://prod-api.mycompany.com/health',
                    '❌ URL参数替换失败'
                );
                console.assert(
                    step1Config.timeout === 10000,
                    '❌ 超时参数替换失败'
                );
                console.assert(
                    step2Config.message === '服务器 生产服务器 状态检查完成',
                    '❌ 消息参数替换失败'
                );
                console.assert(
                    JSON.stringify(step2Config.recipients) === JSON.stringify(['<EMAIL>']),
                    '❌ 邮箱参数替换失败'
                );
                
                console.log('✅ 所有参数替换测试通过!');
                return true;
                
            } catch (error) {
                console.error('❌ 参数替换测试失败:', error);
                return false;
            }
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 工作流模板集成测试页面已加载');
            console.log('📍 当前URL:', window.location.href);
            
            // 检查是否在正确的环境中
            if (window.location.hostname !== 'localhost') {
                console.warn('⚠️  不在本地开发环境中，请确保后端服务已启动');
            }
            
            // 自动运行参数替换测试
            testParameterReplacement();
            
            // 输出测试数据供调试使用
            console.log('🧪 测试数据:', {
                mockTemplate,
                mockUserConfig
            });
        });

        // 检查链接可用性
        function checkLinksAvailability() {
            const links = document.querySelectorAll('.test-link');
            const checkPromises = [];
            
            links.forEach(link => {
                const url = link.href;
                const checkPromise = fetch(url, { method: 'HEAD', mode: 'no-cors' })
                    .then(() => {
                        console.log('✅ 链接可用:', url);
                        link.style.borderLeft = '4px solid #28a745';
                    })
                    .catch(() => {
                        console.log('❌ 链接不可用:', url);
                        link.style.borderLeft = '4px solid #dc3545';
                    });
                
                checkPromises.push(checkPromise);
            });
            
            return Promise.all(checkPromises);
        }

        // 延迟检查链接（给服务器一些启动时间）
        setTimeout(() => {
            console.log('🔗 检查测试链接可用性...');
            checkLinksAvailability();
        }, 2000);
    </script>
</body>
</html>