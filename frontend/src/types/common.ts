/**
 * 前端通用类型定义
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
  timestamp?: string;
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  total?: number;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 排序参数类型
export interface SortParams {
  field: string;
  order: 'asc' | 'desc';
}

// 过滤参数类型
export interface FilterParams {
  [key: string]: any;
}

// 列表查询参数类型
export interface ListParams extends PaginationParams {
  search?: string;
  filters?: FilterParams;
  sort?: SortParams;
}

// 选项类型
export interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
  children?: Option[];
}

// 状态类型
export type Status = 'active' | 'inactive' | 'pending' | 'error';

// 优先级类型
export type Priority = 'low' | 'medium' | 'high' | 'urgent';

// 表单验证规则类型
export interface ValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (value: any) => boolean | string;
}

// 操作结果类型
export interface OperationResult {
  success: boolean;
  message?: string;
  data?: any;
}

// 错误信息类型
export interface ErrorInfo {
  code: string;
  message: string;
  details?: any;
}

// 文件信息类型
export interface FileInfo {
  id?: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  uploadTime?: string;
  status?: 'uploading' | 'success' | 'error';
}

// 时间范围类型
export interface TimeRange {
  startTime: string;
  endTime: string;
}

// 统计数据类型
export interface StatData {
  label: string;
  value: number;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
}

// 导出数据类型
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf';
  fields?: string[];
  filename?: string;
}

// 用户基础信息类型
export interface UserBasic {
  id: string;
  username: string;
  fullName: string;
  avatar?: string;
}

// 操作日志类型
export interface OperationLog {
  id: string;
  operation: string;
  target: string;
  targetId: string;
  userId: string;
  username: string;
  timestamp: string;
  details?: any;
  ip?: string;
  userAgent?: string;
}