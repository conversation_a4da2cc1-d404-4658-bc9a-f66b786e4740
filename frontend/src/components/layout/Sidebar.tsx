import React from "react";
import { useNavigate } from "react-router-dom";
import { Nav, Space, Divider } from "@douyinfe/semi-ui";
import {
  IconHome,
  IconUser,
  IconFolder,
  IconTicketCodeStroked,
  IconSetting,
  IconUserGroup,
  IconKey,
  IconArticle,
  IconSemiLogo,
  IconShield,
  IconActivity,
  IconEdit,
  IconClock,
  IconCode,
} from "@douyinfe/semi-icons";
import { useAuthStore, useUIStore } from "@/stores";
import { UserPanel } from "./UserPanel";

interface NavItem {
  itemKey: string;
  text: string;
  icon: React.ReactNode;
  path?: string;
  items?: NavItem[];
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
}

const navItems: NavItem[] = [
  {
    itemKey: "dashboard",
    text: "看板",
    icon: <IconHome size="large" />,
    path: "/",
  },
  {
    itemKey: "customers",
    text: "客户管理",
    icon: <IconUserGroup size="large" />,
    path: "/customers",
    permissions: "customer:read",
  },
  {
    itemKey: "services",
    text: "服务工单",
    icon: <IconTicketCodeStroked size="large" />,
    path: "/services",
    permissions: "service:read",
  },
  {
    itemKey: "admin",
    text: "系统管理",
    icon: <IconSetting size="large" />,
    permissions: ["user:read", "role:read", "audit:read", "admin:all"],
    items: [
      {
        itemKey: "admin-dashboard",
        text: "管理概览",
        icon: <IconSemiLogo size="large" />,
        path: "/admin/dashboard",
        permissions: ["admin:all"],
      },
      {
        itemKey: "admin-users",
        text: "用户管理",
        icon: <IconUser size="large" />,
        path: "/admin/users",
        permissions: ["user:read", "admin:all"],
      },
      {
        itemKey: "admin-roles",
        text: "角色管理",
        icon: <IconKey size="large" />,
        path: "/admin/roles",
        permissions: ["role:read", "admin:all"],
      },
      {
        itemKey: "admin-permissions",
        text: "权限模板",
        icon: <IconShield size="large" />,
        path: "/admin/permission-templates",
        permissions: ["role:read", "admin:all"],
      },
      {
        itemKey: "admin-monitor",
        text: "系统监控",
        icon: <IconActivity size="large" />,
        path: "/admin/monitor",
        permissions: ["admin:all"],
      },
      {
        itemKey: "admin-tasks",
        text: "任务监控",
        icon: <IconClock size="large" />,
        path: "/admin/tasks",
        permissions: ["admin:all"],
      },
       {
          itemKey: "admin-security",
          text: "安全监控",
          icon: <IconShield size="large" />,
          path: "/admin/security",
          permissions: ["security:read", "admin:all"],
        },
        {
          itemKey: "admin-schedule",
          text: "任务计划",
          icon: <IconClock size="large" />,
          path: "/admin/schedule",
          permissions: ["schedule:read", "admin:all"],
        },
      {
        itemKey: "admin-ai",
        text: "AI集成管理",
        icon: <IconCode size="large" />,
        path: "/admin/ai-management",
        permissions: ["admin:all"],
      },
      {
          itemKey: "admin-monitoring",
          text: "监控管理",
          icon: <IconCode size="large" />,
          path: "/admin/enhanced-monitor",
          permissions: ["monitoring:read", "admin:all"],
        },
        {
          itemKey: "admin-reports",
          text: "报告分析",
          icon: <IconActivity size="large" />,
          path: "/admin/reports",
          permissions: ["report:read", "admin:all"],
        },
        {
          itemKey: "admin-workflow-templates",
          text: "工作流模板",
          icon: <IconFolder size="large" />,
          path: "/admin/workflow-templates",
          permissions: ["workflow:read", "admin:all"],
        },
         {
          itemKey: "admin-workflow-designer",
          text: "工作流配置",
          icon: <IconActivity size="large" />,
          path: "/admin/workflow-designer",
          permissions: ["admin:all"],
        },
        {
          itemKey: "admin-integration-manage",
          text: "集成管理",
          icon: <IconCode size="large" />,
          path: "/admin/integration-manage",
          permissions: ["admin:all"],
        },
      {
        itemKey: "admin-system-config",
        text: "系统配置",
        icon: <IconEdit size="large" />,
        path: "/admin/system-config",
        permissions: ["system:read", "admin:all"],
      },
      {
        itemKey: "admin-audit",
        text: "审计日志",
        icon: <IconArticle size="large" />,
        path: "/admin/audit-logs",
        permissions: ["audit:read", "admin:all"],
      },
    ],
  },
];

export function Sidebar() {
  const { hasAnyPermission, hasAnyRole } = useAuthStore();
  const navigate = useNavigate();
  const [selectedKeys, setSelectedKeys] = React.useState(["dashboard"]);

  // 检查导航项权限
  const hasNavPermission = (item: NavItem): boolean => {
    if (!item.permissions && !item.roles) {
      return true;
    }

    if (item.permissions) {
      const permissions = Array.isArray(item.permissions) ? item.permissions : [item.permissions];
      if (!hasAnyPermission(permissions)) {
        return false;
      }
    }

    if (item.roles) {
      const roles = Array.isArray(item.roles) ? item.roles : [item.roles];
      if (!hasAnyRole(roles)) {
        return false;
      }
    }

    return true;
  };

  // 过滤导航项
  const filterNavItems = (items: NavItem[]): NavItem[] => {
    return items.filter(item => {
      if (!hasNavPermission(item)) {
        return false;
      }

      if (item.items) {
        const filteredItems = filterNavItems(item.items);
        if (filteredItems.length === 0) {
          return false;
        }
        item.items = filteredItems;
      }

      return true;
    });
  };

  // 获取过滤后的导航项
  const filteredNavItems = React.useMemo(() => {
    return filterNavItems([...navItems]);
  }, [hasAnyPermission, hasAnyRole]);

  // 获取当前路径对应的选中项
  React.useEffect(() => {
    const path = window.location.pathname;
    const findSelectedKey = (items: NavItem[]): string | null => {
      for (const item of items) {
        if (item.path === path) {
          return item.itemKey;
        }
        if (item.items) {
          const childKey = findSelectedKey(item.items);
          if (childKey) return childKey;
        }
      }
      return null;
    };

    const key = findSelectedKey(filteredNavItems);
    if (key) {
      setSelectedKeys([key]);
    }
  }, [filteredNavItems]);

  const handleNavClick = (data: any) => {
    const item = findNavItem(filteredNavItems, data.itemKey);
    if (item?.path) {
      navigate(item.path);
      setSelectedKeys([data.itemKey]);
    }
  };

  const findNavItem = (items: NavItem[], key: string): NavItem | null => {
    for (const item of items) {
      if (item.itemKey === key) return item;
      if (item.items) {
        const found = findNavItem(item.items, key);
        if (found) return found;
      }
    }
    return null;
  };

  return (
    <div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <Nav
        defaultSelectedKeys={["dashboard"]}
        style={{ maxWidth: 220, height: "100%" }}
        items={filteredNavItems}
        selectedKeys={selectedKeys}
        onClick={handleNavClick}
        header={{
          logo: <IconSemiLogo style={{ fontSize: 36 }} />,
          text: "运维管理系统",
        }}
        footer={<UserPanel />}
      />
    </div>
  );
}
