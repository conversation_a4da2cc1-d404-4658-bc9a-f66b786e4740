import React, { useState } from 'react';
import { Form, Input, Select, Switch, Button, Space, Tabs, TabPane, TagInput, Radio, RadioGroup, TextArea } from '@douyinfe/semi-ui';
import { IconMail, IconPhone, IconBell, IconInfoCircle, IconPlus, IconDelete } from '@douyinfe/semi-icons';
import BaseStepForm from './BaseStepForm';
import type { WorkflowStep, StepFormProps } from '../../../types/workflow';
const { Option } = Select;

/**
 * 通知步骤配置表单
 */
const NotificationStepForm: React.FC<StepFormProps> = ({
  step,
  onChange,
  onValidation,
  availableVariables = []
}) => {
  const [formApi, setFormApi] = useState<any>(null);
  const [customVariables, setCustomVariables] = useState<Array<{ key: string; value: string }>>([]);

  // 通知模板
  const notificationTemplates = {
    email: {
      service_alert: {
        name: '服务告警通知',
        subject: '【告警】${service_name} 服务异常',
        content: `服务异常通知：

服务名称：\${service_name}
异常类型：\${alert_type}
发生时间：\${alert_time}
异常描述：\${alert_message}

请及时处理！`
      },
      task_complete: {
        name: '任务完成通知',
        subject: '【完成】${task_name} 任务已完成',
        content: `任务完成通知：

任务名称：\${task_name}
完成时间：\${complete_time}
执行结果：\${task_result}
耗时：\${duration}

任务已成功完成。`
      },
      maintenance_reminder: {
        name: '维护提醒',
        subject: '【提醒】${system_name} 计划维护通知',
        content: `维护提醒：

系统名称：\${system_name}
维护时间：\${maintenance_time}
预计耗时：\${estimated_duration}
维护内容：\${maintenance_content}

请提前做好相关准备工作。`
      }
    },
    sms: {
      service_alert: {
        name: '服务告警短信',
        content: '【告警】${service_name}服务异常，${alert_type}，请及时处理！'
      },
      task_complete: {
        name: '任务完成短信',
        content: '【完成】${task_name}任务已完成，结果：${task_result}'
      }
    }
  };

  // 处理配置变化
  const handleConfigChange = (values: any) => {
    // 合并自定义变量
    const variablesObject = customVariables.reduce((obj, { key, value }) => {
      if (key.trim()) {
        obj[key.trim()] = value;
      }
      return obj;
    }, {} as Record<string, string>);

    const updatedStep: WorkflowStep = {
      ...step,
      config: {
        ...step.config,
        ...values,
        variables: {
          ...values.variables,
          ...variablesObject
        }
      }
    };

    // 验证配置
    const errors: string[] = [];
    
    if (!values.notificationType) {
      errors.push('请选择通知类型');
    }

    if (values.notificationType === 'email') {
      if (!values.recipients?.length) {
        errors.push('请输入邮件接收者');
      }
      if (!values.subject?.trim()) {
        errors.push('请输入邮件主题');
      }
      if (!values.content?.trim()) {
        errors.push('请输入邮件内容');
      }
    }

    if (values.notificationType === 'sms') {
      if (!values.phoneNumbers?.length) {
        errors.push('请输入手机号码');
      }
      if (!values.content?.trim()) {
        errors.push('请输入短信内容');
      }
    }

    onValidation(errors.length === 0, errors);
    onChange(updatedStep);
  };

  // 应用模板
  const applyTemplate = (type: 'email' | 'sms', templateKey: string) => {
    const template = notificationTemplates[type][templateKey as keyof typeof notificationTemplates[typeof type]];
    if (template && formApi) {
      if (type === 'email') {
        const emailTemplate = template as { name: string; subject: string; content: string };
        formApi.setValue('subject', emailTemplate.subject);
        formApi.setValue('content', emailTemplate.content);
      } else {
        const smsTemplate = template as { name: string; content: string };
        formApi.setValue('content', smsTemplate.content);
      }
      handleConfigChange(formApi.getValues());
    }
  };

  // 添加自定义变量
  const addCustomVariable = () => {
    setCustomVariables([...customVariables, { key: '', value: '' }]);
  };

  // 删除自定义变量
  const removeCustomVariable = (index: number) => {
    const newVariables = customVariables.filter((_, i) => i !== index);
    setCustomVariables(newVariables);
    if (formApi) {
      handleConfigChange(formApi.getValues());
    }
  };

  // 更新自定义变量
  const updateCustomVariable = (index: number, field: 'key' | 'value', value: string) => {
    const newVariables = [...customVariables];
    newVariables[index][field] = value;
    setCustomVariables(newVariables);
    if (formApi) {
      handleConfigChange(formApi.getValues());
    }
  };

  return (
    <div className="notification-step-form">
      {/* 基础配置 */}
      <BaseStepForm
        step={step}
        onChange={onChange}
        onValidation={onValidation}
        availableVariables={availableVariables}
      />

      {/* 通知特定配置 */}
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          <IconBell style={{ marginRight: '8px' }} />
          通知配置
        </h4>

        <Form
          getFormApi={setFormApi}
          initValues={{
            notificationType: step.config.notificationType || 'email',
            recipients: step.config.recipients || [],
            phoneNumbers: step.config.phoneNumbers || [],
            subject: step.config.subject || '',
            content: step.config.content || '',
            priority: step.config.priority || 'normal',
            template: step.config.template || '',
            variables: step.config.variables || {},
            attachFiles: step.config.attachFiles || false,
            scheduledSend: step.config.scheduledSend || false,
            retryOnFailure: step.config.retryOnFailure || true
          }}
          onValueChange={handleConfigChange}
          labelPosition="top"
        >
          {/* 通知类型选择 */}
          <Form.RadioGroup
            field="notificationType"
            label="通知类型"
            direction="horizontal"
          >
            <Radio value="email">
              <IconMail style={{ marginRight: '4px' }} />
              邮件通知
            </Radio>
            <Radio value="sms">
              <IconPhone style={{ marginRight: '4px' }} />
              短信通知
            </Radio>
            <Radio value="webhook">
              <IconBell style={{ marginRight: '4px' }} />
              Webhook通知
            </Radio>
          </Form.RadioGroup>

          <Tabs type="card" style={{ marginTop: '16px' }}>
            {/* 接收者配置 */}
            <TabPane tab="接收者" itemKey="recipients">
              <div style={{ padding: '16px 0' }}>
                {formApi?.getValue('notificationType') === 'email' && (
                  <Form.TagInput
                    field="recipients"
                    label="邮件接收者"
                    placeholder="输入邮箱地址，按回车添加"
                    rules={[{ required: true, message: '请输入邮件接收者' }]}
                  />
                )}

                {formApi?.getValue('notificationType') === 'sms' && (
                  <Form.TagInput
                    field="phoneNumbers"
                    label="手机号码"
                    placeholder="输入手机号码，按回车添加"
                    rules={[{ required: true, message: '请输入手机号码' }]}
                  />
                )}

                {formApi?.getValue('notificationType') === 'webhook' && (
                  <Form.Input
                    field="webhookUrl"
                    label="Webhook URL"
                    placeholder="https://api.example.com/webhook"
                    rules={[{ required: true, message: '请输入Webhook URL' }]}
                  />
                )}
              </div>
            </TabPane>

            {/* 内容配置 */}
            <TabPane tab="内容" itemKey="content">
              <div style={{ padding: '16px 0' }}>
                {/* 模板选择 */}
                <div style={{ marginBottom: '16px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                    选择预设模板:
                  </label>
                  <Space wrap>
                    {Object.entries(notificationTemplates[formApi?.getValue('notificationType') === 'sms' ? 'sms' : 'email']).map(([key, template]) => (
                      <Button
                        key={key}
                        size="small"
                        onClick={() => applyTemplate(formApi?.getValue('notificationType') === 'sms' ? 'sms' : 'email', key)}
                        style={{ marginBottom: '4px' }}
                      >
                        {template.name}
                      </Button>
                    ))}
                  </Space>
                </div>

                {/* 邮件主题 */}
                {formApi?.getValue('notificationType') === 'email' && (
                  <Form.Input
                    field="subject"
                    label="邮件主题"
                    placeholder="输入邮件主题，支持变量 ${variable_name}"
                    rules={[{ required: true, message: '请输入邮件主题' }]}
                  />
                )}

                {/* 通知内容 */}
                <Form.TextArea
                  field="content"
                  label={formApi?.getValue('notificationType') === 'email' ? '邮件内容' : 
                        formApi?.getValue('notificationType') === 'sms' ? '短信内容' : '消息内容'}
                  placeholder="输入通知内容，支持变量替换 ${variable_name}"
                  autosize={{ minRows: 6, maxRows: 12 }}
                  rules={[{ required: true, message: '请输入通知内容' }]}
                />

                <div style={{ marginTop: '12px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                    <IconInfoCircle style={{ marginRight: '4px' }} />
                    变量替换说明:
                  </label>
                  <div style={{ fontSize: '11px', color: '#999', lineHeight: '1.4' }}>
                    • 使用 <code>${'{variable_name}'}</code> 格式插入变量<br/>
                    • 支持系统变量: <code>${'{current_time}'}</code>, <code>${'{workflow_name}'}</code><br/>
                    • 支持上下文变量: 来自前面步骤的输出数据
                  </div>
                </div>
              </div>
            </TabPane>

            {/* 变量配置 */}
            <TabPane tab="变量" itemKey="variables">
              <div style={{ padding: '16px 0' }}>
                <div style={{ marginBottom: '16px' }}>
                  <Button 
                    icon={<IconPlus />} 
                    onClick={addCustomVariable}
                    size="small"
                  >
                    添加自定义变量
                  </Button>
                </div>

                {customVariables.map((variable, index) => (
                  <div key={index} style={{ display: 'flex', gap: '8px', marginBottom: '8px', alignItems: 'flex-start' }}>
                    <Input
                      placeholder="变量名"
                      value={variable.key}
                      onChange={(value) => updateCustomVariable(index, 'key', value)}
                      style={{ flex: 1 }}
                      size="small"
                    />
                    <Input
                      placeholder="变量值"
                      value={variable.value}
                      onChange={(value) => updateCustomVariable(index, 'value', value)}
                      style={{ flex: 2 }}
                      size="small"
                    />
                    <Button
                      icon={<IconDelete />}
                      onClick={() => removeCustomVariable(index)}
                      type="danger"
                      theme="borderless"
                      size="small"
                    />
                  </div>
                ))}

                {/* 可用系统变量 */}
                <div style={{
                  marginTop: '16px',
                  padding: '12px',
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #91d5ff',
                  borderRadius: '6px'
                }}>
                  <div style={{ color: '#1890ff', fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}>
                    系统变量:
                  </div>
                  <div style={{ fontSize: '11px', color: '#1890ff', lineHeight: '1.4' }}>
                    • <code>${'{current_time}'}</code> - 当前时间<br/>
                    • <code>${'{workflow_name}'}</code> - 工作流名称<br/>
                    • <code>${'{step_name}'}</code> - 当前步骤名称<br/>
                    • <code>${'{execution_id}'}</code> - 执行ID<br/>
                    • <code>${'{user_name}'}</code> - 执行用户名
                  </div>
                </div>
              </div>
            </TabPane>

            {/* 发送选项 */}
            <TabPane tab="发送选项" itemKey="options">
              <div style={{ padding: '16px 0' }}>
                <Form.Select
                  field="priority"
                  label="消息优先级"
                >
                  <Option value="low">低优先级</Option>
                  <Option value="normal">普通优先级</Option>
                  <Option value="high">高优先级</Option>
                  <Option value="urgent">紧急</Option>
                </Form.Select>

                <Form.Switch
                  field="retryOnFailure"
                  label="发送失败时重试"
                />

                <Form.Switch
                  field="scheduledSend"
                  label="定时发送"
                />

                {formApi?.getValue('scheduledSend') && (
                  <Form.DatePicker
                    field="sendTime"
                    label="发送时间"
                    type="dateTime"
                    format="yyyy-MM-dd HH:mm:ss"
                  />
                )}

                {formApi?.getValue('notificationType') === 'email' && (
                  <Form.Switch
                    field="attachFiles"
                    label="附加文件"
                  />
                )}
              </div>
            </TabPane>
          </Tabs>
        </Form>

        {/* 发送结果说明 */}
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '6px'
        }}>
          <div style={{ color: '#389e0d', fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}>
            <IconInfoCircle style={{ marginRight: '4px' }} />
            发送结果变量:
          </div>
          <div style={{ color: '#52c41a', fontSize: '11px', lineHeight: '1.5' }}>
            • <code>result.success</code> - 发送是否成功<br/>
            • <code>result.messageId</code> - 消息ID（如果适用）<br/>
            • <code>result.sentCount</code> - 成功发送数量<br/>
            • <code>result.failedCount</code> - 发送失败数量<br/>
            • <code>result.error</code> - 错误信息（如果失败）
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationStepForm;