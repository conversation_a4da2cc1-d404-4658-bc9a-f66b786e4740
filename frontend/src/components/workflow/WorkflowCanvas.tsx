import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Button, Toast } from '@douyinfe/semi-ui';
import { IconCopy, IconDelete } from '@douyinfe/semi-icons';
import type {
  DesignerNode,
  Connection,
  DesignerConnection,
  Point,
  CanvasEvent,
  DragInfo,
  CanvasSelection
} from '../../types/workflow';

export interface WorkflowCanvasProps {
  nodes: DesignerNode[];
  connections: Connection[];
  selectedNodes: string[];
  selectedConnections: string[];
  onNodeUpdate: (nodeId: string, updates: Partial<DesignerNode>) => void;
  onNodeDelete: (nodeId: string) => void;
  onConnectionAdd: (sourceId: string, targetId: string) => void;
  onConnectionDelete: (connectionId: string) => void;
  onSelectionChange: (selection: CanvasSelection) => void;
  onCanvasEvent: (event: CanvasEvent) => void;
  scale: number;
  offset: Point;
  readonly?: boolean;
}

interface ConnectionPoint {
  nodeId: string;
  type: 'input' | 'output';
  position: Point;
}

const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  nodes,
  connections,
  selectedNodes,
  selectedConnections,
  onNodeUpdate,
  onNodeDelete,
  onConnectionAdd,
  onConnectionDelete,
  onSelectionChange,
  onCanvasEvent,
  scale,
  offset,
  readonly = false
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);
  const [dragInfo, setDragInfo] = useState<DragInfo | null>(null);
  const [liveNodePosition, setLiveNodePosition] = useState<Point | null>(null); // 用于实时拖拽位置
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<ConnectionPoint | null>(null);
  const [tempConnection, setTempConnection] = useState<Point | null>(null);
  const [selectionBox, setSelectionBox] = useState<{ start: Point; end: Point } | null>(null);

  // 获取鼠标在画布上的相对位置
  const getCanvasPosition = useCallback((e: React.MouseEvent): Point => {
    if (!canvasRef.current) return { x: 0, y: 0 };
    
    const rect = canvasRef.current.getBoundingClientRect();
    return {
      x: (e.clientX - rect.left - offset.x) / scale,
      y: (e.clientY - rect.top - offset.y) / scale
    };
  }, [scale, offset]);

  // 处理节点大小调整
  const handleNodeResize = useCallback((nodeId: string, newSize: { width: number; height: number }) => {
    onNodeUpdate(nodeId, { size: newSize });
  }, [onNodeUpdate]);

  // 获取节点的连接点位置
  const getConnectionPoint = useCallback((nodeId: string, type: 'input' | 'output'): Point => {
    const isDragging = dragInfo?.type === 'node' && dragInfo.nodeId === nodeId;
    const positionOverride = isDragging ? liveNodePosition : null;

    const node = nodes.find(n => n.id === nodeId);
    if (!node) return { x: 0, y: 0 };

    const position = positionOverride || node.position;

    const baseX = position.x + (type === 'output' ? node.size.width : 0);
    const baseY = position.y + node.size.height / 2;
    
    return { x: baseX, y: baseY };
  }, [nodes, dragInfo, liveNodePosition]);

  // 处理节点拖拽开始
  const handleNodeDragStart = useCallback((e: React.MouseEvent, nodeId: string) => {
    if (readonly) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const mousePos = getCanvasPosition(e);
    
    setDragInfo({
      type: 'node',
      nodeId,
      startPosition: mousePos,
      originalPosition: node.position,
      offset: {
        x: mousePos.x - node.position.x,
        y: mousePos.y - node.position.y
      }
    });
    setLiveNodePosition(node.position); // 初始化实时位置

    // 如果节点未选中，则选中它
    if (!selectedNodes.includes(nodeId)) {
      onSelectionChange({ nodes: [nodeId], connections: [] });
    }
  }, [readonly, nodes, getCanvasPosition, selectedNodes, onSelectionChange]);

  // 处理连接点拖拽开始
  const handleConnectionStart = useCallback((e: React.MouseEvent, nodeId: string, type: 'input' | 'output') => {
    if (readonly) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const position = getConnectionPoint(nodeId, type);
    
    setIsConnecting(true);
    setConnectionStart({ nodeId, type, position });
    setTempConnection(position);
  }, [readonly, getConnectionPoint]);

  // 处理鼠标移动
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const mousePos = getCanvasPosition(e);

    if (dragInfo && dragInfo.type === 'node') {
      // 节点拖拽: 只更新本地实时位置，不调用全局更新
      const newPosition = {
        x: mousePos.x - dragInfo.offset.x,
        y: mousePos.y - dragInfo.offset.y
      };

      // 网格对齐
      const gridSize = 20;
      newPosition.x = Math.round(newPosition.x / gridSize) * gridSize;
      newPosition.y = Math.round(newPosition.y / gridSize) * gridSize;

      setLiveNodePosition(newPosition);
    } else if (isConnecting && connectionStart) {
      // 连接线拖拽
      setTempConnection(mousePos);
    } else if (selectionBox) {
      // 选择框拖拽
      setSelectionBox({
        start: selectionBox.start,
        end: mousePos
      });
    }
  }, [dragInfo, isConnecting, connectionStart, selectionBox, getCanvasPosition]);

  // 处理鼠标释放
  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    // 节点拖拽结束，此时才调用全局更新
    if (dragInfo && dragInfo.type === 'node' && liveNodePosition) {
      onNodeUpdate(dragInfo.nodeId, { position: liveNodePosition });
      onCanvasEvent({
        type: 'node_moved',
        nodeId: dragInfo.nodeId,
        position: liveNodePosition
      });
      setDragInfo(null);
      setLiveNodePosition(null);
    }

    // 处理连接创建
    if (isConnecting) {
      const target = e.target as HTMLElement;
      const targetNodeId = target.getAttribute('data-node-id');
      const targetType = target.getAttribute('data-connection-type');

      if (targetNodeId && targetType && connectionStart) {
        // 验证连接的有效性
        if (connectionStart.nodeId !== targetNodeId && 
            connectionStart.type !== targetType) {
          
          const sourceId = connectionStart.type === 'output' ? connectionStart.nodeId : targetNodeId;
          const targetId = connectionStart.type === 'output' ? targetNodeId : connectionStart.nodeId;
          
          // 检查是否已存在相同连接
          const existingConnection = connections.find(
            c => c.sourceNodeId === sourceId && c.targetNodeId === targetId
          );
          
          if (!existingConnection) {
            onConnectionAdd(sourceId, targetId);
          } else {
            Toast.warning('连接已存在');
          }
        }
      }

      setIsConnecting(false);
      setConnectionStart(null);
      setTempConnection(null);
    }

    // 处理框选
    if (selectionBox) {
      const { start, end } = selectionBox;
      const selectedNodeIds = nodes.filter(node => {
        const nodeRect = {
          left: node.position.x,
          top: node.position.y,
          right: node.position.x + node.size.width,
          bottom: node.position.y + node.size.height
        };
        
        const selectionRect = {
          left: Math.min(start.x, end.x),
          top: Math.min(start.y, end.y),
          right: Math.max(start.x, end.x),
          bottom: Math.max(start.y, end.y)
        };
        
        return nodeRect.left < selectionRect.right &&
               nodeRect.right > selectionRect.left &&
               nodeRect.top < selectionRect.bottom &&
               nodeRect.bottom > selectionRect.top;
      }).map(node => node.id);

      onSelectionChange({ nodes: selectedNodeIds, connections: [] });
      setSelectionBox(null);
    }
  }, [dragInfo, liveNodePosition, onNodeUpdate, onCanvasEvent, isConnecting, connectionStart, connections, onConnectionAdd, selectionBox, nodes, onSelectionChange]);

  // 处理画布点击
  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      // 点击空白区域，清除选择
      onSelectionChange({ nodes: [], connections: [] });
    }
  }, [onSelectionChange]);

  // 处理画布右键菜单
  const handleCanvasContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (readonly) return;

    const position = getCanvasPosition(e);
    onCanvasEvent({
      type: 'canvas_context_menu',
      position
    });
  }, [readonly, getCanvasPosition, onCanvasEvent]);

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (readonly) return;

      if (e.key === 'Delete' || e.key === 'Backspace') {
        // 删除选中的节点和连接
        selectedNodes.forEach(nodeId => onNodeDelete(nodeId));
        selectedConnections.forEach(connectionId => onConnectionDelete(connectionId));
      } else if (e.key === 'Escape') {
        // 清除选择和取消操作
        onSelectionChange({ nodes: [], connections: [] });
        setIsConnecting(false);
        setConnectionStart(null);
        setTempConnection(null);
        setDragInfo(null);
      } else if (e.ctrlKey || e.metaKey) {
        // 处理画布级别的快捷键
        switch (e.key) {
          case 'a': // Ctrl+A 全选
            e.preventDefault();
            onSelectionChange({
              nodes: nodes.map(n => n.id),
              connections: connections.map(c => c.id)
            });
            break;
          case 'c': // Ctrl+C 复制
            if (selectedNodes.length > 0) {
              e.preventDefault();
              // TODO: 实现复制逻辑
              console.log('Copy nodes:', selectedNodes);
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [readonly, selectedNodes, selectedConnections, onNodeDelete, onConnectionDelete, onSelectionChange, nodes, connections]);

  // 渲染连接线
  const renderConnections = useCallback(() => {
    return (
      <svg
        ref={svgRef}
        className="workflow-canvas-connections"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 1
        }}
      >
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
          </marker>
        </defs>
        
        {/* 渲染现有连接 */}
        {connections.map(connection => {
          const sourcePoint = getConnectionPoint(connection.sourceNodeId, 'output');
          const targetPoint = getConnectionPoint(connection.targetNodeId, 'input');
          
          // 计算贝塞尔曲线控制点
          const controlPoint1 = { x: sourcePoint.x + 100, y: sourcePoint.y };
          const controlPoint2 = { x: targetPoint.x - 100, y: targetPoint.y };
          
          const path = `M ${sourcePoint.x * scale + offset.x} ${sourcePoint.y * scale + offset.y} 
                       C ${controlPoint1.x * scale + offset.x} ${controlPoint1.y * scale + offset.y}, 
                         ${controlPoint2.x * scale + offset.x} ${controlPoint2.y * scale + offset.y}, 
                         ${targetPoint.x * scale + offset.x} ${targetPoint.y * scale + offset.y}`;

          const isSelected = selectedConnections.includes(connection.id);
          
          return (
            <g key={connection.id}>
              <path
                d={path}
                stroke={isSelected ? '#1890ff' : '#666'}
                strokeWidth={isSelected ? 3 : 2}
                fill="none"
                markerEnd="url(#arrowhead)"
                style={{ pointerEvents: 'stroke', cursor: 'pointer' }}
                onClick={() => onSelectionChange({ 
                  nodes: [], 
                  connections: [connection.id] 
                })}
              />
              {/* 连接线标签 */}
              {connection.label && (
                <text
                  x={(sourcePoint.x + targetPoint.x) * scale / 2 + offset.x}
                  y={(sourcePoint.y + targetPoint.y) * scale / 2 + offset.y - 10}
                  textAnchor="middle"
                  className="connection-label"
                  fill="#666"
                  fontSize="12"
                >
                  {connection.label}
                </text>
              )}
            </g>
          );
        })}
        
        {/* 渲染临时连接线 */}
        {isConnecting && connectionStart && tempConnection && (
          <path
            d={`M ${connectionStart.position.x * scale + offset.x} ${connectionStart.position.y * scale + offset.y} 
                L ${tempConnection.x * scale + offset.x} ${tempConnection.y * scale + offset.y}`}
            stroke="#1890ff"
            strokeWidth={2}
            strokeDasharray="5,5"
            fill="none"
          />
        )}
        
        {/* 渲染选择框 */}
        {selectionBox && (
          <rect
            x={Math.min(selectionBox.start.x, selectionBox.end.x) * scale + offset.x}
            y={Math.min(selectionBox.start.y, selectionBox.end.y) * scale + offset.y}
            width={Math.abs(selectionBox.end.x - selectionBox.start.x) * scale}
            height={Math.abs(selectionBox.end.y - selectionBox.start.y) * scale}
            fill="rgba(24, 144, 255, 0.1)"
            stroke="#1890ff"
            strokeWidth={1}
            strokeDasharray="3,3"
          />
        )}
      </svg>
    );
  }, [connections, selectedConnections, isConnecting, connectionStart, tempConnection, selectionBox, getConnectionPoint, scale, offset, onSelectionChange]);

  return (
    <div 
      className="workflow-canvas"
      ref={canvasRef}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onClick={handleCanvasClick}
      onContextMenu={handleCanvasContextMenu}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        cursor: dragInfo ? 'grabbing' : isConnecting ? 'crosshair' : 'default',
        background: `
          radial-gradient(circle, #ccc 1px, transparent 1px)
        `,
        backgroundSize: `${20 * scale}px ${20 * scale}px`,
        backgroundPosition: `${offset.x}px ${offset.y}px`
      }}
    >
      {renderConnections()}
      
      {/* 渲染节点 */}
            {nodes.map(node => {
        const isDragging = dragInfo?.type === 'node' && dragInfo.nodeId === node.id;
        const nodeToRender = isDragging && liveNodePosition 
          ? { ...node, position: liveNodePosition } 
          : node;

        return (
          <WorkflowNode
            key={node.id}
            node={nodeToRender}
            selected={selectedNodes.includes(node.id)}
            scale={scale}
            readonly={readonly}
            onDragStart={(e) => handleNodeDragStart(e, node.id)}
            onConnectionStart={handleConnectionStart}
            onDelete={() => onNodeDelete(node.id)}
            onNodeResize={handleNodeResize}
          />
        )
      })}
    </div>
  );
};

// 工作流节点组件
interface WorkflowNodeProps {
  node: DesignerNode;
  selected: boolean;
  scale: number;
  readonly?: boolean;
  onDragStart: (e: React.MouseEvent) => void;
  onConnectionStart: (e: React.MouseEvent, nodeId: string, type: 'input' | 'output') => void;
  onDelete: () => void;
  onNodeResize: (nodeId: string, newSize: { width: number; height: number }) => void; // 新增
}

const WorkflowNode: React.FC<WorkflowNodeProps> = React.memo(({
  node,
  selected,
  scale,
  readonly = false,
  onDragStart,
  onConnectionStart,
  onDelete,
  onNodeResize // 新增
}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 });
  
  const getNodeIcon = (stepType: string) => {
    switch (stepType) {
      case 'HTTP_REQUEST': return '🌐';
      case 'DATABASE_OPERATION': return '🗄️';
      case 'NOTIFICATION': return '📧';
      case 'CONDITION': return '❓';
      case 'LOOP': return '🔄';
      case 'DELAY': return '⏰';
      case 'ACTION': return '⚡';
      case 'APPROVAL': return '✋';
      case 'PARALLEL': return '🔀';
      case 'SUBPROCESS': return '📦';
      case 'SCRIPT': return '📝';
      case 'FILE_OPERATION': return '📁';
      default: return '⚙️';
    }
  };

  const getNodeColor = (stepType: string) => {
    switch (stepType) {
      case 'HTTP_REQUEST': return '#52c41a';
      case 'DATABASE_OPERATION': return '#1890ff';
      case 'NOTIFICATION': return '#faad14';
      case 'CONDITION': return '#722ed1';
      case 'LOOP': return '#eb2f96';
      case 'DELAY': return '#13c2c2';
      case 'ACTION': return '#fa541c';
      case 'APPROVAL': return '#13c2c2';
      case 'PARALLEL': return '#2f54eb';
      case 'SUBPROCESS': return '#722ed1';
      case 'SCRIPT': return '#52c41a';
      case 'FILE_OPERATION': return '#fa8c16';
      default: return '#666666';
    }
  };

  // 处理大小调整开始
  const handleResizeStart = (e: React.MouseEvent) => {
    if (readonly) return;
    
    e.stopPropagation();
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: node.size.width,
      height: node.size.height
    });
  };

  // 处理鼠标移动（用于大小调整）
  useEffect(() => {
    if (!isResizing) return;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = (e.clientX - resizeStart.x) / scale;
      const deltaY = (e.clientY - resizeStart.y) / scale;
      
      const newWidth = Math.max(150, resizeStart.width + deltaX); // 最小宽度150px
      const newHeight = Math.max(80, resizeStart.height + deltaY); // 最小高度80px
      
      onNodeResize(node.id, { width: newWidth, height: newHeight });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, resizeStart, node.id, scale, onNodeResize]);

  return (
    <div
      className={`workflow-node ${selected ? 'selected' : ''} ${node.step.type}`}
      style={{
        position: 'absolute',
        left: node.position.x * scale,
        top: node.position.y * scale,
        width: node.size.width * scale,
        height: node.size.height * scale,
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
        zIndex: selected ? 10 : 2,
        border: selected ? '2px solid #1890ff' : '2px solid #d9d9d9',
        borderRadius: '8px',
        backgroundColor: 'white',
        boxShadow: selected 
          ? '0 4px 12px rgba(24, 144, 255, 0.3)' 
          : '0 2px 8px rgba(0, 0, 0, 0.1)',
        cursor: readonly ? 'default' : 'grab',
        userSelect: 'none'
      }}
      onMouseDown={onDragStart}
    >
      {/* 节点头部 */}
      <div 
        className="workflow-node-header"
        style={{
          display: 'flex',
          alignItems: 'center',
          padding: '8px 12px',
          backgroundColor: getNodeColor(node.step.type),
          color: 'white',
          borderRadius: '6px 6px 0 0',
          fontSize: '14px',
          fontWeight: 500
        }}
      >
        <span style={{ marginRight: '8px' }}>
          {getNodeIcon(node.step.type)}
        </span>
        <span className="workflow-node-title">
          {node.step.name || `${node.step.type} 步骤`}
        </span>
        {!readonly && (
          <div style={{ marginLeft: 'auto', display: 'flex', gap: '4px' }}>
            <Button
              icon={<IconCopy />}
              type="tertiary"
              size="small"
              style={{ color: 'white', padding: '2px' }}
              onClick={(e) => {
                e.stopPropagation();
                // TODO: 实现复制节点功能
              }}
            />
            <Button
              icon={<IconDelete />}
              type="tertiary" 
              size="small"
              style={{ color: 'white', padding: '2px' }}
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
            />
          </div>
        )}
      </div>

      {/* 节点内容 */}
      <div 
        className="workflow-node-content"
        style={{
          padding: '12px',
          fontSize: '12px',
          color: '#666',
          height: 'calc(100% - 40px)', // 减去头部高度
          overflow: 'auto'
        }}
      >
        {node.step.description && (
          <div className="workflow-node-description" style={{ marginBottom: '8px' }}>
            {node.step.description}
          </div>
        )}
        
        {/* 显示关键配置信息 */}
        {node.step.type === 'HTTP_REQUEST' && node.step.config.url && (
          <div className="workflow-node-config">
            <span style={{ fontWeight: 500 }}>URL:</span> {node.step.config.url}
          </div>
        )}
        
        {node.step.type === 'CONDITION' && node.step.config.expression && (
          <div className="workflow-node-config">
            <span style={{ fontWeight: 500 }}>条件:</span> {node.step.config.expression}
          </div>
        )}
        
        {node.step.type === 'DATABASE_OPERATION' && node.step.config.query && (
          <div className="workflow-node-config">
            <span style={{ fontWeight: 500 }}>查询:</span> {node.step.config.query.substring(0, 30)}...
          </div>
        )}
        
        {node.step.type === 'NOTIFICATION' && (node.step.config.to || node.step.config.phone) && (
          <div className="workflow-node-config">
            <span style={{ fontWeight: 500 }}>接收者:</span> {node.step.config.to || node.step.config.phone}
          </div>
        )}
      </div>

      {/* 连接点 */}
      {!readonly && (
        <>
          {/* 输入连接点 */}
          <div
            className="workflow-connection-point input"
            data-node-id={node.id}
            data-connection-type="input"
            style={{
              position: 'absolute',
              left: '-6px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '12px',
              height: '12px',
              backgroundColor: '#1890ff',
              border: '2px solid white',
              borderRadius: '50%',
              cursor: 'crosshair',
              zIndex: 5
            }}
            onMouseDown={(e) => onConnectionStart(e, node.id, 'input')}
          />
          
          {/* 输出连接点 */}
          <div
            className="workflow-connection-point output"
            data-node-id={node.id}
            data-connection-type="output"
            style={{
              position: 'absolute',
              right: '-6px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '12px',
              height: '12px',
              backgroundColor: '#52c41a',
              border: '2px solid white',
              borderRadius: '50%',
              cursor: 'crosshair',
              zIndex: 5
            }}
            onMouseDown={(e) => onConnectionStart(e, node.id, 'output')}
          />
        </>
      )}

      {/* 节点状态指示器 */}
      {node.status && (
        <div
          className="workflow-node-status"
          style={{
            position: 'absolute',
            top: '-8px',
            right: '-8px',
            width: '16px',
            height: '16px',
            borderRadius: '50%',
            backgroundColor: 
              node.status === 'success' ? '#52c41a' :
              node.status === 'error' ? '#ff4d4f' :
              node.status === 'running' ? '#1890ff' : '#d9d9d9',
            border: '2px solid white',
            zIndex: 6
          }}
          title={`状态: ${node.status}`}
        />
      )}

      {/* 大小调整手柄（仅在选中时显示） */}
      {!readonly && selected && (
        <div
          className="workflow-node-resize-handle"
          style={{
            position: 'absolute',
            right: '-6px',
            bottom: '-6px',
            width: '12px',
            height: '12px',
            backgroundColor: '#1890ff',
            border: '2px solid white',
            borderRadius: '50%',
            cursor: 'nw-resize',
            zIndex: 5
          }}
          onMouseDown={handleResizeStart}
        />
      )}
    </div>
  );
});

export default WorkflowCanvas;