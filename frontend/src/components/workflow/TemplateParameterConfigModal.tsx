/**
 * 模板参数配置弹窗组件
 * 用于在应用工作流模板时配置自定义参数
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Typography,
  Divider,
  Tag,
  Tooltip,
  Card
} from '@douyinfe/semi-ui';
import {
  IconInfoCircle,
  IconSetting,
  IconPlay,
  IconClose,
  IconClock,
  IconUser,
} from '@douyinfe/semi-icons';
import type { WorkflowTemplate } from '../../services/workflow-template';

const { Text, Title } = Typography;

export interface TemplateParameterConfig {
  workflowName?: string;
  workflowDescription?: string;
  variables?: Record<string, any>;
  settings?: {
    timeout?: number;
    maxRetries?: number;
    errorHandling?: 'stop' | 'continue' | 'skip';
  };
  customTags?: string[];
}

export interface TemplateParameterConfigModalProps {
  visible: boolean;
  template: WorkflowTemplate | null;
  loading?: boolean;
  onConfirm: (config: TemplateParameterConfig) => void;
  onCancel: () => void;
}

const TemplateParameterConfigModal: React.FC<TemplateParameterConfigModalProps> = ({
  visible,
  template,
  loading = false,
  onConfirm,
  onCancel
}) => {
  const [formApi, setFormApi] = useState<any>();
  const [customTags, setCustomTags] = useState<string[]>([]);
  const [tagInputValue, setTagInputValue] = useState('');

  // 表单初始值
  const getInitialValues = useCallback(() => {
    if (!template) return {};

    return {
      workflowName: `基于模板: ${template.name}`,
      workflowDescription: `从模板 "${template.name}" 创建的工作流\n\n${template.description}`,
      timeout: template.workflow?.settings?.timeout || 300000,
      maxRetries: template.workflow?.settings?.maxRetries || 3,
      errorHandling: template.workflow?.settings?.errorHandling || 'stop',
      enableNotifications: true,
      enableLogging: true,
      priority: 'MEDIUM'
    };
  }, [template]);

  // 重置表单数据
  useEffect(() => {
    if (visible && template && formApi) {
      const initialValues = getInitialValues();
      formApi.setValues(initialValues);
      setCustomTags([]);
      setTagInputValue('');
    }
  }, [visible, template, formApi, getInitialValues]);

  // 处理确认
  const handleConfirm = useCallback(async () => {
    if (!formApi || !template) return;

    try {
      const values = formApi.getValues();
      
      const config: TemplateParameterConfig = {
        workflowName: values.workflowName,
        workflowDescription: values.workflowDescription,
        variables: {
          ...template.workflow?.variables,
          // 用户自定义变量
          notification_enabled: values.enableNotifications,
          logging_enabled: values.enableLogging,
          priority: values.priority
        },
        settings: {
          timeout: values.timeout,
          maxRetries: values.maxRetries,
          errorHandling: values.errorHandling
        },
        customTags: customTags
      };

      onConfirm(config);
    } catch (error) {
      console.error('配置参数验证失败:', error);
    }
  }, [formApi, template, customTags, onConfirm]);

  // 添加标签
  const handleAddTag = useCallback(() => {
    if (tagInputValue.trim() && !customTags.includes(tagInputValue.trim())) {
      setCustomTags(prev => [...prev, tagInputValue.trim()]);
      setTagInputValue('');
    }
  }, [tagInputValue, customTags]);

  // 移除标签
  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setCustomTags(prev => prev.filter(tag => tag !== tagToRemove));
  }, []);

  if (!template) return null;

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <IconSetting />
          <span>配置模板参数</span>
        </div>
      }
      visible={visible}
      onCancel={onCancel}
      width={700}
      style={{ top: '10vh' }}
      bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
      footer={
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button
            type="primary"
            theme="solid"
            icon={<IconPlay />}
            onClick={handleConfirm}
            loading={loading}
          >
            应用模板
          </Button>
        </Space>
      }
    >
      {/* 模板信息 */}
      <Card style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', gap: 16 }}>
          <img
            src={template.preview.thumbnail}
            alt={template.name}
            style={{
              width: 80,
              height: 60,
              objectFit: 'cover',
              borderRadius: 6,
              backgroundColor: '#f5f5f5'
            }}
          />
          <div style={{ flex: 1 }}>
            <Title heading={6} style={{ margin: '0 0 8px 0' }}>
              {template.name}
            </Title>
            <Text type="tertiary" style={{ fontSize: 13 }}>
              {template.description}
            </Text>
            <div style={{ marginTop: 8, display: 'flex', gap: 12, alignItems: 'center' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <IconClock size="small" />
                <Text style={{ fontSize: 12 }}>
                  {Math.floor(template.estimatedDuration / 60)} 小时
                  {template.estimatedDuration % 60 > 0 && ` ${template.estimatedDuration % 60} 分钟`}
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <IconUser size="small" />
                <Text style={{ fontSize: 12 }}>{template.author}</Text>
              </div>
              <Tag size="small" color="blue">
                {template.difficulty === 'beginner' ? '初级' : 
                 template.difficulty === 'intermediate' ? '中级' : '高级'}
              </Tag>
            </div>
          </div>
        </div>
      </Card>

      <Form<TemplateParameterConfig>
        getFormApi={api => setFormApi(api)}
        labelPosition="left"
        labelWidth="120px"
        initValues={getInitialValues()}
      >
        <Title heading={6} style={{ margin: '0 0 16px 0' }}>
          基础配置
        </Title>

        <Form.Input
          field="workflowName"
          label="工作流名称"
          placeholder="请输入工作流名称"
          rules={[{ required: true, message: '工作流名称不能为空' }]}
        />

        <Form.TextArea
          field="workflowDescription"
          label="工作流描述"
          placeholder="请输入工作流描述"
          rows={3}
        />

        <Divider />

        <Title heading={6} style={{ margin: '0 0 16px 0' }}>
          执行配置
        </Title>

        <Form.InputNumber
          field="timeout"
          label={
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <span>超时时间</span>
              <Tooltip content="工作流执行的最大时间限制（毫秒）">
                <IconInfoCircle size="small" />
              </Tooltip>
            </div>
          }
          placeholder="300000"
          min={10000}
          max={3600000}
          step={10000}
          suffix="ms"
        />

        <Form.InputNumber
          field="maxRetries"
          label={
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <span>最大重试</span>
              <Tooltip content="单个步骤失败时的最大重试次数">
                <IconInfoCircle size="small" />
              </Tooltip>
            </div>
          }
          placeholder="3"
          min={0}
          max={10}
        />

        <Form.Select
          field="errorHandling"
          label="错误处理"
          placeholder="选择错误处理方式"
          optionList={[
            { value: 'stop', label: '停止执行' },
            { value: 'continue', label: '继续执行' },
            { value: 'skip', label: '跳过当前步骤' }
          ]}
        />

        <Form.Select
          field="priority"
          label="执行优先级"
          placeholder="选择执行优先级"
          optionList={[
            { value: 'LOW', label: '低优先级' },
            { value: 'MEDIUM', label: '中等优先级' },
            { value: 'HIGH', label: '高优先级' },
            { value: 'URGENT', label: '紧急' }
          ]}
        />

        <Divider />

        <Title heading={6} style={{ margin: '0 0 16px 0' }}>
          功能选项
        </Title>

        <Form.Switch
          field="enableNotifications"
          label="启用通知"
          checkedText="开启"
          uncheckedText="关闭"
        />

        <Form.Switch
          field="enableLogging"
          label="详细日志"
          checkedText="开启"
          uncheckedText="关闭"
        />

        {/* 自定义标签 */}
        <div style={{ marginTop: 20 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 4, marginBottom: 8 }}>
            {/* <IconTag size="small" /> */}
            <Text strong>自定义标签</Text>
          </div>
          
          <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
            <Input
              value={tagInputValue}
              onChange={setTagInputValue}
              placeholder="输入标签名称"
              onEnterPress={handleAddTag}
              style={{ flex: 1 }}
            />
            <Button onClick={handleAddTag} disabled={!tagInputValue.trim()}>
              添加
            </Button>
          </div>

          {customTags.length > 0 && (
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
              {customTags.map(tag => (
                <Tag
                  key={tag}
                  closable
                  onClose={() => handleRemoveTag(tag)}
                  color="blue"
                >
                  {tag}
                </Tag>
              ))}
            </div>
          )}
        </div>
      </Form>

      <div style={{ 
        marginTop: 20, 
        padding: 12, 
        backgroundColor: '#f8f9fa', 
        borderRadius: 6,
        fontSize: 12,
        color: '#666'
      }}>
        <IconInfoCircle style={{ marginRight: 4 }} />
        应用模板后，您可以在工作流设计器中进一步修改和调整工作流配置。
      </div>
    </Modal>
  );
};

export default TemplateParameterConfigModal;