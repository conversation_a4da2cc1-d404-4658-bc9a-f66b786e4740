# 通信集成配置系统

## 概述

本系统提供了完整的通信集成配置管理功能，支持企业微信、钉钉、Slack、飞书等主流通信平台的集成配置。

## 功能特性

- **多平台支持**: 支持企业微信、钉钉、Slack、飞书等通信平台
- **统一配置**: 每个平台都有专门的配置表单，包含平台特定的参数
- **连接测试**: 支持测试连接功能，验证配置是否正确
- **状态管理**: 支持启用/禁用集成，设置优先级
- **安全保护**: 敏感信息（如密钥、Token）支持显示/隐藏切换

## 支持的平台

### 1. 企业微信 (WeChat Work)
- **必需参数**: 企业ID (CorpId)、应用ID (AgentId)、应用密钥 (Secret)
- **可选参数**: Token、EncodingAESKey、Webhook地址
- **功能**: 消息推送、文件上传、用户同步

### 2. 钉钉 (DingTalk)
- **必需参数**: 应用Key (AppKey)、应用密钥 (AppSecret)、应用ID (AgentId)、企业ID (CorpId)
- **可选参数**: Webhook地址
- **功能**: 消息推送、文件上传、用户同步、@功能

### 3. Slack
- **必需参数**: Bot Token、Signing Secret、默认频道
- **可选参数**: App-Level Token、Webhook地址
- **功能**: 消息推送、文件上传、用户同步、线程回复

### 4. 飞书 (Feishu)
- **必需参数**: 应用ID (App ID)、应用密钥 (App Secret)
- **可选参数**: 验证Token、加密密钥、Webhook地址
- **功能**: 消息推送、文件上传、用户同步、加密通信

## 使用方法

### 1. 在集成管理页面中
```typescript
import { CommunicationIntegrationForm } from '../components/integration';

// 在列表页中，点击编辑按钮时
const handleEditCommunication = (integration) => {
  const communicationConfig = {
    id: integration.id,
    type: integration.type,
    name: integration.name,
    description: integration.description,
    enabled: integration.enabled,
    priority: integration.priority,
    config: { /* 平台特定配置 */ }
  };
  
  setEditingCommunicationConfig(communicationConfig);
  setShowCommunicationModal(true);
};
```

### 2. 配置表单组件
```typescript
<CommunicationIntegrationForm
  visible={showCommunicationModal}
  onCancel={() => setShowCommunicationModal(false)}
  onSubmit={handleCommunicationSubmit}
  onTest={handleCommunicationTest}
  initialConfig={editingCommunicationConfig}
  loading={loading}
/>
```

### 3. 处理配置提交
```typescript
const handleCommunicationSubmit = async (config: CommunicationConfig) => {
  try {
    if (config.id) {
      // 更新现有配置
      await communicationIntegrationService.updateIntegration(config.id, config);
    } else {
      // 创建新配置
      await communicationIntegrationService.createIntegration(config);
    }
    Toast.success('配置保存成功');
  } catch (error) {
    Toast.error('配置保存失败');
  }
};
```

### 4. 测试连接
```typescript
const handleCommunicationTest = async (config: CommunicationConfig): Promise<boolean> => {
  try {
    const response = await communicationIntegrationService.testConnection({
      type: config.type,
      config: config.config
    });
    return response.success;
  } catch (error) {
    return false;
  }
};
```

## 配置参数说明

### 通用参数
- **集成名称**: 用于标识该集成的名称
- **描述**: 集成的详细说明
- **优先级**: 1-10，数字越大优先级越高
- **启用状态**: 是否启用该集成

### 平台特定参数

#### 企业微信
- **企业ID (CorpId)**: 在企业微信管理后台可以找到
- **应用ID (AgentId)**: 在企业微信应用管理中可以找到
- **应用密钥 (Secret)**: 在企业微信应用管理中可以找到
- **Token**: 用于消息验证（可选）
- **EncodingAESKey**: 用于消息加解密（可选）

#### 钉钉
- **应用Key (AppKey)**: 在钉钉开放平台应用管理中可以找到
- **应用密钥 (AppSecret)**: 在钉钉开放平台应用管理中可以找到
- **应用ID (AgentId)**: 在钉钉应用管理中可以找到
- **企业ID (CorpId)**: 在钉钉企业管理后台可以找到

#### Slack
- **Bot Token**: 在Slack App设置中可以找到Bot User OAuth Token
- **Signing Secret**: 在Slack App设置中可以找到Signing Secret
- **默认频道**: 系统默认发送消息的频道

#### 飞书
- **应用ID (App ID)**: 在飞书开放平台应用管理中可以找到
- **应用密钥 (App Secret)**: 在飞书开放平台应用管理中可以找到
- **验证Token**: 用于验证Webhook请求来源（可选）
- **加密密钥**: 用于消息加解密（可选）

## 注意事项

1. **敏感信息保护**: 密钥、Token等敏感信息默认隐藏，点击眼睛图标可以切换显示/隐藏
2. **必填字段验证**: 每个平台都有必填的配置参数，请确保完整填写
3. **连接测试**: 建议在保存配置前先进行连接测试，确保配置正确
4. **优先级设置**: 当多个集成同时启用时，系统会按照优先级顺序处理
5. **Webhook配置**: 如果需要接收平台回调消息，请配置相应的Webhook地址

## 扩展开发

如需添加新的通信平台支持，请：

1. 在 `CommunicationConfig` 类型中添加新的平台类型
2. 在 `renderChannelSpecificFields` 方法中添加新平台的配置字段
3. 在 `getChannelTitle` 和 `getChannelDescription` 方法中添加新平台的标题和描述
4. 在通信集成服务中添加新平台的配置模板和验证逻辑

## 相关文件

- `CommunicationIntegrationForm.tsx`: 主要的配置表单组件
- `communication-integration.ts`: 通信集成服务
- `IntegrationManagePage.tsx`: 集成管理页面
- `index.ts`: 组件导出文件
