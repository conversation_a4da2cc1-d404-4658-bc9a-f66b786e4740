/**
 * 通信集成配置表单
 * 专注于单个通信通道的配置
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Typography,
  Toast,
  Spin,
  Form,
  Button,
  Space,
  Card,
  Divider,
  Select
} from '@douyinfe/semi-ui';
import { IconEyeOpened, IconEyeClosed, IconPlay } from '@douyinfe/semi-icons';

const { Title, Text } = Typography;

export interface CommunicationConfig {
  id?: string;
  type: 'wechat_work' | 'dingtalk' | 'slack' | 'feishu';
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  config: any;
}

interface CommunicationIntegrationFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (config: CommunicationConfig) => Promise<void>;
  onTest?: (config: CommunicationConfig) => Promise<boolean>;
  initialConfig?: CommunicationConfig;
  loading?: boolean;
}

const CommunicationIntegrationForm: React.FC<CommunicationIntegrationFormProps> = ({
  visible,
  onCancel,
  onSubmit,
  onTest,
  initialConfig,
  loading = false
}) => {
  const [submitting, setSubmitting] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [showSecret, setShowSecret] = useState(false);
  const formApi = useRef<any>();
  const [formData, setFormData] = useState<CommunicationConfig>({
    type: 'wechat_work',
    name: '',
    description: '',
    enabled: true,
    priority: 5,
    config: {}
  });

  // 根据初始配置设置表单数据
  useEffect(() => {
    if (visible) {
      if (initialConfig) {
        setFormData(initialConfig);
        // 同步到表单
        if (formApi.current) {
          formApi.current.setValues({
            type: initialConfig.type,
            name: initialConfig.name,
            description: initialConfig.description,
            enabled: initialConfig.enabled,
            priority: initialConfig.priority,
            ...initialConfig.config
          });
        }
      } else {
        // 重置表单
        const defaultData = {
          type: 'wechat_work' as const,
          name: '',
          description: '',
          enabled: true,
          priority: 5,
          config: {}
        };
        setFormData(defaultData);
        if (formApi.current) {
          formApi.current.reset();
          formApi.current.setValues(defaultData);
        }
      }
    }
  }, [visible, initialConfig]);

  const handleSubmit = async () => {
    try {
      // 验证表单
      await formApi.current?.validate();
      setSubmitting(true);

      // 构建提交数据
      const values = formApi.current?.getValues();
      const submitData: CommunicationConfig = {
        ...formData,
        name: values.name,
        description: values.description,
        enabled: values.enabled,
        priority: values.priority,
        config: {
          ...Object.keys(values).reduce((acc, key) => {
            if (!['type', 'name', 'description', 'enabled', 'priority'].includes(key)) {
              acc[key] = values[key];
            }
            return acc;
          }, {} as any)
        }
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error('Submit failed:', error);
      Toast.error('表单验证失败，请检查输入信息');
    } finally {
      setSubmitting(false);
    }
  };

  const handleTest = async (): Promise<boolean> => {
    if (!onTest) return false;

    try {
      // 验证表单
      await formApi.current?.validate();
      setTestLoading(true);

      // 构建测试数据
      const values = formApi.current?.getValues();
      const testData: CommunicationConfig = {
        ...formData,
        name: values.name,
        description: values.description,
        enabled: values.enabled,
        priority: values.priority,
        config: {
          ...Object.keys(values).reduce((acc, key) => {
            if (!['type', 'name', 'description', 'enabled', 'priority'].includes(key)) {
              acc[key] = values[key];
            }
            return acc;
          }, {} as any)
        }
      };

      const result = await onTest(testData);
      if (result) {
        Toast.success('连接测试成功');
      } else {
        Toast.error('连接测试失败，请检查配置信息');
      }
      return result;
    } catch (error) {
      console.error('Test failed:', error);
      Toast.error('连接测试失败，请检查配置信息');
      return false;
    } finally {
      setTestLoading(false);
    }
  };

  const getChannelTitle = () => {
    switch (formData.type) {
      case 'wechat_work':
        return '企业微信集成配置';
      case 'dingtalk':
        return '钉钉集成配置';
      case 'slack':
        return 'Slack集成配置';
      case 'feishu':
        return '飞书集成配置';
      default:
        return '通信集成配置';
    }
  };

  const getChannelDescription = () => {
    switch (formData.type) {
      case 'wechat_work':
        return '配置企业微信应用集成，支持消息推送和用户同步';
      case 'dingtalk':
        return '配置钉钉应用集成，支持智能工作流和消息通知';
      case 'slack':
        return '配置Slack工作区集成，支持频道消息和文件共享';
      case 'feishu':
        return '配置飞书应用集成，支持智能机器人和消息推送';
      default:
        return '配置通信平台集成参数';
    }
  };

  const renderChannelSpecificFields = () => {
    switch (formData.type) {
      case 'wechat_work':
        return (
          <>
            <Form.Input
              field="corpId"
              label="企业ID (CorpId)"
              placeholder="请输入企业微信的企业ID"
              rules={[{ required: true, message: '请输入企业ID' }]}
              helpText="在企业微信管理后台可以找到企业ID"
              initValue={formData.config?.corpId || ''}
            />
            <Form.Input
              field="agentId"
              label="应用ID (AgentId)"
              placeholder="请输入企业微信应用的AgentId"
              rules={[{ required: true, message: '请输入应用ID' }]}
              helpText="在企业微信应用管理中可以找到AgentId"
              initValue={formData.config?.agentId || ''}
            />
            <Form.Input
              field="secret"
              label="应用密钥 (Secret)"
              placeholder="请输入企业微信应用的Secret"
              rules={[{ required: true, message: '请输入应用密钥' }]}
              helpText="在企业微信应用管理中可以找到Secret"
              initValue={formData.config?.secret || ''}
              suffix={
                <Button
                  icon={showSecret ? <IconEyeOpened /> : <IconEyeClosed />}
                  type="tertiary"
                  onClick={() => setShowSecret(!showSecret)}
                  style={{ border: 'none' }}
                />
              }
              type={showSecret ? 'text' : 'password'}
            />
            <Form.Input
              field="token"
              label="Token (可选)"
              placeholder="请输入Token（用于消息验证）"
              helpText="如果配置了Token，系统会验证消息来源"
              initValue={formData.config?.token || ''}
            />
            <Form.Input
              field="encodingAESKey"
              label="EncodingAESKey (可选)"
              placeholder="请输入EncodingAESKey"
              helpText="用于消息加解密，如果配置了会启用加密模式"
              initValue={formData.config?.encodingAESKey || ''}
            />
          </>
        );
      case 'dingtalk':
        return (
          <>
            <Form.Input
              field="appKey"
              label="应用Key (AppKey)"
              placeholder="请输入钉钉应用的AppKey"
              rules={[{ required: true, message: '请输入应用Key' }]}
              helpText="在钉钉开放平台应用管理中可以找到AppKey"
              initValue={formData.config?.appKey || ''}
            />
            <Form.Input
              field="appSecret"
              label="应用密钥 (AppSecret)"
              placeholder="请输入钉钉应用的AppSecret"
              rules={[{ required: true, message: '请输入应用密钥' }]}
              helpText="在钉钉开放平台应用管理中可以找到AppSecret"
              initValue={formData.config?.appSecret || ''}
              suffix={
                <Button
                  icon={showSecret ? <IconEyeOpened /> : <IconEyeClosed />}
                  type="tertiary"
                  onClick={() => setShowSecret(!showSecret)}
                  style={{ border: 'none' }}
                />
              }
              type={showSecret ? 'text' : 'password'}
            />
            <Form.Input
              field="agentId"
              label="应用ID (AgentId)"
              placeholder="请输入钉钉应用的AgentId"
              rules={[{ required: true, message: '请输入应用ID' }]}
              helpText="在钉钉应用管理中可以找到AgentId"
              initValue={formData.config?.agentId || ''}
            />
            <Form.Input
              field="corpId"
              label="企业ID (CorpId)"
              placeholder="请输入钉钉企业的CorpId"
              rules={[{ required: true, message: '请输入企业ID' }]}
              helpText="在钉钉企业管理后台可以找到CorpId"
              initValue={formData.config?.corpId || ''}
            />
          </>
        );
      case 'slack':
        return (
          <>
            <Form.Input
              field="botToken"
              label="Bot Token"
              placeholder="请输入Slack Bot Token"
              rules={[{ required: true, message: '请输入Bot Token' }]}
              helpText="在Slack App设置中可以找到Bot User OAuth Token"
              initValue={formData.config?.botToken || ''}
              suffix={
                <Button
                  icon={showSecret ? <IconEyeOpened /> : <IconEyeClosed />}
                  type="tertiary"
                  onClick={() => setShowSecret(!showSecret)}
                  style={{ border: 'none' }}
                />
              }
              type={showSecret ? 'text' : 'password'}
            />
            <Form.Input
              field="signingSecret"
              label="Signing Secret"
              placeholder="请输入Slack Signing Secret"
              rules={[{ required: true, message: '请输入Signing Secret' }]}
              helpText="在Slack App设置中可以找到Signing Secret"
              initValue={formData.config?.signingSecret || ''}
              suffix={
                <Button
                  icon={showSecret ? <IconEyeOpened /> : <IconEyeClosed />}
                  type="tertiary"
                  onClick={() => setShowSecret(!showSecret)}
                  style={{ border: 'none' }}
                />
              }
              type={showSecret ? 'text' : 'password'}
            />
            <Form.Input
              field="defaultChannel"
              label="默认频道"
              placeholder="请输入默认频道名称，如 #general"
              rules={[{ required: true, message: '请输入默认频道' }]}
              helpText="系统默认发送消息的频道"
              initValue={formData.config?.defaultChannel || ''}
            />
          </>
        );
      case 'feishu':
        return (
          <>
            <Form.Input
              field="appId"
              label="应用ID (App ID)"
              placeholder="请输入飞书应用的App ID"
              rules={[{ required: true, message: '请输入应用ID' }]}
              helpText="在飞书开放平台应用管理中可以找到App ID"
              initValue={formData.config?.appId || ''}
            />
            <Form.Input
              field="appSecret"
              label="应用密钥 (App Secret)"
              placeholder="请输入飞书应用的App Secret"
              rules={[{ required: true, message: '请输入应用密钥' }]}
              helpText="在飞书开放平台应用管理中可以找到App Secret"
              initValue={formData.config?.appSecret || ''}
              suffix={
                <Button
                  icon={showSecret ? <IconEyeOpened /> : <IconEyeClosed />}
                  type="tertiary"
                  onClick={() => setShowSecret(!showSecret)}
                  style={{ border: 'none' }}
                />
              }
              type={showSecret ? 'text' : 'password'}
            />
            <Form.Input
              field="verificationToken"
              label="验证Token (可选)"
              placeholder="请输入验证Token"
              helpText="用于验证Webhook请求来源"
              initValue={formData.config?.verificationToken || ''}
            />
            <Form.Input
              field="encryptKey"
              label="加密密钥 (可选)"
              placeholder="请输入加密密钥"
              helpText="用于消息加解密，如果配置了会启用加密模式"
              initValue={formData.config?.encryptKey || ''}
              suffix={
                <Button
                  icon={showSecret ? <IconEyeOpened /> : <IconEyeClosed />}
                  type="tertiary"
                  onClick={() => setShowSecret(!showSecret)}
                  style={{ border: 'none' }}
                />
              }
              type={showSecret ? 'text' : 'password'}
            />
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      title={getChannelTitle()}
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0, maxHeight: '80vh', overflow: 'auto' }}
      maskClosable={false}
      closable={true}
    >
      <div style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px' }}>
          <Title heading={4} style={{ marginBottom: '8px' }}>
            {getChannelTitle()}
          </Title>
          <Text type="secondary">
            {getChannelDescription()}
          </Text>
        </div>

        <Spin spinning={loading}>
          <Form
            layout="vertical"
            style={{ maxWidth: 600 }}
            getFormApi={(api) => formApi.current = api}
          >
            <Card title="基本信息" style={{ marginBottom: '24px' }}>
              <Space vertical spacing="loose" style={{ width: '100%' }}>
                <Form.Select
                  field="type"
                  label="通信平台类型"
                  placeholder="请选择通信平台类型"
                  rules={[{ required: true, message: '请选择通信平台类型' }]}
                  initValue={formData.type}
                  onChange={(value) => setFormData(prev => ({ ...prev, type: value as any, config: {} }))}
                  disabled={!!initialConfig} // 编辑时不允许修改类型
                >
                  <Select.Option value="wechat_work">企业微信</Select.Option>
                  <Select.Option value="dingtalk">钉钉</Select.Option>
                  <Select.Option value="slack">Slack</Select.Option>
                  <Select.Option value="feishu">飞书</Select.Option>
                </Form.Select>

                <Form.Input
                  field="name"
                  label="集成名称"
                  placeholder="请输入集成名称"
                  rules={[{ required: true, message: '请输入集成名称' }]}
                  initValue={formData.name}
                />

                <Form.Input
                  field="description"
                  label="描述"
                  placeholder="请输入集成描述（可选）"
                  initValue={formData.description}
                />

                <Form.InputNumber
                  field="priority"
                  label="优先级"
                  min={1}
                  max={10}
                  placeholder="1-10"
                  helpText="数字越大优先级越高"
                  initValue={formData.priority}
                />

                <Form.Switch
                  field="enabled"
                  label="启用集成"
                  checkedText="启用"
                  uncheckedText="禁用"
                  initValue={formData.enabled}
                />
              </Space>
            </Card>

            <Card title="通道配置" style={{ marginBottom: '24px' }}>
              <Space vertical spacing="loose" style={{ width: '100%' }}>
                {renderChannelSpecificFields()}
              </Space>
            </Card>
          </Form>
        </Spin>

        <Divider />

        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            {onTest && (
              <Button
                icon={<IconPlay />}
                onClick={handleTest}
                loading={testLoading}
                type="tertiary"
              >
                测试连接
              </Button>
            )}
          </Space>

          <Space>
            <Button onClick={onCancel}>
              取消
            </Button>
            <Button type="primary" onClick={handleSubmit} loading={submitting}>
              保存配置
            </Button>
          </Space>
        </Space>
      </div>
    </Modal>
  );
};

export default CommunicationIntegrationForm;
