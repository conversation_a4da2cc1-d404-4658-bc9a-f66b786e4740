/**
 * 集成配置弹窗
 * 用于创建和编辑外部系统集成配置
 */

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Steps,
  Card,
  Space,
  Typography,
  Divider,
  Badge,
  Toast,
  Spin,
  Row,
  Col,
  InputNumber,
  Switch,
  TagInput,
  Collapse,
  Tooltip
} from '@douyinfe/semi-ui';
import {
  IconInfoCircle,
  IconTickCircle,
  IconClose,
  IconPlay,
  IconSetting,
  IconLink,
  IconEyeOpened,
  IconEyeClosed
} from '@douyinfe/semi-icons';
import { integrationService, CreateIntegrationRequest, IntegrationConfig } from '../../services/integration';

const { Title, Text } = Typography;
const { Step } = Steps;

interface IntegrationConfigModalProps {
  visible: boolean;
  integration?: IntegrationConfig | null;
  onCancel: () => void;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  type: string;
  description: string;
  category: string;
  priority: number;
  authType: string;
  authCredentials: Record<string, any>;
  endpoints: Record<string, any>;
  settings: Record<string, any>;
  tags: string[];
  enabled: boolean;
}

const IntegrationConfigModal: React.FC<IntegrationConfigModalProps> = ({
  visible,
  integration,
  onCancel,
  onSuccess
}) => {
  const [form] = useState<FormData>({
    name: '',
    type: '',
    description: '',
    category: '',
    priority: 5,
    authType: 'api_key',
    authCredentials: {},
    endpoints: {},
    settings: {},
    tags: [],
    enabled: false
  });
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
    responseTime?: number;
  } | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    type: '',
    description: '',
    category: '',
    priority: 5,
    authType: 'api_key',
    authCredentials: {},
    endpoints: {},
    settings: {},
    tags: [],
    enabled: false
  });

  const integrationTypes = integrationService.getIntegrationTypes();
  const authTypes = integrationService.getAuthTypes();

  // 初始化表单数据
  useEffect(() => {
    if (visible) {
      if (integration) {
        // 编辑模式
        setFormData({
          name: integration.name,
          type: integration.type,
          description: integration.description || '',
          category: integration.category,
          priority: integration.priority,
          authType: integration.auth?.type || 'api_key',
          authCredentials: integration.auth?.credentials || {},
          endpoints: integration.endpoints || {},
          settings: integration.settings || {},
          tags: integration.tags || [],
          enabled: integration.enabled
        });
        
        // 表单数据已经在上面设置过了
      } else {
        // 新建模式
        setFormData({
          name: '',
          type: '',
          description: '',
          category: '',
          priority: 5,
          authType: 'api_key',
          authCredentials: {},
          endpoints: {},
          settings: {},
          tags: [],
          enabled: false
        });
        // 表单数据已经在上面重置过了
      }
      setCurrentStep(0);
      setTestResult(null);
    }
  }, [visible, integration, form]);

  // 步骤定义
  const steps = [
    {
      title: '基本信息',
      description: '配置集成的基本信息',
      icon: <IconInfoCircle />
    },
    {
      title: '认证配置',
      description: '配置访问凭据',
      icon: <IconSetting />
    },
    {
      title: '端点配置',
      description: '配置API端点',
      icon: <IconLink />
    },
    {
      title: '测试连接',
      description: '验证配置是否正确',
      icon: <IconPlay />
    }
  ];

  // 处理表单字段变化
  const handleFieldChange = (field: string, value: any) => {
    if (field.startsWith('auth_')) {
      const authField = field.replace('auth_', '');
      setFormData(prev => ({
        ...prev,
        authCredentials: {
          ...prev.authCredentials,
          [authField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // 获取当前选择的集成类型配置
  const getSelectedIntegrationType = () => {
    return integrationTypes.find(type => type.value === formData.type);
  };

  // 获取当前选择的认证类型配置
  const getSelectedAuthType = () => {
    return authTypes.find(auth => auth.value === formData.authType);
  };

  // 测试连接
  const testConnection = async () => {
    try {
      setTestLoading(true);
      setTestResult(null);

      // 模拟测试连接（实际应该调用后端API）
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 随机成功/失败（实际应该基于真实测试结果）
      const success = Math.random() > 0.3;
      
      setTestResult({
        success,
        message: success ? '连接测试成功' : '连接测试失败：认证信息无效',
        responseTime: success ? Math.floor(Math.random() * 500) + 100 : undefined
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: '连接测试失败：网络错误'
      });
    } finally {
      setTestLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      // 基本验证
      if (!formData.name || !formData.type || !formData.authType) {
        Toast.error('请填写必填字段');
        return;
      }
      setLoading(true);

      const selectedType = getSelectedIntegrationType();
      if (!selectedType) {
        throw new Error('请选择集成类型');
      }

      // 构建请求数据
      const requestData: CreateIntegrationRequest = {
        name: formData.name,
        type: formData.type,
        description: formData.description,
        category: selectedType.category,
        priority: formData.priority,
        auth: {
          type: formData.authType,
          credentials: formData.authCredentials
        },
        endpoints: formData.endpoints,
        settings: formData.settings,
        tags: formData.tags
      };

      let response;
      if (integration) {
        // 更新集成
        response = await integrationService.updateIntegration(integration.id, {
          ...requestData,
          enabled: formData.enabled
        });
      } else {
        // 创建集成
        response = await integrationService.createIntegration(requestData);
      }

      if (response.success) {
        Toast.success(integration ? '集成配置更新成功' : '集成配置创建成功');
        onSuccess();
      } else {
        throw new Error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('Failed to save integration:', error);
      Toast.error(error instanceof Error ? error.message : '保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 下一步
  const nextStep = async () => {
    try {
      // 验证当前步骤的字段
      const fieldsToValidate = getStepFields(currentStep);
      if (fieldsToValidate.length > 0) {
        // 手动验证必填字段
        const hasError = fieldsToValidate.some(field => {
          if (field.startsWith('auth_')) {
            const authField = field.replace('auth_', '');
            return !formData.authCredentials[authField];
          }
          return !formData[field as keyof FormData];
        });
        
        if (hasError) {
          Toast.error('请填写必填字段');
          return;
        }
      }
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
    } catch (error) {
      // 验证失败，不进入下一步
    }
  };

  // 上一步
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  // 获取当前步骤需要验证的字段
  const getStepFields = (step: number): string[] => {
    switch (step) {
      case 0:
        return ['name', 'type', 'description'];
      case 1:
        const authType = getSelectedAuthType();
        return authType ? authType.fields.filter(f => f.required).map(f => `auth_${f.key}`) : [];
      case 2:
        return ['endpoints'];
      default:
        return [];
    }
  };

  // 渲染基本信息步骤
  const renderBasicInfo = () => (
    <Space vertical style={{ width: '100%' }} spacing={16}>
      <Form.Input
        field="name"
        label="集成名称"
        placeholder="请输入集成名称"
        rules={[{ required: true, message: '请输入集成名称' }]}
        onChange={(value) => handleFieldChange('name', value)}
      />

      <Form.Select
        field="type"
        label="集成类型"
        placeholder="请选择集成类型"
        rules={[{ required: true, message: '请选择集成类型' }]}
        onChange={(value) => handleFieldChange('type', value)}
        disabled={!!integration} // 编辑时不允许修改类型
      >
        {integrationTypes.map(type => (
          <Select.Option key={type.value} value={type.value}>
            <Space>
              <span>{type.icon}</span>
              <div>
                <div>{type.label}</div>
                <Text type="tertiary" size="small">{type.description}</Text>
              </div>
            </Space>
          </Select.Option>
        ))}
      </Form.Select>

      <Form.Input
        field="description"
        label="描述"
        placeholder="请输入集成描述（可选）"
        onChange={(value) => handleFieldChange('description', value)}
      />

      <Row gutter={16}>
        <Col span={12}>
          <Form.InputNumber
            field="priority"
            label="优先级"
            min={0}
            max={10}
            step={1}
            placeholder="0-10"
            suffix="级"
            onChange={(value) => handleFieldChange('priority', value || 5)}
          />
        </Col>
        <Col span={12}>
          <Form.Input
            field="tags"
            label="标签"
            placeholder="添加标签，用逗号分隔"
            onChange={(value) => handleFieldChange('tags', value ? value.split(',').map(t => t.trim()) : [])}
          />
        </Col>
      </Row>

      {integration && (
        <Form.Switch
          field="enabled"
          label="启用集成"
          checkedText="启用"
          uncheckedText="禁用"
          onChange={(checked) => handleFieldChange('enabled', checked)}
        />
      )}
    </Space>
  );

  // 渲染认证配置步骤
  const renderAuthConfig = () => {
    const selectedAuthType = getSelectedAuthType();
    
    return (
      <Space vertical style={{ width: '100%' }} spacing={16}>
        <Form.Select
          field="authType"
          label="认证类型"
          placeholder="请选择认证类型"
          rules={[{ required: true, message: '请选择认证类型' }]}
          onChange={(value) => handleFieldChange('authType', value)}
        >
          {authTypes.map(auth => (
            <Select.Option key={auth.value} value={auth.value}>
              <div>
                <div>{auth.label}</div>
                <Text type="tertiary" size="small">{auth.description}</Text>
              </div>
            </Select.Option>
          ))}
        </Form.Select>

        {selectedAuthType && (
          <Card title="认证信息" bordered={false}>
            <Space vertical style={{ width: '100%' }} spacing="medium">
              {selectedAuthType.fields.map(field => (
                <div key={field.key}>
                  {field.type === 'textarea' ? (
                    <Form.TextArea
                      field={`auth_${field.key}`}
                      label={field.label}
                      placeholder={field.placeholder}
                      rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : []}
                      rows={3}
                      onChange={(value) => handleFieldChange(`auth_${field.key}`, value)}
                    />
                  ) : (
                    <Form.Input
                      field={`auth_${field.key}`}
                      label={field.label}
                      placeholder={field.placeholder}
                      type={field.type === 'password' ? 'password' : 'text'}
                      rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : []}
                      onChange={(value) => handleFieldChange(`auth_${field.key}`, value)}
                      mode={field.type === 'password' ? 'password' : undefined}
                    />
                  )}
                </div>
              ))}
            </Space>
          </Card>
        )}
      </Space>
    );
  };

  // 渲染端点配置步骤
  const renderEndpointConfig = () => (
    <Space vertical style={{ width: '100%' }} spacing={16}>
      <div style={{ 
        padding: '12px 16px', 
        backgroundColor: '#e6f7ff', 
        border: '1px solid #91d5ff', 
        borderRadius: '6px',
        color: '#1890ff'
      }}>
        <div style={{ fontWeight: 500, marginBottom: '4px' }}>端点配置</div>
        <div style={{ fontSize: '13px' }}>根据选择的集成类型，系统会自动配置默认的API端点。您也可以根据需要自定义端点配置。</div>
      </div>

      
      <Collapse defaultActiveKey={['basic']}>
        <Collapse.Panel header="基础端点" itemKey="basic">
          <Space vertical style={{ width: '100%' }}>
            <Text type="tertiary">系统将自动配置以下基础端点：</Text>
            <ul>
              <li>认证端点 (获取访问令牌)</li>
              <li>基础API端点 (核心功能调用)</li>
              <li>健康检查端点 (连接状态检测)</li>
            </ul>
          </Space>
        </Collapse.Panel>
        
        <Collapse.Panel header="高级配置" itemKey="advanced">
          <Space vertical style={{ width: '100%' }}>
            <Form.Input
              field="endpoints"
              label="自定义端点配置 (JSON)"
              placeholder='{"custom_endpoint": {"url": "https://api.example.com/custom", "method": "POST"}}'
              style={{ fontFamily: 'monospace' }}
            />
            <Text type="tertiary" size="small">
              请输入有效的JSON格式。如果不需要自定义端点，可以留空。
            </Text>
          </Space>
        </Collapse.Panel>
      </Collapse>
    </Space>
  );

  // 渲染测试连接步骤
  const renderTestConnection = () => (
    <Space vertical style={{ width: '100%' }} spacing={16} align="center">
      <div style={{ textAlign: 'center' }}>
        <Title heading={4}>连接测试</Title>
        <Text type="tertiary">
          测试集成配置是否正确，确保可以正常连接到外部系统
        </Text>
      </div>

      <Card style={{ width: '100%', textAlign: 'center' }}>
        {!testResult && !testLoading && (
          <Space vertical>
            <IconLink size="large" style={{ color: '#8c8c8c' }} />
            <Text>点击下方按钮开始测试连接</Text>
          </Space>
        )}

        {testLoading && (
          <Space vertical>
            <Spin size="large" />
            <Text>正在测试连接...</Text>
          </Space>
        )}

        {testResult && !testLoading && (
          <Space vertical>
            {testResult.success ? (
              <>
                <IconTickCircle size="large" style={{ color: '#52c41a' }} />
                <Text type="success">{testResult.message}</Text>
                {testResult.responseTime && (
                  <Text type="tertiary">响应时间: {testResult.responseTime}ms</Text>
                )}
              </>
            ) : (
              <>
                <IconClose size="large" style={{ color: '#f5222d' }} />
                <Text type="danger">{testResult.message}</Text>
              </>
            )}
          </Space>
        )}
      </Card>

      <Button
        type="primary"
        icon={<IconPlay />}
        loading={testLoading}
        onClick={testConnection}
        disabled={!formData.name || !formData.type || !formData.authType}
      >
        {testResult ? '重新测试' : '开始测试'}
      </Button>

      {testResult && !testResult.success && (
        <div style={{ 
          padding: '12px 16px', 
          backgroundColor: '#fff2f0', 
          border: '1px solid #ffccc7', 
          borderRadius: '6px',
          color: '#cf1322'
        }}>
          <div style={{ fontWeight: 500, marginBottom: '4px' }}>连接测试失败</div>
          <div style={{ fontSize: '13px' }}>请检查认证配置是否正确，确保网络连接正常，然后重试。</div>
        </div>
      )}
    </Space>
  );

  // 渲染当前步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderBasicInfo();
      case 1:
        return renderAuthConfig();
      case 2:
        return renderEndpointConfig();
      case 3:
        return renderTestConnection();
      default:
        return null;
    }
  };

  return (
    <Modal
      title={integration ? '编辑集成配置' : '新建集成配置'}
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ padding: '24px' }}>
        {/* 步骤指示器 */}
        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>

        {/* 表单内容 */}
        <Form<FormData>
          layout="vertical"
          onSubmit={handleSubmit}
        >
          <div style={{ minHeight: 400 }}>
            {renderStepContent()}
          </div>

          {/* 操作按钮 */}
          <Divider />
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onCancel}>
                取消
              </Button>
              {currentStep > 0 && (
                <Button onClick={prevStep}>
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 ? (
                <Button type="primary" onClick={nextStep}>
                  下一步
                </Button>
              ) : (
                <Button
                  type="primary"
                  loading={loading}
                  onClick={handleSubmit}
                  disabled={!testResult?.success && currentStep === steps.length - 1}
                >
                  {integration ? '更新配置' : '创建集成'}
                </Button>
              )}
            </Space>
          </div>
        </Form>
      </div>
    </Modal>
  );
};

export default IntegrationConfigModal;