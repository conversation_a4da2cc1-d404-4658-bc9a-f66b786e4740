/**
 * 通信平台集成配置弹窗
 * 支持企业微信、钉钉、Slack等通信平台的配置
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Steps,
  Card,
  Space,
  Typography,
  Divider,
  Toast,
  Spin,
  Banner,
  Row,
  Col,
  Switch,
  Collapse
} from '@douyinfe/semi-ui';
import {
  IconInfoCircle,
  IconTickCircle,
  IconClear,
  IconPlay,
  IconSetting,
  IconLink
} from '@douyinfe/semi-icons';
import { communicationService } from '../../services/communication';

const { Title, Text } = Typography;
const { Step } = Steps;

interface CommunicationConfigModalProps {
  visible: boolean;
  integration?: any | null;
  onCancel: () => void;
  onSuccess: () => void;
}

interface FormData {
  platform: 'wechat_work' | 'dingtalk' | 'slack' | '';
  name: string;
  config: Record<string, any>;
  enabled: boolean;
}

const CommunicationConfigModal: React.FC<CommunicationConfigModalProps> = ({
  visible,
  integration,
  onCancel,
  onSuccess
}) => {
  const formApi = useRef<any>();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  
  const [formData, setFormData] = useState<FormData>({
    platform: '',
    name: '',
    config: {},
    enabled: false
  });

  const platformTemplates = communicationService.getPlatformConfigTemplates();

  // 初始化表单数据
  useEffect(() => {
    if (visible) {
      if (integration) {
        // 编辑模式
        const platform = integration.type?.replace('chat_', '') as 'wechat_work' | 'dingtalk' | 'slack';
        setFormData({
          platform,
          name: integration.name,
          config: integration.auth?.credentials || {},
          enabled: integration.enabled
        });
        
        formApi.current?.setValues({
          platform,
          name: integration.name,
          enabled: integration.enabled,
          ...integration.auth?.credentials
        });
      } else {
        // 新建模式
        setFormData({
          platform: '',
          name: '',
          config: {},
          enabled: false
        });
        formApi.current?.reset();
      }
      setCurrentStep(0);
      setTestResult(null);
    }
  }, [visible, integration]);

  // 步骤定义
  const steps = [
    {
      title: '选择平台',
      description: '选择要集成的通信平台',
      icon: <IconInfoCircle />
    },
    {
      title: '配置连接',
      description: '配置平台连接信息',
      icon: <IconSetting />
    },
    {
      title: '测试连接',
      description: '测试配置是否正确',
      icon: <IconPlay />
    }
  ];

  // 处理字段变化
  const handleFieldChange = (field: string, value: any) => {
    if (field === 'platform') {
      setFormData(prev => ({
        ...prev,
        platform: value,
        config: {},
        name: platformTemplates[value]?.name || ''
      }));
      formApi.current?.setValue('name', platformTemplates[value]?.name || '');
    } else if (field === 'name') {
      setFormData(prev => ({ ...prev, name: value }));
    } else if (field === 'enabled') {
      setFormData(prev => ({ ...prev, enabled: value }));
    } else {
      setFormData(prev => ({
        ...prev,
        config: {
          ...prev.config,
          [field]: value
        }
      }));
    }
  };

  // 测试连接
  const testConnection = async () => {
    try {
      setTestLoading(true);
      setTestResult(null);

      // 模拟测试连接
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 随机成功/失败
      const success = Math.random() > 0.3;
      
      setTestResult({
        success,
        message: success ? '连接测试成功，可以正常发送消息' : '连接测试失败：请检查配置信息'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: '连接测试失败：网络错误'
      });
    } finally {
      setTestLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await formApi.current?.validate();
      setLoading(true);

      // 模拟保存操作
      await new Promise(resolve => setTimeout(resolve, 1000));

      Toast.success(integration ? '通信集成更新成功' : '通信集成创建成功');
      onSuccess();
    } catch (error) {
      console.error('Failed to save communication integration:', error);
      Toast.error('保存失败，请检查配置信息');
    } finally {
      setLoading(false);
    }
  };

  // 下一步
  const nextStep = async () => {
    try {
      // 验证当前步骤的字段
      const fieldsToValidate = getStepFields(currentStep);
      if (fieldsToValidate.length > 0 && formApi.current) {
        await formApi.current.validate(fieldsToValidate);
      }
      setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
    } catch (error) {
      // 验证失败，不进入下一步
    }
  };

  // 上一步
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  // 获取当前步骤需要验证的字段
  const getStepFields = (step: number): string[] => {
    switch (step) {
      case 0:
        return ['platform', 'name'];
      case 1:
        const template = platformTemplates[formData.platform];
        return template ? template.fields.filter(f => f.required).map(f => f.key) : [];
      default:
        return [];
    }
  };

  // 渲染平台选择步骤
  const renderPlatformSelection = () => (
    <Space vertical style={{ width: '100%' }} spacing={24}>
      <Form.Select
        field="platform"
        label="通信平台"
        placeholder="请选择通信平台"
        rules={[{ required: true, message: '请选择通信平台' }]}
        onChange={(value) => handleFieldChange('platform', value)}
        disabled={!!integration} // 编辑时不允许修改平台
      >
        {Object.entries(platformTemplates).map(([key, template]) => (
          <Select.Option key={key} value={key}>
            <Space>
              <span>{template.icon}</span>
              <div>
                <div>{template.name}</div>
                <Text type="tertiary" size="small">{template.description}</Text>
              </div>
            </Space>
          </Select.Option>
        ))}
      </Form.Select>

      <Form.Input
        field="name"
        label="集成名称"
        placeholder="请输入集成名称"
        rules={[{ required: true, message: '请输入集成名称' }]}
        onChange={(value) => handleFieldChange('name', value)}
      />

      {formData.platform && (
        <Card title="配置指南" bordered={false} style={{ backgroundColor: '#f8f9fa' }}>
          <div>
            <Text strong>设置步骤：</Text>
            <ol style={{ marginTop: 8, paddingLeft: 20 }}>
              {platformTemplates[formData.platform].setupGuide.map((step, index) => (
                <li key={index} style={{ marginBottom: 4 }}>
                  <Text>{step}</Text>
                </li>
              ))}
            </ol>
          </div>
        </Card>
      )}

      {integration && (
        <Form.Switch
          field="enabled"
          label="启用集成"
          checkedText="启用"
          uncheckedText="禁用"
          onChange={(checked) => handleFieldChange('enabled', checked)}
        />
      )}
    </Space>
  );

  // 渲染配置连接步骤
  const renderConnectionConfig = () => {
    if (!formData.platform) return null;

    const template = platformTemplates[formData.platform];
    
    return (
      <Space vertical style={{ width: '100%' }} spacing={24}>
        <Banner
          fullMode={false}
          type="info"
          icon={<IconInfoCircle />}
          title={`配置 ${template.name} 集成`}
          description="请根据平台文档获取相应的配置信息，确保信息准确无误。"
        />

        <Card title="连接配置" bordered={false}>
          <Space vertical style={{ width: '100%' }} spacing={16}>
            {template.fields.map(field => (
              <div key={field.key}>
                {field.type === 'textarea' ? (
                  <Form.TextArea
                    field={field.key}
                    label={field.label}
                    placeholder={field.placeholder}
                    rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : []}
                    rows={3}
                    onChange={(value) => handleFieldChange(field.key, value)}
                  />
                ) : (
                  <Form.Input
                    field={field.key}
                    label={field.label}
                    placeholder={field.placeholder}
                    type={field.type === 'password' ? 'password' : 'text'}
                    rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : []}
                    onChange={(value) => handleFieldChange(field.key, value)}
                    mode={field.type === 'password' ? 'password' : undefined}
                  />
                )}
                {field.help && (
                  <Text type="tertiary" size="small" style={{ display: 'block', marginTop: 4 }}>
                    {field.help}
                  </Text>
                )}
              </div>
            ))}
          </Space>
        </Card>
      </Space>
    );
  };

  // 渲染测试连接步骤
  const renderTestConnection = () => (
    <Space vertical style={{ width: '100%' }} spacing={24} align="center">
      <div style={{ textAlign: 'center' }}>
        <Title heading={4}>连接测试</Title>
        <Text type="tertiary">
          测试通信平台配置是否正确，确保可以正常发送消息
        </Text>
      </div>

      <Card style={{ width: '100%', textAlign: 'center', minHeight: 200 }}>
        {!testResult && !testLoading && (
          <Space vertical>
            <IconLink size="large" style={{ color: '#8c8c8c' }} />
            <Text>点击下方按钮开始测试连接</Text>
          </Space>
        )}

        {testLoading && (
          <Space vertical>
            <Spin size="large" />
            <Text>正在测试连接...</Text>
          </Space>
        )}

        {testResult && !testLoading && (
          <Space vertical>
            {testResult.success ? (
              <>
                <IconTickCircle size="large" style={{ color: '#52c41a' }} />
                <Text type="success">{testResult.message}</Text>
              </>
            ) : (
              <>
                <IconClear size="large" style={{ color: '#f5222d' }} />
                <Text type="danger">{testResult.message}</Text>
              </>
            )}
          </Space>
        )}
      </Card>

      <Button
        type="primary"
        icon={<IconPlay />}
        loading={testLoading}
        onClick={testConnection}
        disabled={!formData.platform || !formData.name}
      >
        {testResult ? '重新测试' : '开始测试'}
      </Button>

      {testResult && !testResult.success && (
        <Banner
          fullMode={false}
          type="danger"
          icon={<IconClear />}
          title="连接测试失败"
          description="请检查配置信息是否正确，确保网络连接正常，然后重试。"
        />
      )}
    </Space>
  );

  // 渲染当前步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderPlatformSelection();
      case 1:
        return renderConnectionConfig();
      case 2:
        return renderTestConnection();
      default:
        return null;
    }
  };

  return (
    <Modal
      title={integration ? '编辑通信集成' : '新建通信集成'}
      visible={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
      style={{ top: 20 }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ padding: '24px' }}>
        {/* 步骤指示器 */}
        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>

        {/* 表单内容 */}
        <Form
          getFormApi={(api: any) => formApi.current = api}
          layout="vertical"
          onSubmit={handleSubmit}
        >
          <div style={{ minHeight: 400 }}>
            {renderStepContent()}
          </div>

          {/* 操作按钮 */}
          <Divider />
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={onCancel}>
                取消
              </Button>
              {currentStep > 0 && (
                <Button onClick={prevStep}>
                  上一步
                </Button>
              )}
              {currentStep < steps.length - 1 ? (
                <Button type="primary" onClick={nextStep}>
                  下一步
                </Button>
              ) : (
                <Button
                  type="primary"
                  loading={loading}
                  onClick={handleSubmit}
                  disabled={!testResult?.success && currentStep === steps.length - 1}
                >
                  {integration ? '更新配置' : '创建集成'}
                </Button>
              )}
            </Space>
          </div>
        </Form>
      </div>
    </Modal>
  );
};

export default CommunicationConfigModal;