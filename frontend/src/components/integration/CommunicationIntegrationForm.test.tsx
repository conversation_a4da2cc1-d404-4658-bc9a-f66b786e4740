/**
 * 通信集成配置表单测试
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CommunicationIntegrationForm from './CommunicationIntegrationForm';
import type { CommunicationConfig } from './CommunicationIntegrationForm';

// Mock Semi UI components
jest.mock('@douyinfe/semi-ui', () => ({
  Modal: ({ children, visible, title }: any) => 
    visible ? <div data-testid="modal" aria-label={title}>{children}</div> : null,
  Form: ({ children, getFormApi }: any) => {
    const mockApi = {
      validate: jest.fn().mockResolvedValue(true),
      getValues: jest.fn().mockReturnValue({}),
      setValues: jest.fn(),
      reset: jest.fn()
    };
    React.useEffect(() => {
      if (getFormApi) getFormApi(mockApi);
    }, [getFormApi]);
    return <form data-testid="form">{children}</form>;
  },
  'Form.Input': ({ field, label, placeholder }: any) => (
    <input data-testid={`input-${field}`} placeholder={placeholder} aria-label={label} />
  ),
  'Form.Select': ({ field, label, children }: any) => (
    <select data-testid={`select-${field}`} aria-label={label}>{children}</select>
  ),
  'Form.InputNumber': ({ field, label }: any) => (
    <input type="number" data-testid={`number-${field}`} aria-label={label} />
  ),
  'Form.Switch': ({ field, label }: any) => (
    <input type="checkbox" data-testid={`switch-${field}`} aria-label={label} />
  ),
  Select: {
    Option: ({ value, children }: any) => <option value={value}>{children}</option>
  },
  Typography: {
    Title: ({ children }: any) => <h1>{children}</h1>,
    Text: ({ children }: any) => <span>{children}</span>
  },
  Button: ({ children, onClick, icon, loading }: any) => (
    <button onClick={onClick} disabled={loading} data-testid="button">
      {icon} {children}
    </button>
  ),
  Space: ({ children }: any) => <div>{children}</div>,
  Card: ({ children, title }: any) => (
    <div data-testid="card">
      {title && <h2>{title}</h2>}
      {children}
    </div>
  ),
  Divider: () => <hr />,
  Spin: ({ children, spinning }: any) => (
    <div data-testid="spin" data-spinning={spinning}>{children}</div>
  ),
  Toast: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

describe('CommunicationIntegrationForm', () => {
  const mockProps = {
    visible: true,
    onCancel: jest.fn(),
    onSubmit: jest.fn(),
    onTest: jest.fn(),
    loading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('应该正确渲染表单', () => {
    render(<CommunicationIntegrationForm {...mockProps} />);
    
    expect(screen.getByTestId('modal')).toBeInTheDocument();
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('select-type')).toBeInTheDocument();
    expect(screen.getByTestId('input-name')).toBeInTheDocument();
  });

  it('应该在不可见时不渲染', () => {
    render(<CommunicationIntegrationForm {...mockProps} visible={false} />);
    
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
  });

  it('应该正确处理初始配置', () => {
    const initialConfig: CommunicationConfig = {
      id: '1',
      type: 'wechat_work',
      name: '测试企业微信',
      description: '测试描述',
      enabled: true,
      priority: 8,
      config: {
        corpId: 'test-corp-id',
        agentId: 'test-agent-id',
        secret: 'test-secret'
      }
    };

    render(
      <CommunicationIntegrationForm 
        {...mockProps} 
        initialConfig={initialConfig} 
      />
    );
    
    expect(screen.getByTestId('modal')).toBeInTheDocument();
  });

  it('应该显示企业微信特定字段', () => {
    const initialConfig: CommunicationConfig = {
      type: 'wechat_work',
      name: '企业微信集成',
      enabled: true,
      priority: 5,
      config: {}
    };

    render(
      <CommunicationIntegrationForm 
        {...mockProps} 
        initialConfig={initialConfig} 
      />
    );
    
    // 企业微信特定字段应该存在
    expect(screen.getByTestId('input-corpId')).toBeInTheDocument();
    expect(screen.getByTestId('input-agentId')).toBeInTheDocument();
    expect(screen.getByTestId('input-secret')).toBeInTheDocument();
  });

  it('应该显示钉钉特定字段', () => {
    const initialConfig: CommunicationConfig = {
      type: 'dingtalk',
      name: '钉钉集成',
      enabled: true,
      priority: 5,
      config: {}
    };

    render(
      <CommunicationIntegrationForm 
        {...mockProps} 
        initialConfig={initialConfig} 
      />
    );
    
    // 钉钉特定字段应该存在
    expect(screen.getByTestId('input-appKey')).toBeInTheDocument();
    expect(screen.getByTestId('input-appSecret')).toBeInTheDocument();
  });

  it('应该正确处理表单提交', async () => {
    const mockOnSubmit = jest.fn().mockResolvedValue(undefined);
    
    render(
      <CommunicationIntegrationForm 
        {...mockProps} 
        onSubmit={mockOnSubmit}
      />
    );
    
    const submitButton = screen.getByText('保存配置');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalled();
    });
  });

  it('应该正确处理连接测试', async () => {
    const mockOnTest = jest.fn().mockResolvedValue(true);
    
    render(
      <CommunicationIntegrationForm 
        {...mockProps} 
        onTest={mockOnTest}
      />
    );
    
    const testButton = screen.getByText('测试连接');
    fireEvent.click(testButton);
    
    await waitFor(() => {
      expect(mockOnTest).toHaveBeenCalled();
    });
  });

  it('应该在加载时显示加载状态', () => {
    render(<CommunicationIntegrationForm {...mockProps} loading={true} />);
    
    const spinComponent = screen.getByTestId('spin');
    expect(spinComponent).toHaveAttribute('data-spinning', 'true');
  });
});
