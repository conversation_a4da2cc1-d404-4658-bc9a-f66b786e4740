/**
 * 外部系统集成管理页面
 * 提供集成配置、监控和管理功能
 */

import React, { useState, useEffect } from 'react';
import {
  Layout,
  Button,
  Table,
  Space,
  Card,
  Tag,
  Modal,
  Toast,
  Typography,
  Select,
  Input,
  Row,
  Col,
  Badge,
  Progress,
  Spin,
  Empty,
  Tooltip
} from '@douyinfe/semi-ui';
import {
  IconPlus,
  IconRefresh,
  IconDelete,
  IconEdit,
  IconEyeOpened,
  IconActivity,
  IconComment,
  IconLink,
  IconSearch,
  IconFilter
} from '@douyinfe/semi-icons';
import { communicationIntegrationService } from '../../services/communication-integration';
import CommunicationIntegrationForm from '../../components/integration/CommunicationIntegrationForm';
import type { CommunicationConfig } from '../../components/integration/CommunicationIntegrationForm';
import './IntegrationManagePage.css';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

interface IntegrationConfig {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error' | 'testing' | 'configuring';
  category: string;
  enabled: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
  lastHealthCheck?: string;
  healthStatus?: 'healthy' | 'warning' | 'critical';
  description?: string;
  config?: any;
}

interface IntegrationMetrics {
  integrationId: string;
  requests: {
    total: number;
    success: number;
    failed: number;
    avgResponseTime: number;
    maxResponseTime: number;
  };
  availability: number;
  healthStatus: 'healthy' | 'warning' | 'critical';
  lastHealthCheck: string;
}

const IntegrationManagePage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [integrations, setIntegrations] = useState<IntegrationConfig[]>([]);
  const [filteredIntegrations, setFilteredIntegrations] = useState<IntegrationConfig[]>([]);
  const [metrics, setMetrics] = useState<Record<string, IntegrationMetrics>>({});
  
  // 过滤和搜索状态
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  
  // 弹窗状态
  const [showCommunicationModal, setShowCommunicationModal] = useState(false);
  const [showMetricsModal, setShowMetricsModal] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationConfig | null>(null);
  const [editingCommunicationConfig, setEditingCommunicationConfig] = useState<CommunicationConfig | null>(null);

  // 加载集成列表
  const loadIntegrations = async () => {
    try {
      setLoading(true);
      
      // 调用后端API获取集成列表
      const response = await communicationIntegrationService.getAllIntegrations();
      
      // 转换数据格式以适配前端显示
      const transformedIntegrations: IntegrationConfig[] = response.map(item => ({
        id: item.id,
        name: item.name,
        type: item.type,
        status: item.status as any,
        category: 'communication',
        enabled: item.enabled,
        priority: item.priority,
        createdAt: String(item.createdAt),
        updatedAt: String(item.updatedAt),
        lastHealthCheck: item.lastHealthCheck ? String(item.lastHealthCheck) : undefined,
        healthStatus: item.healthStatus as any,
        description: item.description,
        config: item.config
      }));
      
      setIntegrations(transformedIntegrations);
      setFilteredIntegrations(transformedIntegrations);
      
      // 加载每个集成的指标数据
      await loadIntegrationMetrics(transformedIntegrations);
      
    } catch (error) {
      console.error('Failed to load integrations:', error);
      Toast.error('加载集成配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载集成指标
  const loadIntegrationMetrics = async (integrations: IntegrationConfig[]) => {
    try {
      const metricsData: Record<string, IntegrationMetrics> = {};
      
      for (const integration of integrations) {
        try {
          const metrics = await communicationIntegrationService.getIntegrationMetrics(
            integration.id,
            'day',
            1
          );
          
          if (metrics.length > 0) {
            const metric = metrics[0];
            metricsData[integration.id] = {
              integrationId: integration.id,
              requests: {
                total: metric.totalRequests,
                success: metric.successRequests,
                failed: metric.failedRequests,
                avgResponseTime: metric.avgResponseTime,
                maxResponseTime: metric.maxResponseTime
              },
              availability: metric.availability,
              healthStatus: metric.availability > 0.95 ? 'healthy' : 
                           metric.availability > 0.8 ? 'warning' : 'critical',
              lastHealthCheck: String(metric.timestamp)
            };
          } else {
            // 如果没有指标数据，使用默认值
            metricsData[integration.id] = {
              integrationId: integration.id,
              requests: { total: 0, success: 0, failed: 0, avgResponseTime: 0, maxResponseTime: 0 },
              availability: 0,
              healthStatus: 'warning',
              lastHealthCheck: integration.lastHealthCheck || new Date().toISOString()
            };
          }
        } catch (error) {
          console.error(`Failed to load metrics for integration ${integration.id}:`, error);
          // 使用默认值
          metricsData[integration.id] = {
            integrationId: integration.id,
            requests: { total: 0, success: 0, failed: 0, avgResponseTime: 0, maxResponseTime: 0 },
            availability: 0,
            healthStatus: 'critical',
            lastHealthCheck: new Date().toISOString()
          };
        }
      }
      
      setMetrics(metricsData);
    } catch (error) {
      console.error('Failed to load integration metrics:', error);
    }
  };

  // 过滤集成列表
  const filterIntegrations = () => {
    let filtered = integrations;

    // 搜索过滤
    if (searchText) {
      filtered = filtered.filter(integration =>
        integration.name.toLowerCase().includes(searchText.toLowerCase()) ||
        integration.type.toLowerCase().includes(searchText.toLowerCase()) ||
        integration.description?.toLowerCase().includes(searchText.toLowerCase())
      );
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(integration => integration.status === statusFilter);
    }

    // 类型过滤
    if (typeFilter !== 'all') {
      filtered = filtered.filter(integration => integration.type === typeFilter);
    }

    setFilteredIntegrations(filtered);
  };

  // 健康检查
  const performHealthCheck = async (integrationId: string) => {
    try {
      setLoading(true);
      
      // 调用后端API进行健康检查
      const result = await communicationIntegrationService.healthCheck(integrationId);
      
      if (result.healthy) {
        Toast.success('健康检查完成，集成状态正常');
      } else {
        Toast.warning('健康检查完成，集成存在问题');
      }
      
      // 重新加载数据
      await loadIntegrations();
    } catch (error) {
      console.error('Health check failed:', error);
      Toast.error('健康检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理通信集成配置提交
  const handleCommunicationSubmit = async (config: CommunicationConfig) => {
    try {
      setLoading(true);
      
      if (editingCommunicationConfig?.id) {
        // 更新现有配置
        await communicationIntegrationService.updateIntegration(
          editingCommunicationConfig.id,
          {
            name: config.name,
            description: config.description,
            enabled: config.enabled,
            priority: config.priority,
            config: config.config
          }
        );
        Toast.success('通信集成配置更新成功');
      } else {
        // 创建新配置
        await communicationIntegrationService.createIntegration({
          name: config.name,
          type: config.type,
          description: config.description,
          enabled: config.enabled,
          priority: config.priority,
          config: config.config
        });
        Toast.success('通信集成配置创建成功');
      }
      
      setShowCommunicationModal(false);
      setEditingCommunicationConfig(null);
      await loadIntegrations(); // 重新加载列表
    } catch (error) {
      console.error('Failed to save communication integration:', error);
      Toast.error('保存通信集成配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 测试通信集成连接
  const handleCommunicationTest = async (config: CommunicationConfig): Promise<boolean> => {
    try {
      const response = await communicationIntegrationService.testConnection({
        type: config.type,
        config: config.config
      });
      return response.success;
    } catch (error) {
      console.error('Test connection failed:', error);
      return false;
    }
  };

  // 编辑通信集成配置
  const handleEditCommunication = (integration: IntegrationConfig) => {
    // 根据集成类型构建通信配置
    const communicationConfig: CommunicationConfig = {
      id: integration.id,
      type: integration.type as any,
      name: integration.name,
      description: integration.description,
      enabled: integration.enabled,
      priority: integration.priority,
      config: integration.config || {}
    };
    
    setEditingCommunicationConfig(communicationConfig);
    setShowCommunicationModal(true);
  };

  // 删除集成
  const deleteIntegration = async (integrationId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '删除后将无法恢复，确定要删除这个集成配置吗？',
      onOk: async () => {
        try {
          setLoading(true);
          
          // 调用后端API删除集成
          await communicationIntegrationService.deleteIntegration(integrationId);
          
          Toast.success('删除成功');
          await loadIntegrations();
        } catch (error) {
          console.error('Failed to delete integration:', error);
          Toast.error('删除失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 启用/禁用集成
  const toggleIntegration = async (integrationId: string, enabled: boolean) => {
    try {
      setLoading(true);
      
      // 调用后端API切换集成状态
      await communicationIntegrationService.toggleIntegration(integrationId, enabled);
      
      Toast.success(`集成已${enabled ? '启用' : '禁用'}`);
      await loadIntegrations();
    } catch (error) {
      console.error('Failed to toggle integration:', error);
      Toast.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    const colors = {
      active: '#52c41a',
      inactive: '#8c8c8c',
      error: '#f5222d',
      testing: '#fa8c16',
      configuring: '#1890ff'
    };
    return colors[status as keyof typeof colors] || '#8c8c8c';
  };

  // 获取健康状态显示
  const getHealthStatus = (integrationId: string) => {
    const metric = metrics[integrationId];
    if (!metric) return null;

    const { healthStatus, availability } = metric;
    let color = '#8c8c8c';
    let text = '未知';

    switch (healthStatus) {
      case 'healthy':
        color = '#52c41a';
        text = '健康';
        break;
      case 'warning':
        color = '#fa8c16';
        text = '警告';
        break;
      case 'critical':
        color = '#f5222d';
        text = '严重';
        break;
    }

    return (
      <Space>
        <Badge dot style={{ backgroundColor: color }} />
        <Text>{text}</Text>
        <Text type="tertiary">({(availability * 100).toFixed(1)}%)</Text>
      </Space>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text: string, record: IntegrationConfig) => (
        <Space vertical spacing="tight">
          <Text strong>{text}</Text>
          {record.description && (
            <Text type="tertiary" size="small">
              {record.description}
            </Text>
          )}
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => {
        const typeMap = {
          wechat_work: '企业微信',
          dingtalk: '钉钉',
          slack: 'Slack',
          feishu: '飞书'
        };
        return (
          <Tag style={{ backgroundColor: '#1890ff', color: 'white' }}>
            {typeMap[type as keyof typeof typeMap] || type}
          </Tag>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: IntegrationConfig) => (
        <Space vertical spacing="tight">
          <Tag style={{ backgroundColor: getStatusColor(status), color: 'white' }}>
            {status === 'active' ? '运行中' :
             status === 'inactive' ? '已停用' :
             status === 'error' ? '错误' :
             status === 'testing' ? '测试中' : '配置中'}
          </Tag>
          <Tooltip content={record.enabled ? '点击禁用' : '点击启用'}>
            <Button
              size="small"
              type={record.enabled ? 'warning' : 'primary'}
              onClick={() => toggleIntegration(record.id, !record.enabled)}
            >
              {record.enabled ? '禁用' : '启用'}
            </Button>
          </Tooltip>
        </Space>
      )
    },
    {
      title: '健康状态',
      key: 'health',
      width: 150,
      render: (_: any, record: IntegrationConfig) => getHealthStatus(record.id)
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: number) => (
        <Progress 
          percent={priority * 10} 
          showInfo={false} 
          stroke={priority >= 8 ? '#f5222d' : priority >= 5 ? '#fa8c16' : '#52c41a'}
          style={{ width: 60 }}
        />
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: IntegrationConfig) => (
        <Space>
          <Tooltip content="查看详情">
            <Button
              theme="borderless"
              type="primary"
              icon={<IconEyeOpened />}
              size="small"
              onClick={() => {
                setSelectedIntegration(record);
                setShowMetricsModal(true);
              }}
            />
          </Tooltip>
          <Tooltip content="编辑配置">
            <Button
              theme="borderless"
              type="secondary"
              icon={<IconEdit />}
              size="small"
              onClick={() => handleEditCommunication(record)}
            />
          </Tooltip>
          <Tooltip content="健康检查">
            <Button
              theme="borderless"
              type="warning"
              icon={<IconActivity />}
              size="small"
              onClick={() => performHealthCheck(record.id)}
            />
          </Tooltip>
          <Tooltip content="删除集成">
            <Button
              theme="borderless"
              type="danger"
              icon={<IconDelete />}
              size="small"
              onClick={() => deleteIntegration(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  useEffect(() => {
    loadIntegrations();
  }, []);

  useEffect(() => {
    filterIntegrations();
  }, [searchText, statusFilter, typeFilter, integrations]);

  // 获取统计数据
  const getStats = () => {
    const total = integrations.length;
    const active = integrations.filter(i => i.status === 'active').length;
    const healthy = Object.values(metrics).filter(m => m.healthStatus === 'healthy').length;
    const avgAvailability = Object.values(metrics).reduce((sum, m) => sum + m.availability, 0) / Object.values(metrics).length || 0;

    return { total, active, healthy, avgAvailability };
  };

  const stats = getStats();

  return (
    <div className="integration-manage-page">
      <Layout>
        <Header className="page-header">
          <div className="header-content">
            <Title heading={2}>外部系统集成管理</Title>
            <Space>
              <Button
                icon={<IconPlus />}
                type="primary"
                onClick={() => {
                  setEditingCommunicationConfig(null);
                  setShowCommunicationModal(true);
                }}
              >
                添加通信集成
              </Button>
              <Button
                icon={<IconRefresh />}
                onClick={loadIntegrations}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </Header>

        <Content className="page-content">
          {/* 统计卡片 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            <Col xs={24} sm={12} md={6}>
              <Card bodyStyle={{ padding: '24px' }}>
                <div className="stat-card">
                  <div className="stat-number">{stats.total}</div>
                  <div className="stat-label">总集成数</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card bodyStyle={{ padding: '24px' }}>
                <div className="stat-card">
                  <div className="stat-number">{stats.active}</div>
                  <div className="stat-label">运行中</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card bodyStyle={{ padding: '24px' }}>
                <div className="stat-card">
                  <div className="stat-number">{stats.healthy}</div>
                  <div className="stat-label">健康状态</div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card bodyStyle={{ padding: '24px' }}>
                <div className="stat-card">
                  <div className="stat-number">{(stats.avgAvailability * 100).toFixed(1)}%</div>
                  <div className="stat-label">平均可用性</div>
                </div>
              </Card>
            </Col>
          </Row>

          {/* 过滤器 */}
          <Card style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col xs={24} sm={12} md={8}>
                <Input
                  placeholder="搜索集成名称、类型..."
                  value={searchText}
                  onChange={(value) => setSearchText(value as string)}
                  showClear
                  prefix={<IconSearch />}
                />
              </Col>
              <Col xs={24} sm={12} md={4}>
                <Select
                  placeholder="状态"
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value as string)}
                  style={{ width: '100%' }}
                  prefix={<IconFilter />}
                >
                  <Select.Option value="all">全部状态</Select.Option>
                  <Select.Option value="active">运行中</Select.Option>
                  <Select.Option value="inactive">已停用</Select.Option>
                  <Select.Option value="error">错误</Select.Option>
                  <Select.Option value="testing">测试中</Select.Option>
                </Select>
              </Col>
              <Col xs={24} sm={12} md={4}>
                <Select
                  placeholder="类型"
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value as string)}
                  style={{ width: '100%' }}
                  prefix={<IconFilter />}
                >
                  <Select.Option value="all">全部类型</Select.Option>
                  <Select.Option value="wechat_work">企业微信</Select.Option>
                  <Select.Option value="dingtalk">钉钉</Select.Option>
                  <Select.Option value="slack">Slack</Select.Option>
                  <Select.Option value="feishu">飞书</Select.Option>
                </Select>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Space>
                  <Button
                    icon={<IconComment />}
                    onClick={() => setShowMessageModal(true)}
                    disabled={!integrations.some(i => i.status === 'active')}
                  >
                    发送测试消息
                  </Button>
                </Space>
              </Col>
            </Row>
          </Card>

          {/* 集成列表 */}
          <Card>
            {integrations.length === 0 && !loading ? (
              <Empty
                image={<IconLink size="large" style={{ color: '#ccc' }} />}
                description="还没有配置任何集成，点击'添加通信集成'开始配置"
              />
            ) : (
              <Table
                columns={columns}
                dataSource={filteredIntegrations}
                rowKey="id"
                loading={loading}
                scroll={{ x: 1000 }}
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  total: filteredIntegrations.length
                }}
                empty={
                  <Empty
                    image={<IconLink size="large" style={{ color: '#ccc' }} />}
                    description={
                      searchText || statusFilter !== 'all' || typeFilter !== 'all' 
                        ? '没有找到匹配的集成配置' 
                        : '还没有配置任何集成，点击\'添加通信集成\'开始配置'
                    }
                  />
                }
              />
            )}
          </Card>
        </Content>
      </Layout>

      {/* 通信集成配置表单 */}
      <CommunicationIntegrationForm
        visible={showCommunicationModal}
        onCancel={() => {
          setShowCommunicationModal(false);
          setEditingCommunicationConfig(null);
        }}
        onSubmit={handleCommunicationSubmit}
        onTest={handleCommunicationTest}
        initialConfig={editingCommunicationConfig || undefined}
        loading={loading}
      />

      {/* 集成监控详情 */}
      <Modal
        visible={showMetricsModal}
        onCancel={() => setShowMetricsModal(false)}
        title="集成监控详情"
        width={800}
        footer={null}
      >
        {selectedIntegration && (
          <div>
            <Card title="基本信息" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Text strong>集成名称:</Text> {selectedIntegration.name}
                </Col>
                <Col span={12}>
                  <Text strong>集成类型:</Text> {selectedIntegration.type}
                </Col>
                <Col span={12}>
                  <Text strong>状态:</Text> {selectedIntegration.status}
                </Col>
                <Col span={12}>
                  <Text strong>优先级:</Text> {selectedIntegration.priority}
                </Col>
              </Row>
            </Card>
            
            <Card title="监控指标" style={{ marginBottom: 16 }}>
              {metrics[selectedIntegration.id] ? (
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Text strong>总请求数:</Text> {metrics[selectedIntegration.id].requests.total}
                  </Col>
                  <Col span={12}>
                    <Text strong>成功率:</Text> {((metrics[selectedIntegration.id].requests.success / metrics[selectedIntegration.id].requests.total) * 100).toFixed(1)}%
                  </Col>
                  <Col span={12}>
                    <Text strong>平均响应时间:</Text> {metrics[selectedIntegration.id].requests.avgResponseTime}ms
                  </Col>
                  <Col span={12}>
                    <Text strong>可用性:</Text> {(metrics[selectedIntegration.id].availability * 100).toFixed(1)}%
                  </Col>
                </Row>
              ) : (
                <Text type="tertiary">暂无监控数据</Text>
              )}
            </Card>
            
            <Card title="健康状态">
              {getHealthStatus(selectedIntegration.id)}
            </Card>
          </div>
        )}
      </Modal>

      {/* 消息发送表单 */}
      <Modal
        visible={showMessageModal}
        onCancel={() => setShowMessageModal(false)}
        title="发送测试消息"
        width={600}
        footer={
          <Space>
            <Button onClick={() => setShowMessageModal(false)}>取消</Button>
            <Button type="primary">发送</Button>
          </Space>
        }
      >
        <div>
          <p>选择要发送消息的集成:</p>
          <Select
            placeholder="选择集成"
            style={{ width: '100%', marginBottom: 16 }}
          >
            {integrations
              .filter(i => i.status === 'active')
              .map(integration => (
                <Select.Option key={integration.id} value={integration.id}>
                  {integration.name}
                </Select.Option>
              ))
            }
          </Select>
          
          <Input
            placeholder="输入测试消息内容"
            style={{ marginBottom: 16 }}
          />
          
          <Text type="tertiary" size="small">
            消息将通过选中的集成发送到对应的通信平台
          </Text>
        </div>
      </Modal>
    </div>
  );
};

export default IntegrationManagePage;