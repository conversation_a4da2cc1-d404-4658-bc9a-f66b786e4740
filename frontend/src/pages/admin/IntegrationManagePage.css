/* 集成管理页面样式 */
.integration-manage-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.page-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  min-height: 64px;
}

.header-content h2 {
  margin: 0;
  color: #1f2937;
}

.page-content {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.stat-card {
  text-align: center;
  padding: 16px 0;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
  margin-bottom: 8px;
  display: block;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* 过滤器卡片样式 */
.filter-card {
  margin-bottom: 24px;
}

.filter-card .semi-card-body {
  padding: 20px;
}

/* 表格样式优化 */
.integration-table {
  margin-top: 16px;
}

.integration-table .semi-table-tbody .semi-table-row:hover {
  background-color: #f8f9fa;
}

.integration-table .semi-table-thead .semi-table-cell {
  background-color: #fafafa;
  font-weight: 600;
  color: #374151;
}

/* 健康状态指示器 */
.health-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.health-indicator .semi-badge {
  margin-right: 4px;
}

/* 优先级进度条样式 */
.priority-progress {
  width: 60px;
}

.priority-progress .semi-progress-line-outer {
  height: 8px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .semi-button {
  min-width: 32px;
}

/* 状态标签样式 */
.status-tag {
  font-weight: 500;
  border-radius: 6px;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.empty-state .semi-empty-image {
  margin-bottom: 16px;
}

.empty-state .semi-empty-description {
  color: #6b7280;
  font-size: 14px;
}

/* 统计卡片响应式 */
@media (max-width: 1200px) {
  .stat-number {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px 20px;
  }
  
  .header-content .semi-space {
    justify-content: center;
  }
  
  .page-content {
    padding: 16px;
  }
  
  .stat-card {
    padding: 20px 16px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .filter-card .semi-card-body {
    padding: 16px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .semi-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: 12px;
  }
  
  .header-content {
    padding: 12px 16px;
  }
  
  .stat-card {
    padding: 16px 12px;
  }
  
  .stat-number {
    font-size: 20px;
  }
}

/* 表格响应式 */
@media (max-width: 1024px) {
  .integration-table .semi-table {
    font-size: 13px;
  }
  
  .integration-table .semi-table-cell {
    padding: 8px 4px;
  }
}

/* 加载状态样式 */
.loading-overlay {
  position: relative;
  min-height: 200px;
}

.loading-overlay .semi-spin {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 弹窗样式优化 */
.modal-content {
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding-top: 16px;
  margin-top: 16px;
}

/* 表单样式 */
.form-section {
  margin-bottom: 24px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section .semi-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.form-section .semi-card-head {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 12px 16px;
}

.form-section .semi-card-body {
  padding: 16px;
}

/* 搜索框样式 */
.search-input {
  position: relative;
}

.search-input .semi-input-prefix {
  color: #9ca3af;
}

.search-input .semi-input {
  border-radius: 8px;
}

/* 过滤器样式 */
.filter-select {
  border-radius: 8px;
}

.filter-select .semi-select-selection {
  border-radius: 8px;
}

/* 按钮样式统一 */
.primary-button {
  border-radius: 6px;
  font-weight: 500;
}

.secondary-button {
  border-radius: 6px;
  font-weight: 500;
}

/* 卡片阴影效果 */
.stat-card-container {
  transition: all 0.3s ease;
}

.stat-card-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 表格行悬停效果 */
.integration-table .semi-table-tbody .semi-table-row {
  transition: background-color 0.2s ease;
}

.integration-table .semi-table-tbody .semi-table-row:hover {
  background-color: #f0f9ff;
}

/* 状态标签悬停效果 */
.status-tag {
  transition: all 0.2s ease;
}

.status-tag:hover {
  transform: scale(1.05);
}

/* 进度条动画 */
.priority-progress .semi-progress-line-inner {
  transition: width 0.6s ease;
}

/* 响应式网格 */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

@media (max-width: 640px) {
  .responsive-grid {
    grid-template-columns: 1fr;
  }
}