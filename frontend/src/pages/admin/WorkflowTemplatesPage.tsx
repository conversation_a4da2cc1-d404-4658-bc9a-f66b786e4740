/**
 * 工作流模板库页面
 * 提供工作流模板的浏览、搜索、预览和应用功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Layout, 
  Input, 
  Button, 
  Card, 
  Tag, 
  Select, 
  Pagination,
  Modal,
  Toast,
  Tabs,
  TabPane,
  Empty,
  Spin,
  Space,
  Row,
  Col,
  Badge,
  Popover,
  Rating,
  Typography
} from '@douyinfe/semi-ui';
import {
  IconSearch,
  IconPlus,
  IconDownload,
  IconUpload,
  IconEyeOpened,
  IconPlay,
  IconStar,
  IconUser,
  IconApps,
  IconFilter,
  IconRefresh,
  IconClock
} from '@douyinfe/semi-icons';
import { workflowTemplateService, type WorkflowTemplate, type TemplateCategory } from '../../services/workflow-template';
import { workflowService } from '../../services/workflow';
import type { WorkflowCategory } from '../../types/workflow';

const { Header, Sider, Content } = Layout;
const { Text, Title } = Typography;

export interface WorkflowTemplatesPageProps {
  onTemplateApply?: (templateId: string, workflow: any) => void;
  mode?: 'browse' | 'select'; // 浏览模式或选择模式
}

const WorkflowTemplatesPage: React.FC<WorkflowTemplatesPageProps> = ({
  onTemplateApply,
  mode = 'browse'
}) => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [categories, setCategories] = useState<TemplateCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<WorkflowCategory | undefined>();
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | undefined>();
  const [searchQuery, setSearchQuery] = useState('');
  const [difficulty, setDifficulty] = useState<string | undefined>();
  const [sortBy, setSortBy] = useState<'rating' | 'usage' | 'recent' | 'name'>('rating');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showPopular, setShowPopular] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [total, setTotal] = useState(0);
  const [activeTab, setActiveTab] = useState('all');

  // 模态框状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [applyVisible, setApplyVisible] = useState(false);
  const [importVisible, setImportVisible] = useState(false);

  // 侧边栏状态
  const [siderCollapsed, setSiderCollapsed] = useState(false);

  /**
   * 加载模板分类
   */
  const loadCategories = useCallback(async () => {
    try {
      const data = await workflowTemplateService.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('加载模板分类失败:', error);
      Toast.error('加载模板分类失败');
    }
  }, []);

  /**
   * 加载模板列表
   */
  const loadTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const response = await workflowTemplateService.getTemplates({
        category: selectedCategory,
        subcategory: selectedSubcategory,
        search: searchQuery,
        difficulty: difficulty as any,
        popular: showPopular,
        sort: sortBy,
        order: sortOrder,
        limit: pageSize,
        offset: (currentPage - 1) * pageSize
      });

      setTemplates(response.templates);
      setTotal(response.pagination.total);
    } catch (error) {
      console.error('加载模板列表失败:', error);
      Toast.error('加载模板列表失败');
    } finally {
      setLoading(false);
    }
  }, [
    selectedCategory,
    selectedSubcategory,
    searchQuery,
    difficulty,
    showPopular,
    sortBy,
    sortOrder,
    currentPage,
    pageSize
  ]);

  /**
   * 加载热门模板
   */
  const loadPopularTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const data = await workflowTemplateService.getPopularTemplates(pageSize);
      setTemplates(data);
      setTotal(data.length);
    } catch (error) {
      console.error('加载热门模板失败:', error);
      Toast.error('加载热门模板失败');
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  /**
   * 加载最新模板
   */
  const loadRecentTemplates = useCallback(async () => {
    setLoading(true);
    try {
      const data = await workflowTemplateService.getRecentTemplates(pageSize);
      setTemplates(data);
      setTotal(data.length);
    } catch (error) {
      console.error('加载最新模板失败:', error);
      Toast.error('加载最新模板失败');
    } finally {
      setLoading(false);
    }
  }, [pageSize]);

  // 初始化加载
  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  // 根据选项卡加载不同内容
  useEffect(() => {
    switch (activeTab) {
      case 'popular':
        loadPopularTemplates();
        break;
      case 'recent':
        loadRecentTemplates();
        break;
      case 'all':
      default:
        loadTemplates();
        break;
    }
  }, [activeTab, loadTemplates, loadPopularTemplates, loadRecentTemplates]);

  /**
   * 搜索模板
   */
  const handleSearch = useCallback((value: string) => {
    setSearchQuery(value);
    setCurrentPage(1);
  }, []);

  /**
   * 重置筛选条件
   */
  const handleReset = useCallback(() => {
    setSelectedCategory(undefined);
    setSelectedSubcategory(undefined);
    setSearchQuery('');
    setDifficulty(undefined);
    setShowPopular(false);
    setCurrentPage(1);
  }, []);

  /**
   * 预览模板
   */
  const handlePreview = useCallback(async (template: WorkflowTemplate) => {
    try {
      setLoading(true);
      const fullTemplate = await workflowTemplateService.getTemplate(template.id);
      setSelectedTemplate(fullTemplate);
      setPreviewVisible(true);
    } catch (error) {
      console.error('加载模板详情失败:', error);
      Toast.error('加载模板详情失败');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 应用模板
   */
  const handleApply = useCallback(async (template: WorkflowTemplate) => {
    try {
      if (onTemplateApply) {
        // 在选择模式下，直接回调
        onTemplateApply(template.id, template.workflow);
      } else {
        // 在浏览模式下，直接导航到设计器并传递模板ID
        Toast.success('正在从模板创建工作流...');
        const url = `/admin/workflow-designer?templateId=${template.id}&mode=template`;
        window.open(url, '_blank');
      }
    } catch (error) {
      console.error('应用模板失败:', error);
      Toast.error('应用模板失败');
    }
  }, [onTemplateApply]);

  /**
   * 下载模板
   */
  const handleDownload = useCallback(async (template: WorkflowTemplate) => {
    try {
      await workflowTemplateService.downloadTemplate(template.id, template.name);
      Toast.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      Toast.error('下载模板失败');
    }
  }, []);

  /**
   * 模板评分
   */
  const handleRate = useCallback(async (template: WorkflowTemplate, rating: number) => {
    try {
      await workflowTemplateService.rateTemplate(template.id, { rating });
      Toast.success('评分成功');
      // 重新加载模板列表
      switch (activeTab) {
        case 'popular':
          loadPopularTemplates();
          break;
        case 'recent':
          loadRecentTemplates();
          break;
        default:
          loadTemplates();
          break;
      }
    } catch (error) {
      console.error('评分失败:', error);
      Toast.error('评分失败');
    }
  }, [activeTab, loadTemplates, loadPopularTemplates, loadRecentTemplates]);

  /**
   * 导入模板
   */
  const handleImport = useCallback((file: File) => {
    workflowTemplateService.importTemplateFromFile(file)
      .then(() => {
        Toast.success('模板导入成功');
        setImportVisible(false);
        loadTemplates();
      })
      .catch((error) => {
        console.error('导入模板失败:', error);
        Toast.error('导入模板失败');
      });
  }, [loadTemplates]);

  /**
   * 获取当前分类的子分类
   */
  const getCurrentSubcategories = useCallback(() => {
    if (!selectedCategory) return [];
    const category = categories.find(cat => cat.id === selectedCategory);
    return category?.subcategories || [];
  }, [selectedCategory, categories]);

  /**
   * 渲染模板卡片
   */
  const renderTemplateCard = (template: WorkflowTemplate) => {
    return (
      <Card
        key={template.id}
        style={{ height: '100%' }}
        cover={
          <div 
            style={{ 
              height: 180, 
              backgroundImage: `url(${template.preview.thumbnail})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundColor: '#f5f5f5'
            }}
          >
            <div style={{ 
              position: 'absolute', 
              top: 8, 
              right: 8,
              display: 'flex',
              gap: 4
            }}>
              {template.isPopular && (
                <Badge count="热门" style={{ backgroundColor: '#ff4d4f' }} />
              )}
              {template.isBuiltin && (
                <Badge count="官方" style={{ backgroundColor: '#1890ff' }} />
              )}
            </div>
            <div style={{ 
              position: 'absolute', 
              bottom: 8, 
              left: 8
            }}>
              <Tag 
                color={workflowTemplateService.getDifficultyColor(template.difficulty) as any}
                size="small"
              >
                {workflowTemplateService.getDifficultyLabel(template.difficulty)}
              </Tag>
            </div>
          </div>
        }
        bodyStyle={{ padding: '16px' }}
        actions={[
          <Button 
            key="preview"
            icon={<IconEyeOpened />} 
            size="small"
            type="tertiary"
            onClick={() => handlePreview(template)}
          >
            预览
          </Button>,
          <Button 
            key="apply"
            icon={<IconPlay />} 
            size="small"
            type="primary"
            theme="solid"
            onClick={() => handleApply(template)}
          >
            {mode === 'select' ? '选择' : '应用'}
          </Button>,
          <Popover
            key="more"
            trigger="click"
            position="top"
            content={
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                <Button 
                  icon={<IconDownload />} 
                  size="small"
                  type="tertiary"
                  onClick={() => handleDownload(template)}
                >
                  下载
                </Button>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Text style={{ fontSize: 12 }}>评分:</Text>
                  <Rating 
                    value={template.rating}
                    size={14}
                    onChange={(rating) => handleRate(template, rating)}
                  />
                </div>
              </div>
            }
          >
            <Button icon={<IconApps />} size="small" type="tertiary" />
          </Popover>
        ]}
      >
        <Card.Meta
          title={
            <div>
              <Text strong style={{ fontSize: 14 }}>
                {template.name}
              </Text>
              <div style={{ marginTop: 4 }}>
                <Rating value={template.rating} disabled size={12} />
                <Text type="tertiary" style={{ fontSize: 11, marginLeft: 8 }}>
                  ({template.usageCount} 次使用)
                </Text>
              </div>
            </div>
          }
          description={
            <div>
              <Text 
                type="tertiary" 
                style={{ 
                  fontSize: 12, 
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  WebkitLineClamp: 2,
                  overflow: 'hidden'
                }}
              >
                {template.description}
              </Text>
              <div style={{ marginTop: 8, display: 'flex', alignItems: 'center', gap: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <IconClock size="small" />
                  <Text style={{ fontSize: 11 }}>
                    {workflowTemplateService.formatEstimatedDuration(template.estimatedDuration)}
                  </Text>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <IconUser size="small" />
                  <Text style={{ fontSize: 11 }}>{template.author}</Text>
                </div>
              </div>
              <div style={{ marginTop: 8 }}>
                {template.tags.slice(0, 3).map(tag => (
                  <Tag key={tag} size="small" style={{ marginRight: 4, marginBottom: 4 }}>
                    {tag}
                  </Tag>
                ))}
                {template.tags.length > 3 && (
                  <Text type="tertiary" style={{ fontSize: 11 }}>
                    +{template.tags.length - 3} 更多
                  </Text>
                )}
              </div>
            </div>
          }
        />
      </Card>
    );
  };

  /**
   * 渲染侧边栏
   */
  const renderSider = () => (
    <Sider
      style={{ 
        backgroundColor: 'var(--semi-color-bg-1)',
        borderRight: '1px solid var(--semi-color-border)',
        width: siderCollapsed ? 80 : 280
      }}
    >
      <div style={{ padding: '16px' }}>
        {siderCollapsed ? (
          <div style={{ textAlign: 'center' }}>
            <Button 
              icon={<IconFilter />}
              size="small"
              type="tertiary"
              onClick={() => setSiderCollapsed(false)}
              style={{ padding: '8px' }}
            />
          </div>
        ) : (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <Title heading={6} style={{ margin: 0 }}>
                筛选条件
              </Title>
              <Button 
                icon={<IconFilter />}
                size="small"
                type="tertiary"
                onClick={() => setSiderCollapsed(true)}
                style={{ padding: '4px' }}
              />
            </div>
        
        {/* 分类筛选 */}
        <div style={{ marginBottom: '16px' }}>
          <Text style={{ fontSize: '13px', fontWeight: 500, marginBottom: '8px', display: 'block' }}>
            模板分类
          </Text>
          <Select
            value={selectedCategory}
            onChange={(value) => setSelectedCategory(value as WorkflowCategory)}
            placeholder="选择分类"
            style={{ width: '100%' }}
            optionList={categories.map(cat => ({
              value: cat.id,
              label: `${cat.icon} ${cat.name}`
            }))}
          />
        </div>

        {/* 子分类筛选 */}
        {selectedCategory && getCurrentSubcategories().length > 0 && (
          <div style={{ marginBottom: '16px' }}>
            <Text style={{ fontSize: '13px', fontWeight: 500, marginBottom: '8px', display: 'block' }}>
              子分类
            </Text>
            <Select
              value={selectedSubcategory}
              onChange={(value) => setSelectedSubcategory(value as string)}
              placeholder="选择子分类"
              style={{ width: '100%' }}
              optionList={getCurrentSubcategories().map(sub => ({
                value: sub.id,
                label: sub.name
              }))}
            />
          </div>
        )}

        {/* 难度筛选 */}
        <div style={{ marginBottom: '16px' }}>
          <Text style={{ fontSize: '13px', fontWeight: 500, marginBottom: '8px', display: 'block' }}>
            难度等级
          </Text>
          <Select
            value={difficulty}
            onChange={(value) => setDifficulty(value as string)}
            placeholder="选择难度"
            style={{ width: '100%' }}
            optionList={[
              { value: 'beginner', label: '初级' },
              { value: 'intermediate', label: '中级' },
              { value: 'advanced', label: '高级' }
            ]}
          />
        </div>

        {/* 排序选择 */}
        <div style={{ marginBottom: '16px' }}>
          <Text style={{ fontSize: '13px', fontWeight: 500, marginBottom: '8px', display: 'block' }}>
            排序方式
          </Text>
          <Select
            value={`${sortBy}_${sortOrder}`}
            onChange={(value) => {
              if (typeof value === 'string') {
                const [sort, order] = value.split('_');
                setSortBy(sort as any);
                setSortOrder(order as any);
              }
            }}
            style={{ width: '100%' }}
            optionList={[
              { value: 'rating_desc', label: '评分降序' },
              { value: 'usage_desc', label: '使用次数降序' },
              { value: 'recent_desc', label: '最新更新' },
              { value: 'name_asc', label: '名称升序' }
            ]}
          />
        </div>

        <Button
          icon={<IconRefresh />}
          onClick={handleReset}
          style={{ width: '100%' }}
          type="tertiary"
        >
          重置筛选
        </Button>
          </>
        )}
      </div>
    </Sider>
  );

  return (
    <div style={{ height: '100vh', background: 'var(--semi-color-bg-0)' }}>
      <Layout style={{ height: '100%' }}>
        {/* 侧边栏 */}
        {renderSider()}

        {/* 主内容区 */}
        <Layout>
          {/* 头部 */}
          <Header style={{ 
            backgroundColor: 'var(--semi-color-bg-1)', 
            borderBottom: '1px solid var(--semi-color-border)',
            padding: '0 24px'
          }}>
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'space-between',
              height: '100%'
            }}>
              <div>
                <Title heading={4} style={{ margin: 0 }}>
                  工作流模板库
                </Title>
                <Text type="tertiary" style={{ fontSize: '13px' }}>
                  浏览和使用预置的工作流模板
                </Text>
              </div>

              <Space>
                <Button
                  icon={<IconUpload />}
                  onClick={() => setImportVisible(true)}
                >
                  导入模板
                </Button>
                <Button
                  icon={<IconPlus />}
                  theme="solid"
                  type="primary"
                  onClick={() => {
                    // 导航到创建模板页面或打开创建模态框
                    window.open('/admin/workflows/designer?mode=template', '_blank');
                  }}
                >
                  创建模板
                </Button>
              </Space>
            </div>
          </Header>

          {/* 内容区 */}
          <Content style={{ padding: '24px', overflow: 'auto' }}>
            {/* 搜索栏 */}
            <div style={{ marginBottom: '24px' }}>
              <Row gutter={16}>
                <Col span={16}>
                  <Input
                    prefix={<IconSearch />}
                    placeholder="搜索模板名称、描述或标签..."
                    value={searchQuery}
                    onChange={handleSearch}
                    style={{ width: '100%' }}
                  />
                </Col>
                <Col span={8}>
                  <div style={{ display: 'flex', gap: 8 }}>
                    <Text type="tertiary" style={{ lineHeight: '32px', fontSize: '13px' }}>
                      共 {total} 个模板
                    </Text>
                  </div>
                </Col>
              </Row>
            </div>

            {/* 标签页 */}
            <Tabs 
              activeKey={activeTab} 
              onChange={setActiveTab}
              style={{ marginBottom: '16px' }}
            >
              <TabPane tab="全部模板" itemKey="all" />
              <TabPane tab="热门推荐" itemKey="popular" />
              <TabPane tab="最新更新" itemKey="recent" />
            </Tabs>

            {/* 模板网格 */}
            <Spin spinning={loading}>
              {templates.length > 0 ? (
                <Row gutter={[16, 16]}>
                  {templates.map(template => (
                    <Col key={template.id} span={6}>
                      {renderTemplateCard(template)}
                    </Col>
                  ))}
                </Row>
              ) : (
                <Empty
                  title="暂无模板"
                  description="没有找到符合条件的模板"
                  image={<IconApps style={{ fontSize: '48px', color: '#ccc' }} />}
                />
              )}
            </Spin>

            {/* 分页 */}
            {templates.length > 0 && activeTab === 'all' && (
              <div style={{ marginTop: '32px', textAlign: 'center' }}>
                <Pagination
                  currentPage={currentPage}
                  pageSize={pageSize}
                  total={total}
                  onPageChange={setCurrentPage}
                  onPageSizeChange={setPageSize}
                  showSizeChanger
                  showQuickJumper
                  showTotal
                />
              </div>
            )}
          </Content>
        </Layout>
      </Layout>

      {/* 模板预览对话框 */}
      <Modal
        title="模板预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={1200}
        height="80vh"
        footer={
          <Space>
            <Button onClick={() => setPreviewVisible(false)}>
              关闭
            </Button>
            {selectedTemplate && (
              <Button 
                type="primary"
                theme="solid"
                onClick={() => {
                  handleApply(selectedTemplate);
                  setPreviewVisible(false);
                }}
              >
                应用模板
              </Button>
            )}
          </Space>
        }
      >
        {selectedTemplate && (
          <div>
            {/* 模板信息 */}
            <Card style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', gap: '16px' }}>
                <img 
                  src={selectedTemplate.preview.thumbnail}
                  alt={selectedTemplate.name}
                  style={{ width: 120, height: 80, objectFit: 'cover', borderRadius: 6 }}
                />
                <div style={{ flex: 1 }}>
                  <Title heading={5} style={{ margin: '0 0 8px 0' }}>
                    {selectedTemplate.name}
                  </Title>
                  <Text type="tertiary">{selectedTemplate.description}</Text>
                  <div style={{ marginTop: 8, display: 'flex', gap: 16, alignItems: 'center' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                      <Rating value={selectedTemplate.rating} disabled size={14} />
                      <Text style={{ fontSize: 12 }}>({selectedTemplate.usageCount} 使用)</Text>
                    </div>
                    <Tag color={workflowTemplateService.getDifficultyColor(selectedTemplate.difficulty) as any}>
                      {workflowTemplateService.getDifficultyLabel(selectedTemplate.difficulty)}
                    </Tag>
                    <Text type="tertiary" style={{ fontSize: 12 }}>
                      预计时长: {workflowTemplateService.formatEstimatedDuration(selectedTemplate.estimatedDuration)}
                    </Text>
                  </div>
                </div>
              </div>
            </Card>

            {/* 工作流步骤预览 */}
            {selectedTemplate.workflow && (
              <Card title="工作流步骤">
                <div>
                  {selectedTemplate.workflow.steps.map((step, index) => (
                    <div 
                      key={index}
                      style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        padding: '12px 0',
                        borderBottom: index < selectedTemplate.workflow!.steps.length - 1 ? '1px solid var(--semi-color-border)' : 'none'
                      }}
                    >
                      <div style={{ 
                        minWidth: '32px', 
                        height: '32px', 
                        borderRadius: '50%', 
                        backgroundColor: 'var(--semi-color-primary)',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '14px',
                        marginRight: '16px'
                      }}>
                        {index + 1}
                      </div>
                      <div style={{ flex: 1 }}>
                        <Text strong>{step.name}</Text>
                        <div>
                          <Text type="tertiary" style={{ fontSize: '12px' }}>
                            {step.description || `执行 ${step.type} 操作`}
                          </Text>
                        </div>
                      </div>
                      <Tag size="small">{step.type}</Tag>
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>
        )}
      </Modal>

      {/* 导入模板对话框 */}
      <Modal
        title="导入模板"
        visible={importVisible}
        onCancel={() => setImportVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ textAlign: 'center', padding: '32px' }}>
          <input
            type="file"
            accept=".json"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                handleImport(file);
              }
            }}
            style={{ display: 'none' }}
            id="template-import-input"
          />
          <label htmlFor="template-import-input">
            <Button icon={<IconUpload />} size="large">
              选择模板文件
            </Button>
          </label>
          <div style={{ marginTop: '16px' }}>
            <Text type="tertiary">
              请选择 .json 格式的工作流模板文件
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default WorkflowTemplatesPage;