/**
 * 工作流设计器页面样式
 */

/* 页面主容器 */
.workflow-designer-page {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background: var(--semi-color-bg-0);
}

/* 加载状态 */
.workflow-designer-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  gap: 16px;
  
  p {
    color: var(--semi-color-text-1);
    font-size: 16px;
  }
}

/* 布局组件 */
.workflow-designer-layout {
  height: 100%;
  
  .semi-layout-header {
    background: var(--semi-color-bg-1);
    border-bottom: 1px solid var(--semi-color-border);
    padding: 0 24px;
    height: 64px;
    line-height: 64px;
  }
  
  .semi-layout-sider {
    background: var(--semi-color-bg-1);
    border-right: 1px solid var(--semi-color-border);
    
    &:last-child {
      border-right: none;
      border-left: 1px solid var(--semi-color-border);
    }
  }
  
  .semi-layout-content {
    background: var(--semi-color-bg-0);
    overflow: hidden;
  }
}

/* 头部工具栏 */
.workflow-designer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .workflow-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--semi-color-text-0);
      display: flex;
      align-items: center;
      gap: 4px;
      
      .modified-indicator {
        color: var(--semi-color-warning);
        font-size: 20px;
        line-height: 1;
      }
    }
    
    .workflow-status {
      display: flex;
      gap: 8px;
      
      .validation-error {
        background: var(--semi-color-danger-light-default);
        color: var(--semi-color-danger);
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
      
      .readonly-indicator {
        background: var(--semi-color-fill-1);
        color: var(--semi-color-text-2);
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
  
  .header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

/* 侧边栏面板 */
.workflow-designer-left-panel,
.workflow-designer-right-panel {
  .semi-layout-sider-children {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

/* 主要内容区域 */
.workflow-designer-content {
  position: relative;
  height: calc(100vh - 64px);
}

/* 预览对话框 */
.workflow-preview-modal {
  .semi-modal-body {
    padding: 0;
    height: 750px;
    overflow: hidden;
  }
}

/* 预览面板和调试面板 */
.workflow-preview-panel,
.workflow-debug-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--semi-color-bg-1);
}

/* 执行状态指示器 */
.execution-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  
  &.running {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }
  
  &.completed {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
  }
  
  &.failed {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
  
  &.pending {
    background-color: #f0f0f0;
    color: #666;
    border: 1px solid #d9d9d9;
  }
}

/* 变量树和日志条目样式 */
.variable-tree-item {
  padding: 8px 12px;
  border: 1px solid var(--semi-color-border);
  border-radius: 6px;
  margin-bottom: 8px;
  background: var(--semi-color-bg-0);
  
  .variable-name {
    font-family: monospace;
    font-weight: 500;
    font-size: 13px;
    color: var(--semi-color-text-0);
  }
  
  .variable-value {
    margin-top: 4px;
    font-family: monospace;
    font-size: 12px;
    
    &.string { color: #52c41a; }
    &.number { color: #1890ff; }
    &.boolean { color: #fa8c16; }
    &.object { 
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 3px;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 100px;
      overflow: auto;
    }
  }
}

.log-entry {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid;
  
  &.info {
    background: #f0f9ff;
    border-left-color: #1890ff;
    
    .log-icon { color: #1890ff; }
  }
  
  &.warning {
    background: #fffbe6;
    border-left-color: #faad14;
    
    .log-icon { color: #faad14; }
  }
  
  &.error {
    background: #fff2f0;
    border-left-color: #ff4d4f;
    
    .log-icon { color: #ff4d4f; }
  }
  
  &.debug {
    background: #f6ffed;
    border-left-color: #52c41a;
    
    .log-icon { color: #52c41a; }
  }
  
  .log-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    
    .log-message {
      font-weight: 500;
      font-size: 13px;
    }
    
    .log-timestamp {
      font-size: 11px;
      color: var(--semi-color-text-2);
      margin-left: auto;
    }
  }
  
  .log-details {
    font-family: monospace;
    font-size: 11px;
    background: rgba(0, 0, 0, 0.02);
    padding: 8px;
    border-radius: 3px;
    margin-top: 8px;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

/* 工作流管理对话框 */
.workflow-manager-modal {
  .semi-modal-body {
    padding: 16px;
    height: 650px;
    overflow: hidden;
  }
}

/* 快捷键提示 */
.keyboard-shortcuts-hint {
  position: fixed;
  bottom: 16px;
  right: 16px;
  z-index: 1000;
  opacity: 0.8;
  
  .shortcuts {
    display: flex;
    gap: 8px;
    background: var(--semi-color-bg-2);
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: var(--semi-shadow-elevated);
    font-size: 12px;
    
    span {
      color: var(--semi-color-text-2);
      padding: 2px 6px;
      background: var(--semi-color-fill-0);
      border-radius: 3px;
      font-family: monospace;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .workflow-designer-left-panel,
  .workflow-designer-right-panel {
    width: 240px !important;
  }
}

@media (max-width: 768px) {
  .workflow-designer-page {
    .header-left .workflow-title {
      font-size: 16px;
    }
    
    .header-actions {
      gap: 4px;
      
      .semi-button {
        padding: 0 8px;
        
        .semi-button-content {
          font-size: 12px;
        }
      }
    }
  }
  
  .keyboard-shortcuts-hint {
    display: none;
  }
}

/* 暗色主题适配 */
[theme-mode="dark"] {
  .workflow-designer-page {
    background: var(--semi-color-bg-0);
  }
  
  .workflow-designer-layout {
    .semi-layout-header {
      background: var(--semi-color-bg-1);
      border-bottom-color: var(--semi-color-border);
    }
    
    .semi-layout-sider {
      background: var(--semi-color-bg-1);
      border-color: var(--semi-color-border);
    }
    
    .semi-layout-content {
      background: var(--semi-color-bg-0);
    }
  }
}