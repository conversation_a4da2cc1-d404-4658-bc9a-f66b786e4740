/**
 * 工作流设计器页面
 * 提供可视化的工作流设计和编辑功能
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Button, Space, Modal, Toast, Spin, Divider, SideSheet } from '@douyinfe/semi-ui';
import { 
  IconSave, 
  IconUpload, 
  IconDownload, 
  IconPlay, 
  IconSetting,
  IconEyeOpened,
  IconUndo,
  IconRedo,
  IconStop,
  IconCode,
  IconAlertTriangle
} from '@douyinfe/semi-icons';
import { useWorkflowDesigner } from '../../hooks/useWorkflowDesigner';
import { workflowTemplateService } from '../../services/workflow-template';
import WorkflowDesignerCore from '../../components/workflow/WorkflowDesignerCore';
import WorkflowPropertiesPanel from '../../components/workflow/WorkflowPropertiesPanel';
import WorkflowPreviewPanel from '../../components/workflow/WorkflowPreviewPanel';
import WorkflowDebugPanel from '../../components/workflow/WorkflowDebugPanel';
import TemplateParameterConfigModal from '../../components/workflow/TemplateParameterConfigModal';
import { workflowSimulator } from '../../services/workflow-simulator';
import type { WorkflowDefinition, CanvasSelection, CanvasEvent, WorkflowExecution, DesignerNode, DesignerConnection } from '../../types/workflow';
import './WorkflowDesignerPage.css';

const { Header, Content, Sider } = Layout;

export interface WorkflowDesignerPageProps {
  workflowId?: string; // 编辑模式时传入
  templateId?: string; // 从模板创建时传入
  mode?: 'create' | 'edit' | 'view'; // 页面模式
}

const WorkflowDesignerPage: React.FC<WorkflowDesignerPageProps> = ({
  workflowId,
  templateId,
  mode = 'create'
}) => {
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const [rightPanelVisible, setRightPanelVisible] = useState(true);
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [selectedConnections, setSelectedConnections] = useState<string[]>([]);
  const [scale, setScale] = useState(1);
  const [offset, setOffset] = useState({ x: 0, y: 0 });
  const [currentExecution, setCurrentExecution] = useState<WorkflowExecution | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  
  // 模板参数配置相关状态
  const [showTemplateConfigModal, setShowTemplateConfigModal] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState<any>(null);
  const [templateConfigLoading, setTemplateConfigLoading] = useState(false);
  
  const {
    workflow,
    designerState,
    selectedNodes: designerSelectedNodes,
    isModified,
    isValid,
    validationErrors,
    loadWorkflow,
    loadWorkflowFromDefinition,
    saveWorkflow,
    executeWorkflow,
    exportWorkflow,
    importWorkflow,
    createNewWorkflow,
    undo,
    redo,
    canUndo,
    canRedo,
    handleDesignerEvent,
  } = useWorkflowDesigner();

  // 从设计器状态中提取nodes和connections
  const nodes = designerState.nodes;
  const connections = designerState.connections;

  // 从URL参数获取模板信息
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlTemplateId = urlParams.get('templateId');
    const urlMode = urlParams.get('mode');
    
    if (urlTemplateId && urlMode === 'template' && !templateId) {
      // 从URL参数中获取模板ID
      console.log('从URL参数检测到模板ID:', urlTemplateId);
    }
  }, [templateId]);

  // 初始化工作流
  useEffect(() => {
    const initializeWorkflow = async () => {
      setLoading(true);
      try {
        // 从URL参数获取模板信息
        const urlParams = new URLSearchParams(window.location.search);
        const urlTemplateId = urlParams.get('templateId');
        const urlMode = urlParams.get('mode');
        
        if (workflowId) {
          // 编辑或查看已存在的工作流
          await loadWorkflow(workflowId);
        } else if (templateId || (urlTemplateId && urlMode === 'template')) {
          // 从模板创建新工作流
          const targetTemplateId = templateId || urlTemplateId;
          console.log('正在从模板创建工作流，模板ID:', targetTemplateId);
          
          try {
            // 获取模板详情
            const template = await workflowTemplateService.getTemplate(targetTemplateId as string);
            console.log('成功加载模板:', template);
            
            if (template.workflow) {
              // 设置当前模板并显示参数配置弹窗
              setCurrentTemplate(template);
              setShowTemplateConfigModal(true);
            } else {
              console.error('模板缺少工作流数据:', template);
              Toast.error('模板数据不完整，无法创建工作流');
              createNewWorkflow();
            }
          } catch (error) {
            console.error('加载模板失败:', error);
            Toast.error('加载模板失败，创建空白工作流');
            createNewWorkflow();
          }
        } else {
          // 创建全新的工作流
          createNewWorkflow();
        }
      } catch (error) {
        console.error('初始化工作流失败:', error);
        Toast.error('初始化工作流失败');
      } finally {
        setLoading(false);
      }
    };

    initializeWorkflow();
  }, [workflowId, templateId, loadWorkflow, loadWorkflowFromDefinition, createNewWorkflow]);

  // 设置模拟器执行监听
  useEffect(() => {
    const unsubscribe = workflowSimulator.onExecutionUpdate((execution) => {
      setCurrentExecution(execution);
      if (execution.status === 'COMPLETED' || execution.status === 'FAILED') {
        setIsExecuting(false);
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  // 参数替换函数
  const replaceParametersInObject = useCallback((obj: any, parameters: Record<string, any>): any => {
    if (typeof obj === 'string') {
      // 替换字符串中的参数占位符 {{parameter_name}}
      return obj.replace(/\{\{(\w+)\}\}/g, (match, paramName) => {
        return parameters[paramName] !== undefined ? parameters[paramName] : match;
      });
    } else if (Array.isArray(obj)) {
      // 递归处理数组
      return obj.map(item => replaceParametersInObject(item, parameters));
    } else if (obj && typeof obj === 'object') {
      // 递归处理对象
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        result[key] = replaceParametersInObject(value, parameters);
      }
      return result;
    }
    return obj;
  }, []);

  // 从模板加载工作流（带参数配置）
  const loadWorkflowFromTemplate = useCallback(async (template: any, config?: any) => {
    try {
      // 合并用户配置的变量和模板默认变量
      const mergedVariables = {
        ...template.workflow.variables,
        ...(config?.variables || {})
      };

      // 处理工作流步骤，替换参数占位符
      let processedSteps = template.workflow.steps || [];
      if (config?.variables && Object.keys(config.variables).length > 0) {
        processedSteps = replaceParametersInObject(processedSteps, config.variables);
      }

      // 创建基于模板的新工作流定义
      const newWorkflow: Partial<WorkflowDefinition> = {
        name: config?.workflowName || `基于模板: ${template.name}`,
        description: config?.workflowDescription || `从模板 "${template.name}" 创建的工作流\n\n${template.description}`,
        version: '1.0.0',
        isActive: false,
        steps: processedSteps,
        variables: mergedVariables,
        settings: {
          timeout: config?.settings?.timeout || template.workflow.settings?.timeout || 300000,
          maxRetries: config?.settings?.maxRetries || template.workflow.settings?.maxRetries || 3,
          errorHandling: config?.settings?.errorHandling || template.workflow.settings?.errorHandling || 'stop'
        }
      };
      
      // 使用loadWorkflowFromDefinition方法
      await loadWorkflowFromDefinition(newWorkflow);
      
      // 记录模板应用
      try {
        await workflowTemplateService.rateTemplate(template.id, { rating: 5 });
      } catch (error) {
        console.warn('记录模板使用失败:', error);
      }
      
    } catch (error) {
      console.error('从模板加载工作流失败:', error);
      throw error;
    }
  }, [loadWorkflowFromDefinition, replaceParametersInObject]);

  // 处理模板参数配置确认
  const handleTemplateConfigConfirm = useCallback(async (config: any) => {
    if (!currentTemplate) return;

    try {
      setTemplateConfigLoading(true);
      
      // 使用配置参数从模板创建工作流
      await loadWorkflowFromTemplate(currentTemplate, config);
      
      Toast.success(`已从模板 "${currentTemplate.name}" 创建工作流`);
      setShowTemplateConfigModal(false);
      setCurrentTemplate(null);
    } catch (error) {
      console.error('应用模板配置失败:', error);
      Toast.error('应用模板配置失败');
    } finally {
      setTemplateConfigLoading(false);
    }
  }, [currentTemplate, loadWorkflowFromTemplate]);

  // 处理模板参数配置取消
  const handleTemplateConfigCancel = useCallback(() => {
    setShowTemplateConfigModal(false);
    setCurrentTemplate(null);
    // 如果取消了模板配置，创建空白工作流
    createNewWorkflow();
  }, [createNewWorkflow]);

  // #region 事件处理
  // 处理节点更新（来自属性面板）
  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<DesignerNode>) => {
    handleDesignerEvent({
      type: 'NODE_UPDATE',
      nodeId,
      updates,
    });
  }, [handleDesignerEvent]);

  // 处理节点列表变化 (拖拽添加、移动、删除)
  const handleNodesChange = useCallback((updatedNodes: DesignerNode[]) => {
    const originalNodeCount = nodes.length;
    const updatedNodeCount = updatedNodes.length;

    if (updatedNodeCount > originalNodeCount) {
      // 节点增加
      const originalNodeIds = new Set(nodes.map(n => n.id));
      const newNode = updatedNodes.find(n => !originalNodeIds.has(n.id));
      if (newNode) {
        handleDesignerEvent({ type: 'NODE_ADD', node: newNode });
      }
    } else if (updatedNodeCount < originalNodeCount) {
      // 节点删除
      const updatedNodeIds = new Set(updatedNodes.map(n => n.id));
      const deletedNode = nodes.find(n => !updatedNodeIds.has(n.id));
      if (deletedNode) {
        handleDesignerEvent({ type: 'NODE_DELETE', nodeId: deletedNode.id });
      }
    } else {
      // 节点移动
      const movedNode = updatedNodes.find(un => {
        const on = nodes.find(n => n.id === un.id);
        return on && (on.position.x !== un.position.x || on.position.y !== un.position.y);
      });

      if (movedNode) {
        handleDesignerEvent({
          type: 'NODE_MOVE',
          nodeId: movedNode.id,
          position: movedNode.position
        });
      }
    }
  }, [nodes, handleDesignerEvent]);

  // 处理连接列表变化
  const handleConnectionsChange = useCallback((updatedConnections: DesignerConnection[]) => {
    const originalConnectionCount = connections.length;
    const updatedConnectionCount = updatedConnections.length;

    if (updatedConnectionCount > originalConnectionCount) {
      const originalConnectionIds = new Set(connections.map(c => c.id));
      const newConnection = updatedConnections.find(c => !originalConnectionIds.has(c.id));
      if (newConnection) {
        handleDesignerEvent({ type: 'CONNECTION_CREATE', connection: newConnection });
      }
    } else if (updatedConnectionCount < originalConnectionCount) {
      const updatedConnectionIds = new Set(updatedConnections.map(c => c.id));
      const deletedConnection = connections.find(c => !updatedConnectionIds.has(c.id));
      if (deletedConnection) {
        handleDesignerEvent({ type: 'CONNECTION_DELETE', connectionId: deletedConnection.id });
      }
    }
  }, [connections, handleDesignerEvent]);


  // 处理选择变化
  const handleSelectionChange = useCallback((selection: CanvasSelection) => {
    setSelectedNodes(selection.nodes);
    setSelectedConnections(selection.connections);
  }, []);

  // 处理画布事件
  const handleCanvasEvent = useCallback((event: CanvasEvent) => {
    console.log('Canvas event:', event);
    // 可以在这里处理画布事件，如节点移动、选择等
  }, []);
  // #endregion

  // 保存工作流
  const handleSave = useCallback(async () => {
    if (!isValid) {
      Toast.warning('请先修复工作流中的错误');
      return;
    }

    try {
      setLoading(true);
      await saveWorkflow();
      Toast.success('工作流保存成功');
    } catch (error) {
      console.error('保存工作流失败:', error);
      Toast.error('保存工作流失败');
    } finally {
      setLoading(false);
    }
  }, [isValid, saveWorkflow]);

  // 执行工作流
  const handleExecute = useCallback(async () => {
    if (!workflow?.id) {
      Toast.warning('请先保存工作流');
      return;
    }

    if (!isValid) {
      Toast.warning('工作流配置有误，无法执行');
      return;
    }

    try {
      setLoading(true);
      const execution = await executeWorkflow({
        workflowId: workflow.id,
        context: {},
        priority: 'MEDIUM'
      });
      
      Toast.success(`工作流执行已启动，会话ID: ${execution.sessionId}`);
      
      // 可以在这里导航到执行监控页面
      // navigate(`/admin/workflows/execution/${execution.sessionId}`);
    } catch (error) {
      console.error('执行工作流失败:', error);
      Toast.error('执行工作流失败');
    } finally {
      setLoading(false);
    }
  }, [workflow, isValid, executeWorkflow]);

  // 模拟执行工作流
  const handleSimulateExecution = useCallback(async (): Promise<WorkflowExecution> => {
    if (!nodes.length) {
      Toast.warning('请先添加工作流节点');
      throw new Error('No nodes to execute');
    }

    if (!isValid) {
      Toast.warning('工作流配置有误，无法执行');
      throw new Error('Invalid workflow configuration');
    }

    setIsExecuting(true);
    setCurrentExecution(null);

    try {
      const mockWorkflow: WorkflowDefinition = {
        id: workflow?.id || 'mock_workflow',
        name: workflow?.name || '测试工作流',
        description: workflow?.description || '模拟执行测试',
        version: '1.0.0',
        isActive: true,
        steps: nodes.map(node => node.step),
        variables: {},
        settings: {
          timeout: 300000,
          maxRetries: 3,
          errorHandling: 'stop'
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      const execution = await workflowSimulator.simulateExecution(
        mockWorkflow,
        nodes,
        connections,
        {
          test_mode: true,
          user_id: 'test_user',
          timestamp: new Date().toISOString()
        }
      );

      Toast.success('模拟执行完成');
      return execution;

    } catch (error) {
      setIsExecuting(false);
      console.error('模拟执行失败:', error);
      Toast.error('模拟执行失败');
      throw error;
    }
  }, [workflow, nodes, connections, isValid]);

  // 停止执行
  const handleStopExecution = useCallback(() => {
    if (currentExecution?.sessionId) {
      workflowSimulator.stopExecution(currentExecution.sessionId);
      setIsExecuting(false);
      Toast.info('已停止执行');
    }
  }, [currentExecution]);

  // 导出工作流
  const handleExport = useCallback(async () => {
    try {
      const exportData = await exportWorkflow();
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `workflow_${workflow?.name || 'unnamed'}_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      Toast.success('工作流导出成功');
    } catch (error) {
      console.error('导出工作流失败:', error);
      Toast.error('导出工作流失败');
    }
  }, [exportWorkflow, workflow]);

  // 导入工作流
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const text = await file.text();
        const importData = JSON.parse(text);
        await importWorkflow(importData);
        Toast.success('工作流导入成功');
      } catch (error) {
        console.error('导入工作流失败:', error);
        Toast.error('导入工作流失败');
      }
    };
    input.click();
  }, [importWorkflow]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            handleSave();
            break;
          case 'z':
            if (e.shiftKey) {
              e.preventDefault();
              redo();
            } else {
              e.preventDefault();
              undo();
            }
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSave, undo, redo]);

  // 获取可用变量
  const getAvailableVariables = useCallback(() => {
    return [
      { name: 'workflow_id', type: 'string', description: '工作流ID' },
      { name: 'execution_id', type: 'string', description: '执行ID' },
      { name: 'current_time', type: 'date', description: '当前时间' },
      { name: 'user_name', type: 'string', description: '执行用户' },
      { name: 'input', type: 'object', description: '输入数据' },
      { name: 'context', type: 'object', description: '上下文数据' }
    ];
  }, []);

  // 验证节点并获取错误信息
  const validateNodes = useCallback(() => {
    const errors: string[] = [];
    
    nodes.forEach((node, index) => {
      // 检查节点基本属性
      if (!node.step.type) {
        errors.push(`节点 ${index + 1} (${node.step.name || '未命名'}): 步骤类型不能为空`);
      }
      
      // 检查特定类型步骤的配置
      switch (node.step.type) {
        case 'HTTP_REQUEST':
          if (!node.step.config.url) {
            errors.push(`节点 ${index + 1} (${node.step.name || 'HTTP请求'}): URL不能为空`);
          }
          break;
        case 'DATABASE_OPERATION':
          if (!node.step.config.query) {
            errors.push(`节点 ${index + 1} (${node.step.name || '数据库操作'}): 查询语句不能为空`);
          }
          break;
        case 'NOTIFICATION':
          if (!node.step.config.recipients || node.step.config.recipients.length === 0) {
            errors.push(`节点 ${index + 1} (${node.step.name || '通知'}): 接收者不能为空`);
          }
          break;
        case 'CONDITION':
          if (!node.step.config.expression) {
            errors.push(`节点 ${index + 1} (${node.step.name || '条件判断'}): 表达式不能为空`);
          }
          break;
      }
    });
    
    return errors;
  }, [nodes]);

  if (loading) {
    return (
      <div className="workflow-designer-loading">
        <Spin size="large" />
        <p>正在加载工作流设计器...</p>
      </div>
    );
  }

  return (
    <div className="workflow-designer-page">
      <Layout className="workflow-designer-layout">
        {/* 头部工具栏 */}
        <Header className="workflow-designer-header">
          <div className="header-left">
            <h2 className="workflow-title">
              {workflow?.name || '新建工作流'}
              {isModified && <span className="modified-indicator"> *</span>}
            </h2>
            <div className="workflow-status">
              {!isValid && (
                <span className="validation-error">
                  {validationErrors.length} 个错误
                </span>
              )}
              {mode === 'view' && (
                <span className="readonly-indicator">只读模式</span>
              )}
            </div>
          </div>
          
          <Space className="header-actions">
            {/* 撤销重做 */}
            <Button 
              icon={<IconUndo />}
              onClick={undo}
              disabled={!canUndo}
              theme="borderless"
              size="small"
            />
            <Button 
              icon={<IconRedo />}
              onClick={redo}
              disabled={!canRedo}
              theme="borderless"
              size="small"
            />
            
            <Divider layout="vertical" />
            
            {/* 导入导出 */}
            <Button 
              icon={<IconUpload />}
              onClick={handleImport}
              theme="borderless"
              disabled={mode === 'view'}
              size="small"
            >
              导入
            </Button>
            
            <Button 
              icon={<IconDownload />}
              onClick={handleExport}
              theme="borderless"
              size="small"
            >
              导出
            </Button>
            
            {/* 预览 */}
            <Button 
              icon={<IconEyeOpened />}
              onClick={() => setShowPreview(true)}
              theme="borderless"
              size="small"
            >
              预览
            </Button>
            
            {/* 调试 */}
            <Button 
              icon={<IconCode />}
              onClick={() => setShowDebugPanel(true)}
              theme="borderless"
              type={showDebugPanel ? 'primary' : 'tertiary'}
              size="small"
            >
              调试
            </Button>
            
            {/* 属性面板切换 */}
            <Button 
              icon={<IconSetting />}
              onClick={() => setRightPanelVisible(!rightPanelVisible)}
              theme="borderless"
              type={rightPanelVisible ? 'primary' : 'tertiary'}
              size="small"
            >
              属性
            </Button>
            
            <Divider layout="vertical" />
            
            {/* 执行和保存 */}
            {isExecuting ? (
              <Button 
                icon={<IconStop />}
                onClick={handleStopExecution}
                theme="solid"
                type="danger"
                size="small"
              >
                停止执行
              </Button>
            ) : (
              <Button 
                icon={<IconPlay />}
                onClick={handleSimulateExecution}
                theme="solid"
                type="secondary"
                disabled={!nodes.length || mode === 'view'}
                size="small"
              >
                模拟执行
              </Button>
            )}
            
            <Button 
              icon={<IconSave />}
              onClick={handleSave}
              theme="solid"
              type="primary"
              disabled={!isModified || !isValid || mode === 'view'}
              size="small"
            >
              保存
            </Button>
          </Space>
        </Header>

        {/* 错误信息显示区域 */}
        {!isValid && validationErrors.length > 0 && (
          <div 
            className="workflow-validation-errors"
            style={{
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              padding: '12px 16px',
              margin: '0 16px 16px 16px',
              borderRadius: '6px'
            }}
          >
            <div style={{ 
              color: '#ff4d4f', 
              fontWeight: 500, 
              marginBottom: '8px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <IconAlertTriangle style={{ marginRight: '8px' }} />
              工作流配置错误:
            </div>
            <ul style={{ 
              margin: 0, 
              paddingLeft: '20px',
              maxHeight: '150px',
              overflowY: 'auto'
            }}>
              {validationErrors.map((error, index) => (
                <li 
                  key={index} 
                  style={{ 
                    color: '#ff4d4f', 
                    fontSize: '13px',
                    marginBottom: '4px'
                  }}
                >
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* 节点错误信息显示区域 */}
        {nodes.length > 0 && validateNodes().length > 0 && (
          <div 
            className="node-validation-errors"
            style={{
              backgroundColor: '#fffbe6',
              border: '1px solid #ffe58f',
              padding: '12px 16px',
              margin: '0 16px 16px 16px',
              borderRadius: '6px'
            }}
          >
            <div style={{ 
              color: '#faad14', 
              fontWeight: 500, 
              marginBottom: '8px',
              display: 'flex',
              alignItems: 'center'
            }}>
              <IconAlertTriangle style={{ marginRight: '8px' }} />
              节点配置警告:
            </div>
            <ul style={{ 
              margin: 0, 
              paddingLeft: '20px',
              maxHeight: '150px',
              overflowY: 'auto'
            }}>
              {validateNodes().map((error, index) => (
                <li 
                  key={index} 
                  style={{ 
                    color: '#faad14', 
                    fontSize: '13px',
                    marginBottom: '4px'
                  }}
                >
                  {error}
                </li>
              ))}
            </ul>
          </div>
        )}

        <Layout style={{ height: 'calc(100vh - 60px)' }}>
          {/* 中心设计画布 */}
          <Content className="workflow-designer-content" style={{ position: 'relative' }}>
            <WorkflowDesignerCore
              nodes={nodes}
              connections={connections}
              selectedNodes={selectedNodes}
              selectedConnections={selectedConnections}
              scale={scale}
              offset={offset}
              readonly={mode === 'view'}
              loading={loading}
              onNodesChange={handleNodesChange}
              onConnectionsChange={handleConnectionsChange}
              onNodeUpdate={handleNodeUpdate} // 新增此行
              onSelectionChange={handleSelectionChange}
              onScaleChange={setScale}
              onOffsetChange={setOffset}
              onCanvasEvent={handleCanvasEvent}
            />
          </Content>

          {/* 右侧属性面板 */}
          {rightPanelVisible && (
            <Sider 
              className="workflow-designer-right-panel"
              style={{
                backgroundColor: 'white',
                borderLeft: '1px solid #d9d9d9',
                height: '100%',
                overflow: 'scroll',
                width: '360px'
              }}
            >
              <WorkflowPropertiesPanel
                selectedNodes={selectedNodes}
                nodes={nodes}
                onNodeUpdate={handleNodeUpdate}
                onClose={() => setRightPanelVisible(false)}
                availableVariables={getAvailableVariables()}
                readonly={mode === 'view'}
              />
            </Sider>
          )}
        </Layout>
      </Layout>

      {/* 预览对话框 */}
      <Modal
        title="工作流预览"
        visible={showPreview}
        onCancel={() => setShowPreview(false)}
        width={1400}
        height="85vh"
        footer={null}
        style={{ top: '5vh' }}
        bodyStyle={{ padding: 0, height: '80vh' }}
      >
        <WorkflowPreviewPanel
          workflow={workflow || undefined}
          nodes={nodes}
          connections={connections}
          visible={showPreview}
          onClose={() => setShowPreview(false)}
          onExecute={handleSimulateExecution}
          onStop={handleStopExecution}
          execution={currentExecution}
        />
      </Modal>

      {/* 调试面板 */}
      <SideSheet
        title="工作流调试面板"
        visible={showDebugPanel}
        onCancel={() => setShowDebugPanel(false)}
        width={600}
        placement="right"
        bodyStyle={{ padding: 0 }}
      >
        <WorkflowDebugPanel
          execution={currentExecution}
          nodes={nodes}
          visible={showDebugPanel}
          onClose={() => setShowDebugPanel(false)}
        />
      </SideSheet>

      {/* 模板参数配置弹窗 */}
      <TemplateParameterConfigModal
        visible={showTemplateConfigModal}
        template={currentTemplate}
        loading={templateConfigLoading}
        onConfirm={handleTemplateConfigConfirm}
        onCancel={handleTemplateConfigCancel}
      />

      {/* 全局快捷键提示 */}
      <div className="keyboard-shortcuts-hint">
        <div className="shortcuts">
          <span>Ctrl+S 保存</span>
          <span>Ctrl+Z 撤销</span>
          <span>Ctrl+Shift+Z 重做</span>
        </div>
      </div>
    </div>
  );
};

export default WorkflowDesignerPage;