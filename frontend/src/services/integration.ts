/**
 * 外部系统集成API服务
 */

import { get, post, put, del } from '../utils/request';
import type { ApiResponse } from '../types/common';

export interface IntegrationConfig {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error' | 'testing' | 'configuring';
  category: string;
  enabled: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
  lastHealthCheck?: string;
  healthStatus?: 'healthy' | 'warning' | 'critical';
  description?: string;
  auth?: {
    type: string;
    credentials: Record<string, any>;
  };
  endpoints?: Record<string, any>;
  settings?: Record<string, any>;
  tags?: string[];
}

export interface IntegrationMetrics {
  integrationId: string;
  timestamp: string;
  requests: {
    total: number;
    success: number;
    failed: number;
    avgResponseTime: number;
    maxResponseTime: number;
  };
  errors: {
    total: number;
    byType: Record<string, number>;
  };
  availability: number;
  lastHealthCheck: string;
  healthStatus: 'healthy' | 'warning' | 'critical';
}

export interface IntegrationEvent {
  id: string;
  integrationId: string;
  type: 'success' | 'error' | 'warning' | 'info';
  operation: string;
  message: string;
  details?: any;
  timestamp: string;
  resolved?: boolean;
  resolvedAt?: string;
}

export interface CreateIntegrationRequest {
  name: string;
  type: string;
  description?: string;
  auth: {
    type: string;
    credentials: Record<string, any>;
  };
  endpoints: Record<string, any>;
  settings?: Record<string, any>;
  tags?: string[];
  category: string;
  priority?: number;
}

export interface ExecuteOperationRequest {
  operation: string;
  endpoint: string;
  parameters?: Record<string, any>;
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

class IntegrationService {
  
  /**
   * 获取所有集成配置
   */
  async getAllIntegrations(params?: {
    type?: string;
    status?: string;
    category?: string;
  }): Promise<ApiResponse<IntegrationConfig[]>> {
    return get('/api/v1/integrations', { params });
  }

  /**
   * 获取单个集成配置
   */
  async getIntegration(id: string): Promise<ApiResponse<IntegrationConfig>> {
    return get(`/api/v1/integrations/${id}`);
  }

  /**
   * 创建集成配置
   */
  async createIntegration(data: CreateIntegrationRequest): Promise<ApiResponse<IntegrationConfig>> {
    return post('/api/v1/integrations', data);
  }

  /**
   * 更新集成配置
   */
  async updateIntegration(id: string, data: Partial<IntegrationConfig>): Promise<ApiResponse<IntegrationConfig>> {
    return put(`/api/v1/integrations/${id}`, data);
  }

  /**
   * 删除集成配置
   */
  async deleteIntegration(id: string): Promise<ApiResponse<null>> {
    return del(`/api/v1/integrations/${id}`);
  }

  /**
   * 执行集成操作
   */
  async executeOperation(id: string, request: ExecuteOperationRequest): Promise<ApiResponse<any>> {
    return post(`/api/v1/integrations/${id}/execute`, request);
  }

  /**
   * 批量执行集成操作
   */
  async executeBatchOperations(requests: {
    requests: (ExecuteOperationRequest & { integrationId: string })[];
    parallel?: boolean;
    continueOnError?: boolean;
    timeout?: number;
  }): Promise<ApiResponse<any>> {
    return post('/api/v1/integrations/batch-execute', requests);
  }

  /**
   * 获取集成监控指标
   */
  async getMetrics(id: string): Promise<ApiResponse<IntegrationMetrics>> {
    return get(`/api/v1/integrations/${id}/metrics`);
  }

  /**
   * 获取集成事件日志
   */
  async getEvents(id: string, limit = 100): Promise<ApiResponse<IntegrationEvent[]>> {
    return get(`/api/v1/integrations/${id}/events`, { params: { limit } });
  }

  /**
   * 集成健康检查
   */
  async healthCheck(id: string): Promise<ApiResponse<{
    healthy: boolean;
    details?: any;
  }>> {
    return get(`/api/v1/integrations/${id}/health`);
  }

  /**
   * 获取性能趋势数据
   */
  async getPerformanceTrend(id: string, hours = 24): Promise<ApiResponse<{
    timestamps: string[];
    responseTimes: number[];
    successRates: number[];
    errorCounts: number[];
  }>> {
    return get(`/api/v1/integrations/${id}/performance-trend`, { params: { hours } });
  }

  /**
   * 测试集成连接
   */
  async testConnection(id: string): Promise<ApiResponse<{
    success: boolean;
    message: string;
    responseTime?: number;
  }>> {
    return post(`/api/v1/integrations/${id}/test-connection`);
  }

  /**
   * 启用/禁用集成
   */
  async toggleIntegration(id: string, enabled: boolean): Promise<ApiResponse<IntegrationConfig>> {
    return put(`/api/v1/integrations/${id}`, { enabled });
  }

  /**
   * 获取集成类型列表
   */
  getIntegrationTypes(): Array<{
    value: string;
    label: string;
    category: string;
    icon?: string;
    description?: string;
  }> {
    return [
      {
        value: 'chat_wechat_work',
        label: '企业微信',
        category: 'communication',
        icon: '💬',
        description: '企业微信应用集成，支持消息推送和用户管理'
      },
      {
        value: 'chat_dingtalk',
        label: '钉钉',
        category: 'communication',
        icon: '💬',
        description: '钉钉应用集成，支持消息推送和审批流程'
      },
      {
        value: 'chat_slack',
        label: 'Slack',
        category: 'communication',
        icon: '💬',
        description: 'Slack应用集成，支持频道消息和工作流'
      },
      {
        value: 'cloud_aliyun',
        label: '阿里云',
        category: 'cloud',
        icon: '☁️',
        description: '阿里云服务集成，支持ECS、RDS、SLB等服务'
      },
      {
        value: 'monitor_prometheus',
        label: 'Prometheus',
        category: 'monitoring',
        icon: '📊',
        description: 'Prometheus监控系统集成'
      },
      {
        value: 'monitor_grafana',
        label: 'Grafana',
        category: 'monitoring',
        icon: '📈',
        description: 'Grafana可视化面板集成'
      },
      {
        value: 'devops_gitlab',
        label: 'GitLab',
        category: 'devops',
        icon: '🦊',
        description: 'GitLab代码仓库和CI/CD集成'
      },
      {
        value: 'devops_jenkins',
        label: 'Jenkins',
        category: 'devops',
        icon: '🔧',
        description: 'Jenkins构建服务器集成'
      }
    ];
  }

  /**
   * 获取认证类型列表
   */
  getAuthTypes(): Array<{
    value: string;
    label: string;
    description: string;
    fields: Array<{
      key: string;
      label: string;
      type: 'input' | 'password' | 'textarea';
      required: boolean;
      placeholder?: string;
    }>;
  }> {
    return [
      {
        value: 'api_key',
        label: 'API Key',
        description: '使用API密钥进行认证',
        fields: [
          {
            key: 'apiKey',
            label: 'API密钥',
            type: 'password',
            required: true,
            placeholder: '请输入API密钥'
          },
          {
            key: 'headerName',
            label: '请求头名称',
            type: 'input',
            required: false,
            placeholder: '默认为 X-API-Key'
          }
        ]
      },
      {
        value: 'oauth2',
        label: 'OAuth 2.0',
        description: '使用OAuth 2.0进行认证',
        fields: [
          {
            key: 'clientId',
            label: '客户端ID',
            type: 'input',
            required: true,
            placeholder: '请输入客户端ID'
          },
          {
            key: 'clientSecret',
            label: '客户端密钥',
            type: 'password',
            required: true,
            placeholder: '请输入客户端密钥'
          },
          {
            key: 'tokenEndpoint',
            label: '令牌端点',
            type: 'input',
            required: false,
            placeholder: 'https://api.example.com/oauth/token'
          },
          {
            key: 'scope',
            label: '权限范围',
            type: 'input',
            required: false,
            placeholder: '用空格分隔多个权限'
          }
        ]
      },
      {
        value: 'basic_auth',
        label: 'Basic Auth',
        description: '使用用户名和密码进行认证',
        fields: [
          {
            key: 'username',
            label: '用户名',
            type: 'input',
            required: true,
            placeholder: '请输入用户名'
          },
          {
            key: 'password',
            label: '密码',
            type: 'password',
            required: true,
            placeholder: '请输入密码'
          }
        ]
      },
      {
        value: 'token',
        label: 'Bearer Token',
        description: '使用Bearer令牌进行认证',
        fields: [
          {
            key: 'token',
            label: '访问令牌',
            type: 'password',
            required: true,
            placeholder: '请输入访问令牌'
          },
          {
            key: 'headerName',
            label: '请求头名称',
            type: 'input',
            required: false,
            placeholder: '默认为 Authorization'
          },
          {
            key: 'prefix',
            label: '令牌前缀',
            type: 'input',
            required: false,
            placeholder: '默认为 Bearer'
          }
        ]
      }
    ];
  }
}

export const integrationService = new IntegrationService();