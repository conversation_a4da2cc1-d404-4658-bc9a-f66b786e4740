/**
 * 工作流模板服务
 * 处理工作流模板相关的API请求
 */

import * as request from '../utils/request';
import type {
  WorkflowCategory,
  WorkflowDefinition
} from '../types/workflow';

// 工作流模板接口
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  subcategory: string;
  tags: string[];
  author: string;
  version: string;
  isBuiltin: boolean;
  isPopular: boolean;
  usageCount: number;
  rating: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // 预计执行时间（分钟）
  preview: {
    thumbnail: string;
    screenshots: string[];
    demoVideo?: string;
  };
  requirements: {
    permissions: string[];
    integrations: string[];
    minimumVersion: string;
  };
  workflow?: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
  metadata: {
    createdAt: string;
    updatedAt: string;
    downloads: number;
    lastUsed?: string;
    stepsCount?: number;
    hasVariables?: boolean;
  };
}

// 模板分类接口
export interface TemplateCategory {
  id: WorkflowCategory;
  name: string;
  description: string;
  icon: string;
  subcategories: Array<{
    id: string;
    name: string;
    description: string;
  }>;
}

// 查询参数接口
export interface TemplateListParams {
  category?: WorkflowCategory;
  subcategory?: string;
  search?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  tags?: string[];
  popular?: boolean;
  limit?: number;
  offset?: number;
  sort?: 'rating' | 'usage' | 'recent' | 'name';
  order?: 'asc' | 'desc';
}

// 搜索参数接口
export interface TemplateSearchParams {
  q: string;
  category?: WorkflowCategory;
  difficulty?: string;
  tags?: string[];
  limit?: number;
}

// 应用模板请求接口
export interface ApplyTemplateRequest {
  customizations?: {
    name?: string;
    description?: string;
    variables?: Record<string, any>;
    settings?: any;
  };
}

// 评分请求接口
export interface RateTemplateRequest {
  rating: number; // 1-5
  comment?: string;
}

// 创建模板请求接口
export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: WorkflowCategory;
  subcategory?: string;
  tags?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration?: number;
  preview?: {
    thumbnail?: string;
    screenshots?: string[];
  };
  requirements?: {
    permissions?: string[];
    integrations?: string[];
    minimumVersion?: string;
  };
  workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
}

// 响应接口
export interface TemplateListResponse {
  templates: WorkflowTemplate[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    pages: number;
  };
}

export interface TemplateSearchResponse {
  results: WorkflowTemplate[];
  total: number;
  query: string;
  filters: any;
}

export interface TemplateStatsResponse {
  totalTemplates: number;
  builtinTemplates: number;
  customTemplates: number;
  popularTemplates: number;
  categoryStats: Array<{
    id: WorkflowCategory;
    name: string;
    count: number;
    subcategoryStats: Array<{
      id: string;
      name: string;
      count: number;
    }>;
  }>;
  difficultyStats: {
    beginner: number;
    intermediate: number;
    advanced: number;
  };
  topTags: Array<{
    tag: string;
    count: number;
  }>;
}

export interface ApplyTemplateResponse {
  workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>;
  templateId: string;
  appliedAt: string;
}

/**
 * 工作流模板服务类
 */
class WorkflowTemplateService {
  private readonly baseUrl = '/api/v1/workflow-templates';

  /**
   * 获取所有模板分类
   */
  async getCategories(): Promise<TemplateCategory[]> {
    const response = await request.get(`${this.baseUrl}/categories`);
    return response.data;
  }

  /**
   * 获取模板列表
   */
  async getTemplates(params?: TemplateListParams): Promise<TemplateListResponse> {
    const queryParams = new URLSearchParams();
    
    if (params?.category) queryParams.append('category', params.category);
    if (params?.subcategory) queryParams.append('subcategory', params.subcategory);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.difficulty) queryParams.append('difficulty', params.difficulty);
    if (params?.tags) queryParams.append('tags', params.tags.join(','));
    if (params?.popular !== undefined) queryParams.append('popular', params.popular.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());
    if (params?.sort) queryParams.append('sort', params.sort);
    if (params?.order) queryParams.append('order', params.order);

    const url = queryParams.toString() ? `${this.baseUrl}?${queryParams.toString()}` : this.baseUrl;
    const response = await request.get(url);
    return response.data;
  }

  /**
   * 获取模板详情
   */
  async getTemplate(templateId: string): Promise<WorkflowTemplate> {
    const response = await request.get(`${this.baseUrl}/${templateId}`);
    return response.data;
  }

  /**
   * 搜索模板
   */
  async searchTemplates(params: TemplateSearchParams): Promise<TemplateSearchResponse> {
    const queryParams = new URLSearchParams();
    queryParams.append('q', params.q);
    
    if (params.category) queryParams.append('category', params.category);
    if (params.difficulty) queryParams.append('difficulty', params.difficulty);
    if (params.tags) queryParams.append('tags', params.tags.join(','));
    if (params.limit) queryParams.append('limit', params.limit.toString());

    const response = await request.get(`${this.baseUrl}/search?${queryParams.toString()}`);
    return response.data;
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(limit = 10): Promise<WorkflowTemplate[]> {
    const response = await request.get(`${this.baseUrl}/popular?limit=${limit}`);
    return response.data;
  }

  /**
   * 获取最新模板
   */
  async getRecentTemplates(limit = 10): Promise<WorkflowTemplate[]> {
    const response = await request.get(`${this.baseUrl}/recent?limit=${limit}`);
    return response.data;
  }

  /**
   * 获取指定分类的模板
   */
  async getTemplatesByCategory(category: WorkflowCategory): Promise<WorkflowTemplate[]> {
    const response = await this.getTemplates({ category });
    return response.templates;
  }

  /**
   * 获取指定子分类的模板
   */
  async getTemplatesBySubcategory(category: WorkflowCategory, subcategory: string): Promise<WorkflowTemplate[]> {
    const response = await this.getTemplates({ category, subcategory });
    return response.templates;
  }

  /**
   * 应用模板创建工作流
   */
  async applyTemplate(templateId: string, data?: ApplyTemplateRequest): Promise<ApplyTemplateResponse> {
    const response = await request.post(`${this.baseUrl}/${templateId}/apply`, data);
    return response.data;
  }

  /**
   * 模板评分
   */
  async rateTemplate(templateId: string, data: RateTemplateRequest): Promise<void> {
    await request.post(`${this.baseUrl}/${templateId}/rate`, data);
  }

  /**
   * 导出模板
   */
  async exportTemplate(templateId: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/${templateId}/export`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`导出模板失败: ${response.statusText}`);
    }
    
    return response.blob();
  }

  /**
   * 导入模板
   */
  async importTemplate(templateData: any): Promise<{ templateId: string; importedAt: string }> {
    const response = await request.post(`${this.baseUrl}/import`, templateData);
    return response.data;
  }

  /**
   * 创建自定义模板
   */
  async createCustomTemplate(data: CreateTemplateRequest): Promise<{ templateId: string; createdAt: string }> {
    const response = await request.post(this.baseUrl, data);
    return response.data;
  }

  /**
   * 获取模板统计信息
   */
  async getTemplateStats(): Promise<TemplateStatsResponse> {
    const response = await request.get(`${this.baseUrl}/stats`);
    return response.data;
  }

  /**
   * 从文件导入模板
   */
  async importTemplateFromFile(file: File): Promise<{ templateId: string; importedAt: string }> {
    try {
      const text = await file.text();
      const templateData = JSON.parse(text);
      return this.importTemplate(templateData);
    } catch (error) {
      throw new Error(`导入模板文件失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 下载模板文件
   */
  async downloadTemplate(templateId: string, templateName?: string): Promise<void> {
    try {
      const blob = await this.exportTemplate(templateId);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `workflow-template-${templateName || templateId}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      throw new Error(`下载模板失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取难度等级的中文名称
   */
  getDifficultyLabel(difficulty: 'beginner' | 'intermediate' | 'advanced'): string {
    const labels = {
      beginner: '初级',
      intermediate: '中级',
      advanced: '高级'
    };
    return labels[difficulty] || difficulty;
  }

  /**
   * 获取难度等级的颜色
   */
  getDifficultyColor(difficulty: 'beginner' | 'intermediate' | 'advanced'): string {
    const colors = {
      beginner: 'green',
      intermediate: 'orange', 
      advanced: 'red'
    };
    return colors[difficulty] || 'grey';
  }

  /**
   * 格式化预计执行时间
   */
  formatEstimatedDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} 分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours} 小时 ${remainingMinutes} 分钟` : `${hours} 小时`;
    }
  }

  /**
   * 验证模板数据格式
   */
  validateTemplateData(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || typeof data.name !== 'string') {
      errors.push('模板名称不能为空');
    }

    if (!data.workflow) {
      errors.push('工作流定义不能为空');
    } else {
      if (!data.workflow.steps || !Array.isArray(data.workflow.steps)) {
        errors.push('工作流步骤不能为空');
      }
    }

    if (data.difficulty && !['beginner', 'intermediate', 'advanced'].includes(data.difficulty)) {
      errors.push('难度等级无效');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const workflowTemplateService = new WorkflowTemplateService();
export default workflowTemplateService;