/**
 * 通信平台集成API服务
 */

import { post } from '../utils/request';
import type { ApiResponse } from '../types/common';

export interface MessageConfig {
  type: 'text' | 'markdown' | 'card' | 'image' | 'file';
  content: string;
  title?: string;
  url?: string;
  imageUrl?: string;
  fileUrl?: string;
  buttons?: Array<{
    text: string;
    url?: string;
    type?: 'default' | 'primary' | 'danger';
  }>;
}

export interface SendMessageRequest {
  platform: 'wechat_work' | 'dingtalk' | 'slack';
  recipients: string[];
  message: MessageConfig;
  isGroup?: boolean;
  atAll?: boolean;
  atUsers?: string[];
}

export interface BroadcastMessageRequest {
  platforms: ('wechat_work' | 'dingtalk' | 'slack')[];
  recipients: Record<string, string[]>;
  message: MessageConfig;
}

export interface WeChatWorkConfig {
  corpId: string;
  corpSecret: string;
  agentId: string;
  name?: string;
}

export interface DingTalkConfig {
  appKey: string;
  appSecret: string;
  robotToken?: string;
  name?: string;
}

export interface SlackConfig {
  botToken: string;
  signingSecret: string;
  appId?: string;
  name?: string;
}

class CommunicationService {
  
  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<ApiResponse<any>> {
    return post('/api/v1/integrations/communication/send-message', request);
  }

  /**
   * 广播消息到多个平台
   */
  async broadcastMessage(request: BroadcastMessageRequest): Promise<ApiResponse<Record<string, any>>> {
    return post('/api/v1/integrations/communication/broadcast', request);
  }

  /**
   * 注册企业微信集成
   */
  async registerWeChatWork(config: WeChatWorkConfig): Promise<ApiResponse<null>> {
    return post('/api/v1/integrations/communication/wechat-work', config);
  }

  /**
   * 注册钉钉集成
   */
  async registerDingTalk(config: DingTalkConfig): Promise<ApiResponse<null>> {
    return post('/api/v1/integrations/communication/dingtalk', config);
  }

  /**
   * 注册Slack集成
   */
  async registerSlack(config: SlackConfig): Promise<ApiResponse<null>> {
    return post('/api/v1/integrations/communication/slack', config);
  }

  /**
   * 发送测试消息
   */
  async sendTestMessage(
    platform: 'wechat_work' | 'dingtalk' | 'slack',
    recipients: string[],
    testType: 'text' | 'markdown' | 'card' = 'text'
  ): Promise<ApiResponse<any>> {
    const testMessages = {
      text: {
        type: 'text' as const,
        content: '🎉 这是一条来自运维服务管理系统的测试消息！\n\n系统集成测试成功，消息推送功能正常运行。'
      },
      markdown: {
        type: 'markdown' as const,
        content: `# 系统测试消息 🚀

**运维服务管理系统**集成测试成功！

## 功能特性
- ✅ 工单管理
- ✅ SLA监控  
- ✅ 自动化工作流
- ✅ 外部系统集成

---
*测试时间: ${new Date().toLocaleString()}*`
      },
      card: {
        type: 'card' as const,
        title: '🔔 系统通知',
        content: '运维服务管理系统集成测试成功！所有功能模块运行正常，消息推送服务已就绪。',
        url: window.location.origin
      }
    };

    return this.sendMessage({
      platform,
      recipients,
      message: testMessages[testType]
    });
  }

  /**
   * 获取平台配置模板
   */
  getPlatformConfigTemplates(): Record<string, {
    name: string;
    icon: string;
    description: string;
    fields: Array<{
      key: string;
      label: string;
      type: 'input' | 'password' | 'textarea';
      required: boolean;
      placeholder?: string;
      help?: string;
    }>;
    setupGuide: string[];
  }> {
    return {
      wechat_work: {
        name: '企业微信',
        icon: '💬',
        description: '通过企业微信应用推送工单通知、告警信息等',
        fields: [
          {
            key: 'corpId',
            label: '企业ID',
            type: 'input',
            required: true,
            placeholder: '请输入企业微信的企业ID',
            help: '可在企业微信管理后台"我的企业"中查看'
          },
          {
            key: 'corpSecret',
            label: '应用密钥',
            type: 'password',
            required: true,
            placeholder: '请输入应用的Secret',
            help: '可在企业微信管理后台"应用管理"中查看'
          },
          {
            key: 'agentId',
            label: '应用ID',
            type: 'input',
            required: true,
            placeholder: '请输入应用的AgentId',
            help: '可在企业微信管理后台"应用管理"中查看'
          },
          {
            key: 'name',
            label: '集成名称',
            type: 'input',
            required: false,
            placeholder: '自定义集成名称（可选）'
          }
        ],
        setupGuide: [
          '登录企业微信管理后台',
          '创建自建应用或使用现有应用',
          '记录企业ID、应用Secret和AgentId',
          '设置应用的可见范围',
          '配置应用的安全域名（如需要）'
        ]
      },
      dingtalk: {
        name: '钉钉',
        icon: '📱',
        description: '通过钉钉应用或机器人推送消息通知',
        fields: [
          {
            key: 'appKey',
            label: '应用Key',
            type: 'input',
            required: true,
            placeholder: '请输入钉钉应用的AppKey',
            help: '可在钉钉开放平台"应用开发"中查看'
          },
          {
            key: 'appSecret',
            label: '应用密钥',
            type: 'password',
            required: true,
            placeholder: '请输入钉钉应用的AppSecret',
            help: '可在钉钉开放平台"应用开发"中查看'
          },
          {
            key: 'robotToken',
            label: '机器人Token',
            type: 'password',
            required: false,
            placeholder: '机器人Webhook Token（可选）',
            help: '如需使用群机器人功能，请配置此项'
          },
          {
            key: 'name',
            label: '集成名称',
            type: 'input',
            required: false,
            placeholder: '自定义集成名称（可选）'
          }
        ],
        setupGuide: [
          '登录钉钉开放平台',
          '创建企业内部应用',
          '获取AppKey和AppSecret',
          '配置应用权限范围',
          '如需使用机器人，创建群机器人并获取Webhook地址'
        ]
      },
      slack: {
        name: 'Slack',
        icon: '⚡',
        description: '通过Slack Bot推送消息到频道或私聊',
        fields: [
          {
            key: 'botToken',
            label: 'Bot Token',
            type: 'password',
            required: true,
            placeholder: 'xoxb-your-bot-token',
            help: '以"xoxb-"开头的Bot User OAuth Token'
          },
          {
            key: 'signingSecret',
            label: '签名密钥',
            type: 'password',
            required: true,
            placeholder: '请输入Signing Secret',
            help: '用于验证请求来源的签名密钥'
          },
          {
            key: 'appId',
            label: '应用ID',
            type: 'input',
            required: false,
            placeholder: '请输入App ID（可选）',
            help: 'Slack应用的唯一标识符'
          },
          {
            key: 'name',
            label: '集成名称',
            type: 'input',
            required: false,
            placeholder: '自定义集成名称（可选）'
          }
        ],
        setupGuide: [
          '访问Slack API网站并创建新应用',
          '配置Bot Token Scopes权限',
          '安装应用到工作区',
          '复制Bot User OAuth Token',
          '记录Signing Secret'
        ]
      }
    };
  }

  /**
   * 获取消息类型模板
   */
  getMessageTypeTemplates(): Record<string, {
    name: string;
    description: string;
    example: MessageConfig;
  }> {
    return {
      text: {
        name: '纯文本',
        description: '发送简单的文本消息',
        example: {
          type: 'text',
          content: '这是一条文本消息'
        }
      },
      markdown: {
        name: 'Markdown',
        description: '支持Markdown格式的富文本消息',
        example: {
          type: 'markdown',
          content: '# 标题\n\n**粗体文本** 和 *斜体文本*\n\n- 列表项1\n- 列表项2'
        }
      },
      card: {
        name: '卡片消息',
        description: '带标题、内容和操作按钮的卡片',
        example: {
          type: 'card',
          title: '工单通知',
          content: '您有一个新的工单需要处理',
          url: 'https://example.com/ticket/123'
        }
      }
    };
  }

  /**
   * 验证接收者格式
   */
  validateRecipients(platform: string, recipients: string[]): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!recipients || recipients.length === 0) {
      errors.push('接收者列表不能为空');
      return { valid: false, errors };
    }

    switch (platform) {
      case 'wechat_work':
        recipients.forEach((recipient, index) => {
          if (!recipient.trim()) {
            errors.push(`第${index + 1}个接收者不能为空`);
          }
          // 企业微信用户ID格式验证
          if (!/^[a-zA-Z0-9_-]+$/.test(recipient)) {
            errors.push(`第${index + 1}个接收者"${recipient}"格式不正确（应为用户ID或部门ID）`);
          }
        });
        break;

      case 'dingtalk':
        recipients.forEach((recipient, index) => {
          if (!recipient.trim()) {
            errors.push(`第${index + 1}个接收者不能为空`);
          }
          // 钉钉用户ID格式验证
          if (!/^[a-zA-Z0-9_-]+$/.test(recipient)) {
            errors.push(`第${index + 1}个接收者"${recipient}"格式不正确（应为用户ID）`);
          }
        });
        break;

      case 'slack':
        recipients.forEach((recipient, index) => {
          if (!recipient.trim()) {
            errors.push(`第${index + 1}个接收者不能为空`);
          }
          // Slack频道名或用户ID格式验证
          if (!/^[#@]?[a-zA-Z0-9_-]+$/.test(recipient)) {
            errors.push(`第${index + 1}个接收者"${recipient}"格式不正确（应为#频道名或@用户名）`);
          }
        });
        break;
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 格式化接收者列表
   */
  formatRecipients(platform: string, recipients: string[]): string[] {
    return recipients.map(recipient => {
      const trimmed = recipient.trim();
      
      switch (platform) {
        case 'slack':
          // 自动添加#或@前缀
          if (!trimmed.startsWith('#') && !trimmed.startsWith('@')) {
            return trimmed.includes('channel') || trimmed.includes('general') ? `#${trimmed}` : `@${trimmed}`;
          }
          return trimmed;
        
        default:
          return trimmed;
      }
    });
  }
}

export const communicationService = new CommunicationService();