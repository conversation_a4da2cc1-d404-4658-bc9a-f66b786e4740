/**
 * 通信集成服务
 * 提供企业微信、钉钉、Slack、飞书等平台的集成管理
 */

import { get, post, put, del } from '../utils/request';
import type { ApiResponse } from '../types/common';
import type { CommunicationConfig } from '../components/integration/CommunicationIntegrationForm';

export interface CommunicationIntegration {
  id: string;
  name: string;
  type: 'wechat_work' | 'dingtalk' | 'slack' | 'feishu';
  status: 'active' | 'inactive' | 'error' | 'testing';
  enabled: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
  lastHealthCheck?: string;
  healthStatus?: 'healthy' | 'warning' | 'critical';
  description?: string;
  config: any;
}

export interface TestConnectionRequest {
  type: string;
  config: any;
}

export interface TestConnectionResponse {
  success: boolean;
  message: string;
  details?: any;
}

class CommunicationIntegrationService {
  
  /**
   * 获取所有通信集成配置
   */
  async getAllIntegrations(): Promise<CommunicationIntegration[]> {
    const response = await get('/api/v1/communication-integrations');
    return response.data || [];
  }

  /**
   * 获取单个通信集成配置
   */
  async getIntegration(id: string): Promise<CommunicationIntegration | null> {
    const response = await get(`/api/v1/communication-integrations/${id}`);
    return response.data || null;
  }

  /**
   * 创建通信集成配置
   */
  async createIntegration(config: CommunicationConfig): Promise<CommunicationIntegration> {
    const response = await post('/api/v1/communication-integrations', config);
    return response.data;
  }

  /**
   * 更新通信集成配置
   */
  async updateIntegration(id: string, config: Partial<CommunicationConfig>): Promise<CommunicationIntegration> {
    const response = await put(`/api/v1/communication-integrations/${id}`, config);
    return response.data;
  }

  /**
   * 根据类型获取通道配置模板
   */
  getChannelConfigTemplate(type: string) {
    const templates = {
      wechat_work: {
        name: '企业微信集成',
        description: '用于工单通知和状态推送',
        fields: ['corpId', 'agentId', 'secret', 'token', 'encodingAESKey'],
        required: ['corpId', 'agentId', 'secret']
      },
      dingtalk: {
        name: '钉钉集成',
        description: '备用通信渠道',
        fields: ['appKey', 'appSecret', 'agentId', 'corpId'],
        required: ['appKey', 'appSecret', 'agentId', 'corpId']
      },
      slack: {
        name: 'Slack集成',
        description: '国际化团队协作',
        fields: ['botToken', 'signingSecret', 'defaultChannel'],
        required: ['botToken', 'signingSecret', 'defaultChannel']
      },
      feishu: {
        name: '飞书集成',
        description: '字节系产品集成',
        fields: ['appId', 'appSecret', 'verificationToken', 'encryptKey'],
        required: ['appId', 'appSecret']
      }
    };

    return templates[type as keyof typeof templates] || templates.wechat_work;
  }

  /**
   * 删除通信集成配置
   */
  async deleteIntegration(id: string): Promise<void> {
    await del(`/api/v1/communication-integrations/${id}`);
  }

  /**
   * 测试连接
   */
  async testConnection(request: TestConnectionRequest): Promise<TestConnectionResponse> {
    const response = await post('/api/v1/communication-integrations/test-connection', request);
    return response.data;
  }

  /**
   * 启用/禁用集成
   */
  async toggleIntegration(id: string, enabled: boolean): Promise<void> {
    await put(`/api/v1/communication-integrations/${id}/toggle`, { enabled });
  }

  /**
   * 健康检查
   */
  async healthCheck(id: string): Promise<{ healthy: boolean; details?: any }> {
    const response = await post(`/api/v1/communication-integrations/${id}/health-check`);
    return response.data;
  }

  /**
   * 发送测试消息
   */
  async sendTestMessage(id: string, message: string): Promise<{ success: boolean; messageId?: string }> {
    const response = await post(`/api/v1/communication-integrations/send-message`, { 
      integrationId: id, 
      message 
    });
    return response.data;
  }

  /**
   * 获取集成指标
   */
  async getIntegrationMetrics(
    integrationId: string, 
    period: string = 'day', 
    limit: number = 30
  ): Promise<any[]> {
    const response = await get(`/api/v1/communication-integrations/${integrationId}/metrics?period=${period}&limit=${limit}`);
    return response.data || [];
  }

  /**
   * 获取集成类型信息
   */
  getIntegrationTypes() {
    return [
      {
        value: 'wechat_work',
        label: '企业微信',
        description: '企业微信应用集成，支持消息推送和用户同步',
        icon: '💬',
        features: ['消息推送', '文件上传', '用户同步', '群组管理']
      },
      {
        value: 'dingtalk',
        label: '钉钉',
        description: '钉钉应用集成，支持智能工作流和消息通知',
        icon: '🔔',
        features: ['消息推送', '文件上传', '用户同步', '@功能']
      },
      {
        value: 'slack',
        label: 'Slack',
        description: 'Slack工作区集成，支持频道消息和文件共享',
        icon: '💻',
        features: ['消息推送', '文件上传', '用户同步', '线程回复']
      },
      {
        value: 'feishu',
        label: '飞书',
        description: '飞书应用集成，支持智能机器人和消息推送',
        icon: '🦅',
        features: ['消息推送', '文件上传', '用户同步', '加密通信']
      }
    ];
  }

  /**
   * 获取平台特定的配置说明
   */
  getPlatformConfigGuide(type: string) {
    const guides = {
      wechat_work: {
        title: '企业微信配置指南',
        steps: [
          '1. 登录企业微信管理后台',
          '2. 创建自建应用',
          '3. 获取企业ID (CorpId)',
          '4. 获取应用ID (AgentId)',
          '5. 获取应用密钥 (Secret)',
          '6. 配置可信IP地址',
          '7. 设置应用可见范围'
        ],
        helpUrl: 'https://work.weixin.qq.com/api/doc/90000/90135/90664'
      },
      dingtalk: {
        title: '钉钉配置指南',
        steps: [
          '1. 登录钉钉开放平台',
          '2. 创建企业内部应用',
          '3. 获取AppKey和AppSecret',
          '4. 获取AgentId',
          '5. 配置应用权限',
          '6. 设置回调域名',
          '7. 配置IP白名单'
        ],
        helpUrl: 'https://open.dingtalk.com/document/'
      },
      slack: {
        title: 'Slack配置指南',
        steps: [
          '1. 登录Slack API管理页面',
          '2. 创建新的Slack App',
          '3. 配置Bot Token Scopes',
          '4. 获取Bot User OAuth Token',
          '5. 获取Signing Secret',
          '6. 配置Event Subscriptions',
          '7. 设置Webhook URL'
        ],
        helpUrl: 'https://api.slack.com/start'
      },
      feishu: {
        title: '飞书配置指南',
        steps: [
          '1. 登录飞书开放平台',
          '2. 创建企业自建应用',
          '3. 获取App ID和App Secret',
          '4. 配置应用权限',
          '5. 设置事件订阅',
          '6. 配置Webhook地址',
          '7. 获取验证Token'
        ],
        helpUrl: 'https://open.feishu.cn/document/'
      }
    };

    return guides[type as keyof typeof guides] || guides.wechat_work;
  }

  /**
   * 验证配置格式
   */
  validateConfig(type: string, config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    switch (type) {
      case 'wechat_work':
        if (!config.corpId) errors.push('企业ID不能为空');
        if (!config.agentId) errors.push('应用ID不能为空');
        if (!config.secret) errors.push('应用密钥不能为空');
        break;
      case 'dingtalk':
        if (!config.appKey) errors.push('应用Key不能为空');
        if (!config.appSecret) errors.push('应用密钥不能为空');
        if (!config.agentId) errors.push('应用ID不能为空');
        if (!config.corpId) errors.push('企业ID不能为空');
        break;
      case 'slack':
        if (!config.botToken) errors.push('Bot Token不能为空');
        if (!config.signingSecret) errors.push('Signing Secret不能为空');
        if (!config.defaultChannel) errors.push('默认频道不能为空');
        break;
      case 'feishu':
        if (!config.appId) errors.push('应用ID不能为空');
        if (!config.appSecret) errors.push('应用密钥不能为空');
        break;
      default:
        errors.push('不支持的集成类型');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 模拟测试连接（实际项目中应该调用后端API）
   */
  async mockTestConnection(request: TestConnectionRequest): Promise<TestConnectionResponse> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // 模拟测试结果
    const success = Math.random() > 0.2; // 80%成功率

    if (success) {
      return {
        success: true,
        message: '连接测试成功',
        details: {
          responseTime: Math.floor(Math.random() * 500) + 100,
          platform: request.type,
          timestamp: new Date().toISOString()
        }
      };
    } else {
      return {
        success: false,
        message: '连接测试失败：认证信息无效或网络连接异常',
        details: {
          errorCode: 'AUTH_FAILED',
          platform: request.type,
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}

export const communicationIntegrationService = new CommunicationIntegrationService();
export default communicationIntegrationService;
