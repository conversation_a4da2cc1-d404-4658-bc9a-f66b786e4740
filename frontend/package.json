{"name": "ops-management-frontend", "version": "1.0.0", "private": true, "description": "运维服务管理系统前端 - React + Rsbuild", "keywords": ["react", "rsbuild", "typescript", "ops-management"], "license": "MIT", "scripts": {"analyze": "BUNDLE_ANALYZE=true rsbuild build", "build": "rsbuild build", "build:ts": "tsc -b tsconfig.build.json", "dev": "rsbuild dev", "lint": "eslint ./ --cache --quiet", "lint:fix": "eslint ./ --cache --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "rsbuild preview", "type-check": "tsc --noEmit", "test": "vitest --run --passWithNoTests"}, "dependencies": {"@douyinfe/semi-icons": "^2.85.0", "@douyinfe/semi-rspack-plugin": "^2.85.0", "@douyinfe/semi-ui": "^2.85.0", "@monaco-editor/react": "^4.7.0", "@tanstack/react-query": "^5.28.4", "@tinymce/tinymce-react": "^6.3.0", "@visactor/vchart": "^2.0.3", "ahooks": "^3.7.8", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "echarts": "^6.0.0", "immer": "^10.0.3", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "query-string": "^8.1.0", "react": "~18.2.0", "react-dom": "~18.2.0", "react-error-boundary": "^4.0.9", "react-pdf": "^10.0.1", "react-router": "^6.22.0", "react-router-dom": "^6.22.0", "zustand": "^4.4.7"}, "devDependencies": {"@rsbuild/core": "~1.1.0", "@rsbuild/plugin-react": "~1.0.3", "@rsbuild/plugin-sass": "^1.3.3", "@rsbuild/plugin-type-check": "~1.0.1", "@types/lodash-es": "^4.17.12", "@types/node": "^18", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "@types/react-pdf": "^7.0.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "@vitest/coverage-v8": "~3.0.5", "autoprefixer": "^10.4.21", "cors": "^2.8.5", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "express": "^5.1.0", "prettier": "^3.2.5", "tailwindcss": "~3.4.0", "typescript": "~5.4.2", "vitest": "~3.0.5"}}